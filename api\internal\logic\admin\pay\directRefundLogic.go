package pay

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-pay/gopay"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DirectRefundLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDirectRefundLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DirectRefundLogic {
	return &DirectRefundLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DirectRefundLogic) DirectRefund(req *types.DirectRefundReq) (resp *types.DirectRefundResp, err error) {
	l.Logger.Infof("DirectRefund start: refund_amount=%.2f, order_nos=%s", req.RefundAmount, req.OrderNos)

	// 解析订单号列表
	orderNos := strings.Split(req.OrderNos, ",")
	var results []types.DirectRefundItem

	// 依次处理每个订单号
	for _, orderNo := range orderNos {
		orderNo = strings.TrimSpace(orderNo)
		if orderNo == "" {
			continue
		}

		result := l.processRefund(orderNo, req.RefundAmount)
		results = append(results, result)
	}

	resp = &types.DirectRefundResp{
		ErrorCode: 0,
		ErrorMsg:  "success",
		Data:      results,
	}

	l.Logger.Infof("DirectRefund completed: processed_count=%d", len(results))
	return resp, nil
}

// processRefund 处理单个订单的退款
func (l *DirectRefundLogic) processRefund(orderNo string, refundAmount float64) types.DirectRefundItem {
	l.Logger.Infof("processRefund start: order_no=%s, amount=%.2f", orderNo, refundAmount)

	// 生成退款单号
	refundNo := fmt.Sprintf("DR%s%06d", time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)

	// 调用微信退款API
	err := l.initiateWechatRefund(orderNo, refundNo, refundAmount)
	if err != nil {
		l.Logger.Errorf("processRefund failed: order_no=%s, error=%v", orderNo, err)
		return types.DirectRefundItem{
			OrderNo:  orderNo,
			Success:  false,
			RefundNo: refundNo,
			Message:  fmt.Sprintf("退款失败: %v", err),
		}
	}

	l.Logger.Infof("processRefund success: order_no=%s, refund_no=%s", orderNo, refundNo)
	return types.DirectRefundItem{
		OrderNo:  orderNo,
		Success:  true,
		RefundNo: refundNo,
		Message:  "退款成功",
	}
}

// initiateWechatRefund 发起微信退款
func (l *DirectRefundLogic) initiateWechatRefund(outTradeNo, refundNo string, amount float64) error {
	l.Logger.Infof("WechatDirectRefund start: order_no=%s, refund_no=%s, amount=%.2f", outTradeNo, refundNo, amount)

	// 构建退款请求参数
	refundAmount := int64(amount * 100) // 转换为分

	// 构建退款请求参数
	refundReq := make(gopay.BodyMap)
	refundReq.Set("out_trade_no", outTradeNo)
	refundReq.Set("out_refund_no", refundNo)
	refundReq.Set("reason", "后台直接退款")
	refundReq.Set("notify_url", l.svcCtx.Config.WePay.NotifyUrl) // 退款回调地址

	// V3版本金额信息
	// 注意：由于订单数据已清理，无法获取原订单金额，这里使用退款金额作为原订单金额
	// 这种情况下，退款金额不能超过原订单金额，需要人工确认
	amountMap := make(gopay.BodyMap)
	amountMap.Set("refund", refundAmount)
	amountMap.Set("total", refundAmount) // 假设退款金额等于原订单金额（全额退款）
	amountMap.Set("currency", "CNY")
	refundReq.Set("amount", amountMap)

	// 根据配置判断是否为服务商模式
	var isPartnerMode bool
	if l.svcCtx.Config.WePay.SubMiniAppId != "" && l.svcCtx.Config.WePay.SubMchId != "" {
		// 服务商模式 - 必须添加 sub_mchid 参数
		refundReq.Set("sub_mchid", l.svcCtx.Config.WePay.SubMchId)
		isPartnerMode = true
	} else {
		// 直连模式
		isPartnerMode = false
	}

	// 记录请求参数
	l.Logger.Infof("WechatDirectRefund request params: %+v", refundReq)

	// 调用微信V3退款API
	refundResp, err := l.svcCtx.WePay.V3Refund(l.ctx, refundReq)
	if err != nil {
		l.Logger.Errorf("WechatDirectRefund API failed: order_no=%s, error=%v", outTradeNo, err)
		return fmt.Errorf("微信退款API调用失败: %v", err)
	}

	// 检查退款结果
	if refundResp.Code != 0 {
		l.Logger.Errorf("WechatDirectRefund failed: order_no=%s, refund_no=%s, code=%d, error=%s",
			outTradeNo, refundNo, refundResp.Code, refundResp.Error)
		return fmt.Errorf("微信退款失败: %s", refundResp.Error)
	}

	// 退款成功 - 记录详细的退款信息
	l.Logger.Infof("WechatDirectRefund success: order_no=%s, refund_no=%s, refund_id=%s, amount=%.2f, mode=%s",
		outTradeNo, refundNo, refundResp.Response.RefundId, amount,
		func() string {
			if isPartnerMode {
				return "partner"
			}
			return "direct"
		}())

	return nil
}
