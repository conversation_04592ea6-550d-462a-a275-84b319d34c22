package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftLogic {
	return &GiftLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftLogic) Gift(req *types.BoxGiftReq) (resp *types.BoxGiftResp, err error) {
	// todo: add your logic here and delete this line

	return
}
