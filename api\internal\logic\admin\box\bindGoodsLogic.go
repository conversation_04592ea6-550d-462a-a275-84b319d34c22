package box

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type BindGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBindGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindGoodsLogic {
	return &BindGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BindGoodsLogic) BindGoods(req *types.BoxBindGoodsReq) error {
	// 查询盲盒是否存在
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("BindGoods BoxModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已删除
	if box.DeleteTime > 0 {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 查询现有的盲盒项目
	existingItems, err := l.svcCtx.BoxItemsModel.FindByBoxId(l.ctx, int64(box.Id))
	if err != nil {
		l.Logger.Error("BindGoods BoxItemsModel.FindByBoxId error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 验证传入的数据数量是否与现有记录匹配
	if len(req.Items) != len(existingItems) {
		return xerr.NewErrMsg("传入的商品项目数量与现有记录不匹配")
	}

	// 创建现有项目ID的映射，用于验证
	existingItemIds := make(map[int64]bool)
	for _, item := range existingItems {
		existingItemIds[int64(item.Id)] = true
	}

	// 验证传入的所有ID都存在
	for _, itemUpdate := range req.Items {
		if !existingItemIds[itemUpdate.Id] {
			return xerr.NewErrMsg(fmt.Sprintf("盲盒项目ID %d 不存在", itemUpdate.Id))
		}
		// 验证商品信息不能为空
		if len(itemUpdate.ItemsInfo) == 0 {
			return xerr.NewErrMsg("商品详情不能为空")
		}
	}

	// 开启事务
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 更新每个盲盒项目
		for _, itemUpdate := range req.Items {
			// 将商品信息转换为JSON
			itemsJson, err := json.Marshal(itemUpdate.ItemsInfo)
			if err != nil {
				l.Logger.Error("BindGoods json.Marshal error: %v", err)
				return xerr.NewErrCode(xerr.ServerCommonError)
			}

			// 使用原生SQL更新vh_box_items
			updateQuery := `UPDATE vh_box_items SET type = ?, items_info = ? WHERE id = ?`
			_, err = tx.ExecContext(ctx, updateQuery, itemUpdate.Type, string(itemsJson), itemUpdate.Id)
			if err != nil {
				l.Logger.Error("BindGoods update vh_box_items error: %v", err)
				return xerr.NewErrCode(xerr.DbError)
			}
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
