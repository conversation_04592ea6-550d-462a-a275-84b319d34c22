package goods

import (
	"context"
	"strconv"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminGoodsListReq) (resp *types.AdminGoodsListResp, err error) {
	var goods []*model.VhGoods

	// 构建查询条件
	builder := l.svcCtx.GoodsModel.RowBuilder().
		Where(squirrel.Eq{"delete_time": 0}). // 软删除条件
		OrderBy("sort DESC, id DESC")

	// 商品名称筛选
	if req.Title != "" {
		builder = builder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
	}

	// 商品类型筛选
	if req.Type != 0 {
		builder = builder.Where(squirrel.Eq{"type": req.Type})
	}

	// 上架状态筛选
	if req.OnsaleStatus != 0 {
		builder = builder.Where(squirrel.Eq{"onsale_status": req.OnsaleStatus})
	}

	// 商品简码筛选 - 在 items_info JSON 字段中搜索 short_code
	if req.ShortCode != "" {
		builder = builder.Where(squirrel.Like{"items_info": "%" + req.ShortCode + "%"})
	}

	// 时间范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.EndDate)
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 包含结束日期的整天
				builder = builder.Where(squirrel.GtOrEq{"created_time": startTime}).
					Where(squirrel.LtOrEq{"created_time": endTime})
			}
		}
	}

	// 分页
	offset := (req.Page - 1) * req.Limit
	builder = builder.Limit(uint64(req.Limit)).Offset(uint64(offset))

	// 查询数据
	err = l.svcCtx.GoodsModel.FindRows(l.ctx, builder, &goods)
	if err != nil {
		l.Logger.Error("AdminList GoodsModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From(l.svcCtx.GoodsModel.TableName()).
		Where(squirrel.Eq{"delete_time": 0})

	if req.Title != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
	}
	if req.Type != 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"type": req.Type})
	}
	if req.OnsaleStatus != 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"onsale_status": req.OnsaleStatus})
	}
	if req.ShortCode != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"items_info": "%" + req.ShortCode + "%"})
	}
	if req.StartDate != "" && req.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.EndDate)
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second)
				countBuilder = countBuilder.Where(squirrel.GtOrEq{"created_time": startTime}).
					Where(squirrel.LtOrEq{"created_time": endTime})
			}
		}
	}

	total, err := l.svcCtx.GoodsModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminList GoodsModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.AdminGoodsListResp{
		List:  make([]types.GoodsInfo, 0),
		Total: total,
	}

	for _, item := range goods {
		goodsInfo := types.GoodsInfo{
			Id:               int64(item.Id),
			Title:            item.Title,
			Brief:            item.Brief,
			Type:             item.Type,
			ItemsInfo:        item.ItemsInfo.String,
			Price:            item.Price,
			Inventory:        int64(item.Inventory),
			ErpAmount:        item.ErpAmount,
			CashbackAmount:   item.CashbackAmount,
			DeductibleAmount: item.DeductibleAmount,
			ProductImg:       l.processImages(item.ProductImg),
			AvatarImage:      l.processImage(item.AvatarImage),
			Detail:           item.Detail.String,
			OnsaleStatus:     item.OnsaleStatus,
			WarehouseCode:    item.WarehouseCode,
			OnsaleTime:       item.OnsaleTime,
			SoldOutTime:      item.SoldOutTime,
			Purchased:        int64(item.Purchased),
			SalesUserNum:     int64(item.SalesUserNum),
			Pv:               int64(item.Pv),
			Sort:             item.Sort,
			VhUid:            item.VhUid,
			VhVosName:        item.VhVosName.String,
			CreatedTime:      function.FormatTime(item.CreatedTime),
			UpdateTime:       function.FormatTime(item.UpdateTime),
		}

		// 查询商品标签
		labels, err := l.svcCtx.GoodsLabelModel.FindByPeriodId(l.ctx, int64(item.Id))
		if err == nil {
			for _, label := range labels {
				goodsInfo.Labels = append(goodsInfo.Labels, types.GoodsLabelInfo{
					LabelId: label.LabelId.Int64,
					Name:    label.Name,
				})
			}
		}

		// 解析商品产品信息
		goodsInfo.Products = l.parseItemsInfo(item.ItemsInfo.String)

		resp.List = append(resp.List, goodsInfo)
	}

	return
}

// processImage 处理单个图片URL
func (l *AdminListLogic) processImage(image string) string {
	if image == "" {
		return ""
	}
	if strings.HasPrefix(image, "http") {
		return image
	}
	return l.svcCtx.Config.ITEM.ALIURL + image
}

// processImages 处理多个图片URL（逗号分隔）
func (l *AdminListLogic) processImages(images string) string {
	if images == "" {
		return ""
	}

	imageList := strings.Split(images, ",")
	var processedImages []string

	for _, img := range imageList {
		img = strings.TrimSpace(img)
		if img != "" {
			if strings.HasPrefix(img, "http") {
				processedImages = append(processedImages, img)
			} else {
				processedImages = append(processedImages, l.svcCtx.Config.ITEM.ALIURL+img)
			}
		}
	}

	return strings.Join(processedImages, ",")
}

// parseItemsInfo 解析商品简码信息
// 格式: "简码1*数量1+简码2*数量2" 或 "简码1+简码2"
func (l *AdminListLogic) parseItemsInfo(itemsInfo string) []types.ProductInfo {
	var products []types.ProductInfo

	if itemsInfo == "" {
		return products
	}

	// 按加号分割
	items := strings.Split(itemsInfo, "+")

	for _, item := range items {
		item = strings.TrimSpace(item)
		if item == "" {
			continue
		}

		// 检查是否包含数量信息（*号分隔）
		if strings.Contains(item, "*") {
			parts := strings.Split(item, "*")
			if len(parts) == 2 {
				code := strings.TrimSpace(parts[0])
				quantityStr := strings.TrimSpace(parts[1])

				quantity, err := strconv.ParseInt(quantityStr, 10, 64)
				if err != nil {
					// 解析失败，默认数量为1
					quantity = 1
				}

				if code != "" {
					products = append(products, types.ProductInfo{
						Code:     code,
						Quantity: quantity,
					})
				}
			}
		} else {
			// 没有数量信息，默认为1
			if item != "" {
				products = append(products, types.ProductInfo{
					Code:     item,
					Quantity: 1,
				})
			}
		}
	}

	return products
}
