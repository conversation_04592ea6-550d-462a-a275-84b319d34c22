package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftAcceptLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftAcceptLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftAcceptLogic {
	return &GiftAcceptLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftAcceptLogic) GiftAccept(req *types.BoxGiftAcceptReq) (resp *types.BoxGiftAcceptResp, err error) {
	// todo: add your logic here and delete this line

	return
}
