package lotteryActivity

import (
	"context"
	"engine/api/internal/logic"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.LotteryActivityUpdateReq) error {
	// 检查记录是否存在
	activity, err := l.svcCtx.LotteryActivityModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if errors.Is(err, model.ErrNotFound) {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.<PERSON>("UpdateLogic.LotteryActivityModel.FindOne err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if req.Status == 1 {
		if activity.Status != 1 {
			//查询是否有已开启的活动
			err = logic.HasActiveActivity(l.ctx, l.svcCtx)
			if err != nil {
				return err
			}
		}
	}

	// 解析开始时间
	startTime, err := common.ParseTime(req.StartTime)
	if err != nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "解析开始时间失败:"+err.Error())
	}

	// 更新抽奖活动数据
	activity.Title = req.Title
	activity.Describe = req.Describe
	activity.Status = req.Status
	activity.StartTime = startTime
	activity.GoodsId = req.GoodsId
	activity.GoodsTitle = req.GoodsTitle
	activity.GoodsImg = req.GoodsImg
	activity.Total = req.Total
	activity.WinnerCount = req.WinnerCount

	// 更新数据库
	err = l.svcCtx.LotteryActivityModel.Update(l.ctx, activity)
	if err != nil {
		l.Errorf("UpdateLogic.LotteryActivityModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
