package lotteryActivity

import (
	"context"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateStatusLogic {
	return &UpdateStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateStatusLogic) UpdateStatus(req *types.LotteryActivityStatusUpdateReq) error {
	// 检查记录是否存在
	activity, err := l.svcCtx.LotteryActivityModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if errors.Is(err, model.ErrNotFound) {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.<PERSON>("UpdateStatusLogic.LotteryActivityModel.FindOne err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if req.Status == 1 {
		//查询是否有已开启的活动
		err = logic.HasActiveActivity(l.ctx, l.svcCtx)
		if err != nil {
			return err
		}
	}

	// 只更新状态字段
	activity.Status = req.Status

	// 更新数据库
	err = l.svcCtx.LotteryActivityModel.Update(l.ctx, activity)
	if err != nil {
		l.Errorf("UpdateStatusLogic.LotteryActivityModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
