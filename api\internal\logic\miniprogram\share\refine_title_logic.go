package share

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RefineTitleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRefineTitleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefineTitleLogic {
	return &RefineTitleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefineTitleLogic) RefineTitle(req *types.RefineTitleReq) (resp *types.RefineTitleResp, err error) {
	// todo: add your logic here and delete this line

	return
}
