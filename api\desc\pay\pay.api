syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    // 直接退款请求
    DirectRefundReq {
        RefundAmount float64 `json:"refund_amount" validate:"required,gt=0" v:"退款金额"`
        OrderNos     string  `json:"order_nos" validate:"required" v:"商家订单号(多个用逗号分割)"`
    }

    // 直接退款响应
    DirectRefundResp {
        ErrorCode int64                `json:"error_code"`
        ErrorMsg  string              `json:"error_msg"`
        Data      []DirectRefundItem  `json:"data"`
    }

    // 直接退款结果项
    DirectRefundItem {
        OrderNo     string `json:"order_no"`     // 订单号
        Success     bool   `json:"success"`      // 是否成功
        RefundNo    string `json:"refund_no"`    // 退款单号
        Message     string `json:"message"`      // 结果消息
    }
)

@server(
    middleware: Global
    group: pay
    prefix: /mulandoGreateDestiny/v1/pay
    timeout: 3s
)

service mulandoGreateDestiny {
    //微信回调
    @handler WechatCallback
    post /wechat_callback
}

// 后台支付管理接口
@server(
    middleware: Global,Admin
    group: admin/pay
    prefix: /mulandoGreateDestiny/v1/admin/pay
    timeout: 30s
)

service mulandoGreateDestiny {
    // 直接退款接口
    @handler DirectRefund
    post /direct_refund (DirectRefundReq) returns (DirectRefundResp)
}