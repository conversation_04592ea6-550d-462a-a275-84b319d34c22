package box

import (
	"context"
	"strings"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftDetailLogic {
	return &GiftDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftDetailLogic) GiftDetail(req *types.BoxGiftDetailReq) (resp *types.BoxGiftDetailResp, err error) {
	// 查询转赠分享记录
	giftShare, err := l.svcCtx.BoxGiftShareModel.FindOne(l.ctx, req.ShareId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("转赠记录不存在")
		}
		l.Logger.Errorf("查询转赠记录失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取转赠人信息
	senderUser, err := l.svcCtx.UserModel.FindOne(l.ctx, giftShare.SenderUid)
	if err != nil {
		l.Logger.Errorf("查询转赠人信息失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 脱敏处理转赠人姓名
	senderName := l.maskUserName(senderUser.Nickname)

	return &types.BoxGiftDetailResp{
		ShareId:        giftShare.Id,
		BoxTitle:       giftShare.BoxTitle,
		BoxAvatarImage: l.normalizeImagePath(giftShare.BoxAvatarImage),
		PeriodLabel:    giftShare.PeriodLabel,
		ExpireTime:     giftShare.ExpireTime.Format("2006-01-02 15:04:05"),
		Status:         giftShare.Status,
		SenderName:     senderName,
	}, nil
}

// maskUserName 脱敏处理用户姓名
func (l *GiftDetailLogic) maskUserName(name string) string {
	if len(name) == 0 {
		return "匿名用户"
	}
	if len(name) == 1 {
		return name
	}
	if len(name) == 2 {
		return string([]rune(name)[0]) + "*"
	}
	runes := []rune(name)
	result := string(runes[0])
	for i := 1; i < len(runes)-1; i++ {
		result += "*"
	}
	result += string(runes[len(runes)-1])
	return result
}

// normalizeImagePath 处理图片路径，添加ALIURL前缀
func (l *GiftDetailLogic) normalizeImagePath(imagePath string) string {
	if imagePath == "" {
		return ""
	}
	// 如果已经是完整URL，直接返回
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		return imagePath
	}
	// 添加ALIURL前缀
	return l.svcCtx.Config.ITEM.ALIURL + imagePath
}
