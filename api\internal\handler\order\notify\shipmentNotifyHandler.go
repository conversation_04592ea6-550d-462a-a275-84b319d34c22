package notify

import (
	"net/http"

	"engine/api/internal/logic/order/notify"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ShipmentNotifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ShipmentNotifyReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := notify.NewShipmentNotifyLogic(r.Context(), svcCtx)
		resp, err := l.ShipmentNotify(&req)
		result.HttpResult(r, w, resp, err)
	}
}
