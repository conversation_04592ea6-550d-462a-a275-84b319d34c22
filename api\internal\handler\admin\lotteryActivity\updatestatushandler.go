package lotteryActivity

import (
	"engine/api/internal/logic/admin/lotteryActivity"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func UpdateStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LotteryActivityStatusUpdateReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := lotteryActivity.NewUpdateStatusLogic(r.Context(), svcCtx)
		err := l.UpdateStatus(&req)
		result.HttpResult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
