syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    GetUserWithdrawalAccountResp {
        WithdrawalAccount
    }
    WithdrawalAccount {
        AliAccount string `json:"ali_account" validate:"required" v:"支付宝账号"`
        AliName string `json:"ali_name" validate:"required" v:"姓名"`
    }
    WithdrawalAccountUpdateReq {
        WithdrawalAccount
    }
    WithdrawalCreateReq {
        Amount float64 `json:"amount" validate:"min=100" v:"提现金额"`
    }

    WithdrawalListReq {
        Paging
        AliName string `form:"ali_name,optional"`
        AliAccount string `form:"ali_account,optional"`
        Telephone string `form:"telephone,optional"`
        Status int64 `form:"status,optional"`
        StartTime string `form:"start_time,optional"`
        EndTime string `form:"end_time,optional"`
    }
    WithdrawalListResp {
        List []WithdrawalListInfo `json:"list"`
        Total int64 `json:"total"`
    }
    WithdrawalListInfo {
        Id uint64 `json:"id"`
        Uid uint64 `json:"uid"`
        AliName string `json:"ali_name"`
        AliAccount string `json:"ali_account"`
        Telephone string `json:"telephone"`
        Amount float64 `json:"amount"`
        Status uint64 `json:"status"`
        BackNote string `json:"back_note"`
        CreateTime string `json:"create_time"`
        HandleTime string `json:"handle_time"`
    }

    WithdrawalReviewReq {
        IdJU
        Status int64 `json:"status,options=[2,4]" validate:"required" v:"状态"`
        BackNote string `json:"back_note,optional" v:"驳回原因"`
    }
)

// 后台
@server(
    middleware: Global,Admin
    group: admin/balance
    prefix: /mulandoGreateDestiny/v1/admin/balance
    timeout: 3s
)

service mulandoGreateDestiny {
    // 提现列表
    @handler WithdrawalList
    get /withdrawalList (WithdrawalListReq) returns (WithdrawalListResp)

    // 提现审核
    @handler WithdrawalReview
    post /withdrawalReview (WithdrawalReviewReq)
}

// 小程序
@server(
    middleware: Global,Auth
    group: miniprogram/balance
    prefix: /mulandoGreateDestiny/v1/miniprogram/balance
    timeout: 3s
)

service mulandoGreateDestiny {
    //提现资料获取
    @handler GetUserWithdrawalAccount
    get /getUserWithdrawalAccount returns (GetUserWithdrawalAccountResp)

    //提现资料修改
    @handler WithdrawalAccountUpdate
    post /withdrawalAccountUpdate (WithdrawalAccountUpdateReq)

    //申请提现
    @handler WithdrawalCreate
    post /withdrawalCreate (WithdrawalCreateReq)
}
