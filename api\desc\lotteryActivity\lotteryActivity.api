syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    // 抽奖活动信息
    LotteryActivityInfo {
        Id uint64 `json:"id"`
        Title string `json:"title"`
        Describe string `json:"describe"`
        Status uint64 `json:"status"`
        StartTime string `json:"start_time"`
        GoodsId uint64 `json:"goods_id"`
        GoodsTitle string `json:"goods_title"`
        GoodsImg string `json:"goods_img"`
        Total uint64 `json:"total"`
        WinnerCount uint64 `json:"winner_count"`
        ParticipantCount uint64 `json:"participant_count"`
        CreateTime string `json:"create_time"`
    }

        // 后台抽奖活动列表请求
    AdminLotteryActivityListReq {
        Paging
        Title string `form:"title,optional"`  // 活动名称搜索
        Status int64 `form:"status,optional"` // 状态筛选
    }

        // 后台抽奖活动列表响应
    AdminLotteryActivityListResp {
        List []LotteryActivityInfo `json:"list"`
        Total int64 `json:"total"`
    }

        // 抽奖活动创建请求
    LotteryActivityCreateReq {
        Title string `json:"title" validate:"required,max=50" v:"活动名称"`
        Describe string `json:"describe" validate:"required,max=200" v:"活动描述"`
        Status uint64 `json:"status,default=0" validate:"min=0,max=1" v:"活动状态"`
        StartTime string `json:"start_time" validate:"required" v:"开始时间"`
        GoodsId uint64 `json:"goods_id" validate:"required" v:"商品ID"`
        GoodsTitle string `json:"goods_title" validate:"required,max=50" v:"商品标题"`
        GoodsImg string `json:"goods_img" validate:"required" v:"商品图片"`
        Total uint64 `json:"total" validate:"required,min=1" v:"开奖人数门槛"`
        WinnerCount uint64 `json:"winner_count" validate:"required,min=1" v:"中奖人数配额"`
    }

        // 抽奖活动更新请求
    LotteryActivityUpdateReq {
        IdJU
        LotteryActivityCreateReq
    }

        // 抽奖活动状态更新请求
    LotteryActivityStatusUpdateReq {
        IdJU
        Status uint64 `json:"status" validate:"min=0,max=1" v:"活动状态"`
    }

        // 获取已启用抽奖活动响应
    ActiveLotteryActivityResp {
        LotteryActivityInfo
    }

        //参与活动
    ActiveLotteryActivityParticipantReq {
        IdJU
        AddressId int64 `json:"address_id" validate:"required" v:"地址ID"`
    }

    ActiveLotteryActivityParticipantAddressReq {
        IdFU
    }
    ActiveLotteryActivityParticipantAddressResp {
        UserAddressInfo
    }
    ActiveLotteryActivityAddressUpdateReq {
        ActiveLotteryActivityParticipantReq
    }

    ActiveLotteryActivityParticipantListReq {
        Paging
        IdFU
    }
    ActiveLotteryActivityParticipantListResp {
        List []ParticipantUserInfo `json:"list"`
        Total int64 `json:"total"`
    }
    ParticipantUserInfo {
        Nickname string `json:"nickname"`
        JoinTime string `json:"join_time"`
    }

    ActiveLotteryActivityisParticipantReq {
        IdFU
    }
    ActiveLotteryActivityisParticipantResp {
        IsParticipant bool `json:"is_participant"`
        IsWinner int64 `json:"is_winner"`
    }
)

// 后台抽奖活动管理接口
@server(
    middleware: Global,Admin
    group: admin/lotteryActivity
    prefix: /mulandoGreateDestiny/v1/admin/lotteryActivity
    timeout: 3s
)

service mulandoGreateDestiny {
    // 抽奖活动列表
    @handler List
    get /list (AdminLotteryActivityListReq) returns (AdminLotteryActivityListResp)

    // 创建抽奖活动
    @handler Create
    post /create (LotteryActivityCreateReq)

    // 更新抽奖活动
    @handler Update
    post /update (LotteryActivityUpdateReq)

    // 更新抽奖活动状态
    @handler UpdateStatus
    post /updateStatus (LotteryActivityStatusUpdateReq)
}

@server(
    middleware: Global,ExistAuth
    group: miniprogram/lotteryActivity
    prefix: /mulandoGreateDestiny/v1/miniprogram/lotteryActivity
    timeout: 3s
)

service mulandoGreateDestiny {
    // 获取已启用的抽奖活动
    @handler GetActive
    get /active returns (ActiveLotteryActivityResp)

    //参与人列表
    @handler ParticipantList
    get /participantList (ActiveLotteryActivityParticipantListReq) returns (ActiveLotteryActivityParticipantListResp)
}

// 小程序抽奖活动接口,必须登录
@server(
    middleware: Global,Auth
    group: miniprogram/lotteryActivity
    prefix: /mulandoGreateDestiny/v1/miniprogram/lotteryActivity
    timeout: 3s
)

service mulandoGreateDestiny {
    // 参与抽奖活动
    @handler Participant
    post /participant (ActiveLotteryActivityParticipantReq)

    //我的参与地址
    @handler ParticipantAddress
    get /participantAddress (ActiveLotteryActivityParticipantAddressReq) returns (ActiveLotteryActivityParticipantAddressResp)

    //是否参与过抽奖
    @handler IsParticipant
    get /isParticipant (ActiveLotteryActivityisParticipantReq) returns (ActiveLotteryActivityisParticipantResp)

    // 修改抽奖地址
    @handler AddressUpdate
    post /addressUpdate (ActiveLotteryActivityAddressUpdateReq)
}
