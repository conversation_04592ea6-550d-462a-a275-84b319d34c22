package partner

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type ApplyPartnerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewApplyPartnerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApplyPartnerLogic {
	return &ApplyPartnerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApplyPartnerLogic) ApplyPartner(req *types.ApplyPartnerReq) (resp *types.ApplyPartnerResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	// 1. 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("用户不存在")
		}
		l.Logger.Errorf("ApplyPartner UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 2. 检查用户是否为会员
	if user.Type != 2 {
		return nil, xerr.NewErrMsg("您不是会员，无法申请合伙人")
	}

	// 3. 查询合伙人等级信息
	userLevel, err := l.svcCtx.UserLevelModel.FindOneByLevel(l.ctx, req.Level)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("合伙人等级不存在")
		}
		l.Logger.Errorf("ApplyPartner UserLevelModel.FindOneByLevel error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 4. 根据合伙人状态处理
	switch user.LevelStatus {
	case 1: // 无合伙人状态，首次申请，直接通过
		return l.processFirstTimeApply(user, userLevel, req.Level)
	case 2: // 有效合伙人，无需重复申请
		return nil, xerr.NewErrMsg("您已经是合伙人，无需重复申请")
	case 3: // 无效合伙人，需要验证任务完成情况
		return l.processReapply(user, userLevel, req.Level)
	default:
		return nil, xerr.NewErrMsg("用户状态异常")
	}
}

// processFirstTimeApply 处理首次申请合伙人
func (l *ApplyPartnerLogic) processFirstTimeApply(user *model.VhUser, userLevel *model.UserLevel, level int64) (*types.ApplyPartnerResp, error) {
	now := time.Now()
	startTime := now.Unix()
	endTime := now.AddDate(0, 0, int(userLevel.Duration)).Unix()
	// 设置结束时间为23:59:59
	endTimeFormatted := time.Unix(endTime, 0).Format("2006-01-02") + " 23:59:59"
	endTimeParsed, _ := time.ParseInLocation("2006-01-02 15:04:05", endTimeFormatted, time.Local)
	endTime = endTimeParsed.Unix()

	// 开始事务处理
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		return l.processPartnerTransaction(ctx, tx, user, level, startTime, endTime, 1, "首次申请合伙人")
	})

	if err != nil {
		l.Logger.Errorf("processFirstTimeApply transaction error: %v", err)
		return nil, xerr.NewErrMsg("申请失败，请稍后重试")
	}

	// 清除用户缓存
	err = l.svcCtx.UserModel.ClearCache(l.ctx, user)
	if err != nil {
		l.Logger.Errorf("processFirstTimeApply clear cache error: %v", err)
		// 不返回错误，因为业务已经成功，只是缓存清除失败
	}

	return &types.ApplyPartnerResp{
		Message: "签约成功，您已成为合伙人",
		Level:   level,
		EndTime: endTimeFormatted,
	}, nil
}

// processReapply 处理重新申请合伙人（需要验证任务）
func (l *ApplyPartnerLogic) processReapply(user *model.VhUser, userLevel *model.UserLevel, level int64) (*types.ApplyPartnerResp, error) {
	// 1. 查询该等级的所有任务
	tasks, err := l.getUserLevelTasks(level)
	if err != nil {
		l.Logger.Errorf("processReapply getUserLevelTasks error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 2. 验证任务完成情况
	taskDetails, allCompleted, err := l.validateTasks(user, tasks)
	if err != nil {
		l.Logger.Errorf("processReapply validateTasks error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if !allCompleted {
		// 记录失败日志
		l.logPartnerAction(user.Id, 1, user.Level, level, user.LevelStatus, user.LevelStatus,
			user.LevelStartTime, user.LevelEndTime, taskDetails, 2, "任务未完成")
		return nil, xerr.NewErrMsg("任务未完成，无法申请合伙人")
	}

	// 3. 任务完成，处理签约
	now := time.Now()
	startTime := now.Unix()
	endTime := now.AddDate(0, 0, int(userLevel.Duration)).Unix()
	// 设置结束时间为23:59:59
	endTimeFormatted := time.Unix(endTime, 0).Format("2006-01-02") + " 23:59:59"
	endTimeParsed, _ := time.ParseInLocation("2006-01-02 15:04:05", endTimeFormatted, time.Local)
	endTime = endTimeParsed.Unix()

	// 开始事务处理
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		return l.processPartnerTransaction(ctx, tx, user, level, startTime, endTime, 1, "重新申请合伙人")
	})

	if err != nil {
		l.Logger.Errorf("processReapply transaction error: %v", err)
		return nil, xerr.NewErrMsg("申请失败，请稍后重试")
	}

	// 清除用户缓存
	err = l.svcCtx.UserModel.ClearCache(l.ctx, user)
	if err != nil {
		l.Logger.Errorf("processReapply clear cache error: %v", err)
		// 不返回错误，因为业务已经成功，只是缓存清除失败
	}

	return &types.ApplyPartnerResp{
		Message: "签约成功，您已成为合伙人",
		Level:   level,
		EndTime: endTimeFormatted,
	}, nil
}

// getUserLevelTasks 获取指定等级的所有任务
func (l *ApplyPartnerLogic) getUserLevelTasks(level int64) ([]*model.UserLevelTask, error) {
	builder := l.svcCtx.UserLevelTaskModel.RowBuilder().Where(squirrel.Eq{"level": level})
	var tasks []*model.UserLevelTask
	err := l.svcCtx.UserLevelTaskModel.FindRows(l.ctx, builder, &tasks)
	return tasks, err
}

// validateTasks 验证任务完成情况
func (l *ApplyPartnerLogic) validateTasks(user *model.VhUser, tasks []*model.UserLevelTask) (string, bool, error) {
	type TaskResult struct {
		TaskId    int64  `json:"task_id"`
		TaskName  string `json:"task_name"`
		TaskType  int64  `json:"task_type"`
		Required  int64  `json:"required"`
		Completed int64  `json:"completed"`
		Status    string `json:"status"`
	}

	var results []TaskResult
	allCompleted := true

	for _, task := range tasks {
		var completed int64
		var err error

		switch task.Type {
		case 1: // 分享任务
			completed, err = l.getShareCount(user.Id, user.LevelStartTime)
			if err != nil {
				return "", false, err
			}
		case 2: // 考试任务
			completed, err = l.getBadgeCount(user.Id)
			if err != nil {
				return "", false, err
			}
		default:
			l.Logger.Infof("Unknown task type: %d", task.Type)
			completed = 0
		}

		status := "completed"
		if completed < task.Num {
			status = "incomplete"
			allCompleted = false
		}

		results = append(results, TaskResult{
			TaskId:    task.Id,
			TaskName:  task.Name,
			TaskType:  task.Type,
			Required:  task.Num,
			Completed: completed,
			Status:    status,
		})
	}

	taskDetailsJson, _ := json.Marshal(results)
	return string(taskDetailsJson), allCompleted, nil
}

// getShareCount 获取用户在指定时间段内的分享数量
func (l *ApplyPartnerLogic) getShareCount(uid int64, startTime int64) (int64, error) {
	startTimeObj := time.Unix(startTime, 0)
	builder := squirrel.Select("COUNT(*)").
		From(l.svcCtx.ShareModel.TableName()).
		Where(squirrel.Eq{"uid": uid}).
		Where(squirrel.GtOrEq{"create_time": startTimeObj})

	count, err := l.svcCtx.ShareModel.FindCount(l.ctx, builder)
	return count, err
}

// processPartnerTransaction 处理合伙人签约事务
func (l *ApplyPartnerLogic) processPartnerTransaction(ctx context.Context, tx *sql.Tx, user *model.VhUser,
	level int64, startTime, endTime int64, actionType int64, reason string) error {

	// 1. 更新用户信息
	updateUserQuery := `UPDATE vh_user SET type = 3, level_status = 2, level = ?, level_start_time = ?, level_end_time = ? WHERE id = ?`
	_, err := tx.ExecContext(ctx, updateUserQuery, level, startTime, endTime, user.Id)
	if err != nil {
		l.Logger.Errorf("Update user partner info error: %v", err)
		return err
	}

	// 清除用户缓存
	clearErr := l.svcCtx.UserModel.ClearCache(ctx, user)
	if clearErr != nil {
		l.Logger.Errorf("Clear user cache error: %v", clearErr)
	}

	// 2. 记录日志
	partnerLog := &model.VhPartnerLog{
		Uid:         user.Id,
		ActionType:  actionType,
		OldLevel:    user.Level,
		NewLevel:    level,
		OldStatus:   user.LevelStatus,
		NewStatus:   2,
		StartTime:   startTime,
		EndTime:     endTime,
		TaskDetails: "",
		Result:      1,
		Reason:      reason,
	}

	_, err = l.svcCtx.PartnerLogModel.InsertTx(ctx, tx, partnerLog)
	if err != nil {
		l.Logger.Errorf("Insert partner log error: %v", err)
		return err
	}

	l.Logger.Infof("User partner status updated: uid=%d, level=%d, start_time=%d, end_time=%d",
		user.Id, level, startTime, endTime)
	return nil
}

// logPartnerAction 记录合伙人操作日志
func (l *ApplyPartnerLogic) logPartnerAction(uid, actionType, oldLevel, newLevel, oldStatus, newStatus,
	startTime, endTime int64, taskDetails string, result int64, reason string) {

	partnerLog := &model.VhPartnerLog{
		Uid:         uid,
		ActionType:  actionType,
		OldLevel:    oldLevel,
		NewLevel:    newLevel,
		OldStatus:   oldStatus,
		NewStatus:   newStatus,
		StartTime:   startTime,
		EndTime:     endTime,
		TaskDetails: taskDetails,
		Result:      result,
		Reason:      reason,
	}

	_, err := l.svcCtx.PartnerLogModel.Insert(l.ctx, partnerLog)
	if err != nil {
		l.Logger.Errorf("Insert partner log error: %v", err)
	}
}

// getBadgeCount 获取用户在vh_academy数据库中的证书数量
func (l *ApplyPartnerLogic) getBadgeCount(uid int64) (int64, error) {
	query := "SELECT COUNT(*) FROM vh_user_badge WHERE uid = ?"
	var count int64
	err := l.svcCtx.AcademyDB.QueryRowCtx(l.ctx, &count, query, uid)
	if err != nil {
		l.Logger.Errorf("getBadgeCount query error: %v", err)
		return 0, err
	}
	return count, nil
}
