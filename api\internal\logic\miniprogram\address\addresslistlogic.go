package address

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressListLogic {
	return &AddressListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressListLogic) AddressList(req *types.AddressListReq) (resp *types.AddressListResp, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 构建查询条件 - 只能查询自己的地址
	builder := l.svcCtx.UserAddressModel.RowBuilder().
		Where(squirrel.Eq{"uid": uid}).
		OrderBy("is_default DESC, id DESC") // 默认地址排在前面

	// 获取总数
	countBuilder := model.CountBuilder("id", l.svcCtx.UserAddressModel.TableName()).
		Where(squirrel.Eq{"uid": uid})

	total, err := l.svcCtx.UserAddressModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AddressList UserAddressModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 分页查询
	builder = builder.Limit(uint64(req.Limit)).
		Offset(model.GetOffset(req.Page, req.Limit))

	var addresses []*model.UserAddress
	err = l.svcCtx.UserAddressModel.FindRows(l.ctx, builder, &addresses)
	if err != nil {
		l.Logger.Error("AddressList UserAddressModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据 - 临时兼容性修复
	list := make([]types.UserAddressInfo, 0, len(addresses))
	for _, address := range addresses {
		addressInfo := types.UserAddressInfo{
			Id:             address.Id,
			Uid:            address.Uid,
			ProvinceId:     address.ProvinceId,
			CityId:         address.CityId,
			TownId:         address.TownId,
			Address:        address.Address,
			IsDefault:      address.IsDefault,
			Label:          address.Label,
			Code:           address.Code,
			Consignee:      address.Consignee,
			ConsigneePhone: address.ConsigneePhone,
			ProvinceName:   address.ProvinceName,
			CityName:       address.CityName,
			TownName:       address.TownName,
			CreatedTime:    common.TimeToString(address.CreatedTime),
			UpdateTime:     common.TimeToString(address.UpdateTime),
		}
		list = append(list, addressInfo)
	}

	return &types.AddressListResp{
		List:  list,
		Total: total,
	}, nil
}
