package decrypt

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShortUrlRedirectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShortUrlRedirectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShortUrlRedirectLogic {
	return &ShortUrlRedirectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShortUrlRedirectLogic) ShortUrlRedirect(req *types.ShortUrlRedirectReq) (resp *types.ShortUrlRedirectResp, err error) {
	l.Logger.Infof("开始处理短链接跳转: %s", req.Url)

	// 验证URL格式
	if !l.isValidURL(req.Url) {
		return nil, xerr.NewErrMsg("无效的URL格式")
	}

	// 跟踪重定向获取最终URL
	finalUrl, redirectCount, err := l.followRedirects(req.Url)
	if err != nil {
		l.Logger.Errorf("跟踪重定向失败 - 原始URL: %s, 错误: %v", req.Url, err)
		return nil, xerr.NewErrMsg("获取最终链接失败")
	}

	l.Logger.Infof("重定向跟踪完成 - 原始URL: %s, 最终URL: %s, 重定向次数: %d", req.Url, finalUrl, redirectCount)

	return &types.ShortUrlRedirectResp{
		FinalUrl:      finalUrl,
		RedirectCount: redirectCount,
	}, nil
}

// isValidURL 验证URL格式是否有效
func (l *ShortUrlRedirectLogic) isValidURL(rawURL string) bool {
	// 如果不是以http://或https://开头，尝试添加https://
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "https://" + rawURL
	}

	_, err := url.Parse(rawURL)
	return err == nil
}

// followRedirects 跟踪所有重定向直到获取最终URL
func (l *ShortUrlRedirectLogic) followRedirects(startURL string) (string, int, error) {
	// 如果不是以http://或https://开头，添加https://
	if !strings.HasPrefix(startURL, "http://") && !strings.HasPrefix(startURL, "https://") {
		startURL = "https://" + startURL
	}

	// 创建HTTP客户端，不自动跟随重定向
	client := &http.Client{
		Timeout: 15 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 不自动跟随重定向，我们要手动处理
			return http.ErrUseLastResponse
		},
	}

	currentURL := startURL
	redirectCount := 0
	maxRedirects := 10 // 最大重定向次数，防止无限循环

	for redirectCount < maxRedirects {
		l.Logger.Infof("访问URL (第%d次): %s", redirectCount+1, currentURL)

		// 创建请求
		req, err := http.NewRequest("GET", currentURL, nil)
		if err != nil {
			return "", redirectCount, fmt.Errorf("创建HTTP请求失败: %v", err)
		}

		// 设置请求头，模拟浏览器
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
		req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
		req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

		// 发送请求
		resp, err := client.Do(req)
		if err != nil {
			return "", redirectCount, fmt.Errorf("发送HTTP请求失败: %v", err)
		}
		defer resp.Body.Close()

		l.Logger.Infof("响应状态码: %d", resp.StatusCode)

		// 检查是否是重定向响应
		if resp.StatusCode >= 300 && resp.StatusCode < 400 {
			// 获取重定向的目标URL
			location := resp.Header.Get("Location")
			if location == "" {
				return "", redirectCount, fmt.Errorf("重定向响应中没有Location头")
			}

			// 处理相对URL
			if strings.HasPrefix(location, "/") {
				// 相对路径，需要拼接域名
				baseURL, err := url.Parse(currentURL)
				if err != nil {
					return "", redirectCount, fmt.Errorf("解析当前URL失败: %v", err)
				}
				location = fmt.Sprintf("%s://%s%s", baseURL.Scheme, baseURL.Host, location)
			} else if !strings.HasPrefix(location, "http://") && !strings.HasPrefix(location, "https://") {
				// 相对URL，需要拼接完整路径
				baseURL, err := url.Parse(currentURL)
				if err != nil {
					return "", redirectCount, fmt.Errorf("解析当前URL失败: %v", err)
				}
				location = baseURL.ResolveReference(&url.URL{Path: location}).String()
			}

			l.Logger.Infof("重定向到: %s", location)
			currentURL = location
			redirectCount++
		} else if resp.StatusCode == 200 {
			// 成功响应，这就是最终URL
			l.Logger.Infof("到达最终URL: %s", currentURL)
			return currentURL, redirectCount, nil
		} else {
			// 其他状态码，可能是错误
			return "", redirectCount, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
		}
	}

	// 达到最大重定向次数
	return "", redirectCount, fmt.Errorf("重定向次数过多，可能存在循环重定向")
}
