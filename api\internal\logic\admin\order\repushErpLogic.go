package order

import (
	"context"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type RepushErpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRepushErpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RepushErpLogic {
	return &RepushErpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// RepushErp 订单重推ERP
func (l *RepushErpLogic) RepushErp(req *types.OrderRepushErpReq) (resp *types.OrderRepushErpResp, err error) {
	l.Logger.Infof("收到订单重推ERP请求: sub_order_no=%s", req.SubOrderNo)

	// 参数验证
	if req.SubOrderNo == "" {
		l.Logger.Errorf("子订单号不能为空")
		return &types.OrderRepushErpResp{
			ErrorCode: 1001,
			ErrorMsg:  "子订单号不能为空",
			Data:      false,
		}, nil
	}

	// 创建T+推送服务
	tPlusService := service.NewTPlusOrderPushService(l.ctx, l.svcCtx)

	// 执行重推
	err = tPlusService.RepushOrderToTPlus(req.SubOrderNo)
	if err != nil {
		l.Logger.Errorf("订单重推ERP失败: sub_order_no=%s, error=%v", req.SubOrderNo, err)

		// 根据错误类型返回不同的错误码
		var errorCode int64 = 1002
		errorMsg := "重推失败"

		if xerrCode, ok := err.(*xerr.CodeError); ok {
			switch xerrCode.GetErrMsg() {
			case "订单不存在":
				errorCode = 1001
				errorMsg = "订单不存在"
			case "只有已完成的订单才能重推T+":
				errorCode = 1003
				errorMsg = "只有已完成的订单才能重推T+"
			case "订单T+推送状态不允许重推":
				errorCode = 1004
				errorMsg = "订单T+推送状态不允许重推"
			default:
				errorMsg = xerrCode.GetErrMsg()
			}
		}

		return &types.OrderRepushErpResp{
			ErrorCode: errorCode,
			ErrorMsg:  errorMsg,
			Data:      false,
		}, nil
	}

	l.Logger.Infof("订单重推ERP成功: sub_order_no=%s", req.SubOrderNo)
	return &types.OrderRepushErpResp{
		ErrorCode: 0,
		ErrorMsg:  "",
		Data:      true,
	}, nil
}
