package balance

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/balance"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func WithdrawalCreateHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WithdrawalCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := balance.NewWithdrawalCreateLogic(r.Context(), svcCtx)
		err := l.WithdrawalCreate(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.Ok(w)
		}
	}
}
