package goods

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniListLogic {
	return &MiniListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniListLogic) MiniList(req *types.MiniGoodsListReq) (resp *types.MiniGoodsListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
