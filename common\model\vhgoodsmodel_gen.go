// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhGoodsFieldNames          = builder.RawFieldNames(&VhGoods{})
	vhGoodsRows                = strings.Join(vhGoodsFieldNames, ",")
	vhGoodsRowsExpectAutoSet   = strings.Join(stringx.Remove(vhGoodsFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`updated_at`"), ",")
	vhGoodsRowsWithPlaceHolder = strings.Join(stringx.Remove(vhGoodsFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`updated_at`"), "=?,") + "=?"


)

type (
	vhGoodsModel interface {
		Insert(ctx context.Context, data *VhGoods) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhGoods, error)
		Update(ctx context.Context, data *VhGoods) error
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhGoodsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhGoods struct {
		Id               uint64         `db:"id"`                // ID
		Title            string         `db:"title"`             // 商品标题
		Brief            string         `db:"brief"`             // 一句话介绍(副标题)
		Type             int64          `db:"type"`              // 类型:1=普通商品,2=抽奖商品
		ItemsInfo        sql.NullString `db:"items_info"`        // 商品简码(简码1*商品数量+简码2*商品数量)格式
		Price            float64        `db:"price"`             // 售价
		Inventory        uint64         `db:"inventory"`         // 库存
		ErpAmount        float64        `db:"erp_amount"`        // 财务核算金额
		CashbackAmount   float64        `db:"cashback_amount"`   // 返现金额
		DeductibleAmount float64        `db:"deductible_amount"` // 礼金可抵扣金额
		ProductImg       string         `db:"product_img"`       // 商品图(多图,分割)
		AvatarImage      string         `db:"avatar_image"`      // 列表图(单图)
		Detail           sql.NullString `db:"detail"`            // 商品描述
		AiResource       sql.NullString `db:"ai_resource"`       // AI资料
		OnsaleStatus     int64          `db:"onsale_status"`     // 上架状态:2=上架,3=下架
		WarehouseCode    string         `db:"warehouse_code"`    // 虚拟仓erp编码(保留字段,暂时不用)
		OnsaleTime       int64          `db:"onsale_time"`       // 上架时间(保留字段,暂时不用)
		SoldOutTime      int64          `db:"sold_out_time"`     // 下架时间(保留字段,暂时不用)
		Purchased        uint64         `db:"purchased"`         // 已售数量
		SalesUserNum     uint64         `db:"sales_user_num"`    // 购买人数
		Pv               uint64         `db:"pv"`                // 浏览量
		DeleteTime       int64          `db:"delete_time"`       // 删除时间
		Sort             int64          `db:"sort"`              // 排序
		VhUid            int64          `db:"vh_uid"`            // 操作用户id
		VhVosName        sql.NullString `db:"vh_vos_name"`       // VOS用户姓名
		CreatedTime      time.Time      `db:"created_time"`      // 创建时间
		UpdateTime       time.Time      `db:"update_time"`       // 更新时间
	}
)

func newVhGoodsModel(conn sqlx.SqlConn) *defaultVhGoodsModel {
	return &defaultVhGoodsModel{
		conn:  conn,
		table: "`vh_goods`",
	}
}

func (m *defaultVhGoodsModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhGoodsModel) FindOne(ctx context.Context, id uint64) (*VhGoods, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhGoodsRows, m.table)
	var resp VhGoods
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhGoodsModel) Insert(ctx context.Context, data *VhGoods) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhGoodsRowsExpectAutoSet)
	return m.conn.ExecCtx(ctx, query, data.Title, data.Brief, data.Type, data.ItemsInfo, data.Price, data.Inventory, data.ErpAmount, data.CashbackAmount, data.DeductibleAmount, data.ProductImg, data.AvatarImage, data.Detail, data.AiResource, data.OnsaleStatus, data.WarehouseCode, data.OnsaleTime, data.SoldOutTime, data.Purchased, data.SalesUserNum, data.Pv, data.DeleteTime, data.Sort, data.VhUid, data.VhVosName)
}

func (m *defaultVhGoodsModel) Update(ctx context.Context, data *VhGoods) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhGoodsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Title, data.Brief, data.Type, data.ItemsInfo, data.Price, data.Inventory, data.ErpAmount, data.CashbackAmount, data.DeductibleAmount, data.ProductImg, data.AvatarImage, data.Detail, data.AiResource, data.OnsaleStatus, data.WarehouseCode, data.OnsaleTime, data.SoldOutTime, data.Purchased, data.SalesUserNum, data.Pv, data.DeleteTime, data.Sort, data.VhUid, data.VhVosName, data.Id)
	return err
}

func (m *defaultVhGoodsModel) tableName() string {
	return m.table
}
