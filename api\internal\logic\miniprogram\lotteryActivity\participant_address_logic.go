package lotteryActivity

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantAddressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantAddressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantAddressLogic {
	return &ParticipantAddressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantAddressLogic) ParticipantAddress(req *types.ActiveLotteryActivityParticipantAddressReq) (resp *types.ActiveLotteryActivityParticipantAddressResp, err error) {
	// todo: add your logic here and delete this line

	return
}
