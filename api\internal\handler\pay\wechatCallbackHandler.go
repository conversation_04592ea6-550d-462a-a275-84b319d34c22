package pay

import (
	"net/http"

	"engine/api/internal/logic/pay"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/core/logx"
)

func WechatCallbackHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		logx.Infof("WechatCallbackHandler received request: method=%s, url=%s, content-type=%s",
			r.Method, r.URL.String(), r.Header.Get("Content-Type"))

		l := pay.NewWechatCallbackLogic(r.Context(), svcCtx)
		err := l.WechatCallback(r)

		// 检查Content-Type来判断返回格式
		contentType := r.Header.Get("Content-Type")
		isV2 := contentType == "application/xml" || contentType == "text/xml" ||
			(contentType == "" && r.ContentLength > 0)

		if err != nil {
			// 支付回调失败，返回失败响应给微信
			logx.Errorf("WechatCallbackHandler processing failed: %v", err)
			w.Write<PERSON>eader(http.StatusOK) // V2版本即使失败也返回200
			if isV2 {
				// V2版本返回XML格式
				w.Header().Set("Content-Type", "application/xml")
				w.Write([]byte(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>`))
				logx.Info("WechatCallbackHandler returned V2 FAIL response")
			} else {
				// V3版本返回JSON格式
				w.Header().Set("Content-Type", "application/json")
				w.Write([]byte(`{"code":"FAIL","message":"处理失败"}`))
				logx.Info("WechatCallbackHandler returned V3 FAIL response")
			}
			return
		}

		// 支付回调成功，返回成功响应给微信
		logx.Info("WechatCallbackHandler processing succeeded")
		w.WriteHeader(http.StatusOK)
		if isV2 {
			// V2版本返回XML格式
			w.Header().Set("Content-Type", "application/xml")
			w.Write([]byte(`<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>`))
			logx.Info("WechatCallbackHandler returned V2 SUCCESS response")
		} else {
			// V3版本返回JSON格式
			w.Header().Set("Content-Type", "application/json")
			w.Write([]byte(`{"code":"SUCCESS","message":"成功"}`))
			logx.Info("WechatCallbackHandler returned V3 SUCCESS response")
		}
	}
}
