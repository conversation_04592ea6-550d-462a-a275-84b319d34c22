package label

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type SimpleListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSimpleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SimpleListLogic {
	return &SimpleListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SimpleListLogic) SimpleList(req *types.SimpleLabelListReq) (resp *types.SimpleLabelListResp, err error) {
	var labels []*model.Label

	// 构建查询条件：只查询启用状态的标签
	builder := l.svcCtx.LabelModel.RowBuilder().
		Where(squirrel.Eq{"status": 1}).
		OrderBy("sort DESC, id DESC")

	// 查询数据
	err = l.svcCtx.LabelModel.FindRows(l.ctx, builder, &labels)
	if err != nil {
		l.Logger.Error("SimpleListLogic LabelModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.SimpleLabelListResp{
		List: make([]types.SimpleLabelInfo, 0),
	}

	for _, label := range labels {
		resp.List = append(resp.List, types.SimpleLabelInfo{
			Id:   label.Id,
			Name: label.Name,
		})
	}

	return
}
