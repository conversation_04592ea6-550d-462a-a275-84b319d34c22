syntax = "v1"

info (
    title:   "解密服务接口"
    desc:    "解密相关接口定义"
    author:  "system"
    version: "v1"
)

import "../common/common.api"

type (
    // AES解密请求
    AESDecryptReq {
        Q string `form:"q" validate:"required" v:"加密数据"`
    }

    // AES解密响应
    AESDecryptResp {
        DecryptedData string `json:"decrypted_data"`
    }

    // AES解密获取链接请求
    AESDecryptUrlReq {
        Q string `form:"q" validate:"required" v:"加密数据"`
    }

    // AES解密获取链接响应
    AESDecryptUrlResp {
        Url string `json:"url"`
    }

    // 短链接跳转请求
    ShortUrlRedirectReq {
        Url string `json:"url" validate:"required" v:"短链接地址"`
    }

    // 短链接跳转响应
    ShortUrlRedirectResp {
        FinalUrl string `json:"final_url"`
        RedirectCount int `json:"redirect_count"`
    }

    // RSA解密请求
    RSADecryptReq {
        EncryptedData string `json:"encrypted_data" validate:"required" v:"加密数据"`
    }

    // RSA解密响应
    RSADecryptResp {
        DecryptedData string `json:"decrypted_data"`
    }
)

// 解密服务接口
@server(
    middleware: Global
    group: decrypt
    prefix: /mulandoGreateDestiny/v1
    timeout: 10s
)
service mulandoGreateDestiny {
    // AES解密
    @handler DecryptAES
    get /card (AESDecryptReq) returns (AESDecryptResp)

    // AES解密获取链接
    @handler DecryptAESUrl
    get /card_url (AESDecryptUrlReq) returns (AESDecryptUrlResp)

    // 短链接跳转获取最终URL
    @handler ShortUrlRedirect
    post /short_url_redirect (ShortUrlRedirectReq) returns (ShortUrlRedirectResp)
}

// RSA解密服务接口
@server(
    middleware: Global
    group: decrypt
    prefix: /mulandoGreateDestiny/v1/decrypt
    timeout: 10s
)
service mulandoGreateDestiny {
    // RSA解密
    @handler DecryptRSA
    post /rsa (RSADecryptReq) returns (RSADecryptResp)
}
