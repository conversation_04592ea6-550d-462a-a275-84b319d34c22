package partner

import (
	"net/http"

	"engine/api/internal/logic/mini/partner"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ApplyPartnerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ApplyPartnerReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := partner.NewApplyPartnerLogic(r.Context(), svcCtx)
		resp, err := l.ApplyPartner(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
