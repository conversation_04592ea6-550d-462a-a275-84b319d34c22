package order

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PaymentStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPaymentStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PaymentStatusLogic {
	return &PaymentStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PaymentStatusLogic) PaymentStatus(req *types.PaymentStatusReq) (resp *types.PaymentStatusResp, err error) {
	// todo: add your logic here and delete this line

	return
}
