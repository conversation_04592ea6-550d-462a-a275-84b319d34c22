package wechat

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AppletOneclickLoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAppletOneclickLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AppletOneclickLoginLogic {
	return &AppletOneclickLoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AppletOneclickLoginLogic) AppletOneclickLogin(req *types.WeChatAppletOneclickLoginReq) (resp *types.WeChatAppletOneclickLoginResp, err error) {
	// todo: add your logic here and delete this line

	return
}
