package goods

import (
	"net/http"

	"engine/api/internal/logic/admin/goods"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func AdminListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AdminGoodsListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := goods.NewAdminListLogic(r.Context(), svcCtx)
		resp, err := l.AdminList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
