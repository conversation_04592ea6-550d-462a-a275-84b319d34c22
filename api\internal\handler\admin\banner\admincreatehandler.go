package banner

import (
	"engine/api/internal/logic/admin/banner"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func AdminCreateHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BannerCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := banner.NewAdminCreateLogic(r.Context(), svcCtx)
		err := l.AdminCreate(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
