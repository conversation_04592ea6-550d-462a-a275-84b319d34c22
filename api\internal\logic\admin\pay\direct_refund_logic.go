package pay

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DirectRefundLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDirectRefundLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DirectRefundLogic {
	return &DirectRefundLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DirectRefundLogic) DirectRefund(req *types.DirectRefundReq) (resp *types.DirectRefundResp, err error) {
	// todo: add your logic here and delete this line

	return
}
