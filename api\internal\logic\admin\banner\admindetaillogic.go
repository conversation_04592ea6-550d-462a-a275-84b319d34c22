package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDetailLogic {
	return &AdminDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDetailLogic) AdminDetail(req *types.BannerDetailReq) (resp *types.BannerInfo, err error) {
	banner, err := l.svcCtx.BannerModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDetailLogic BannerModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = &types.BannerInfo{
		Id:          banner.Id,
		Name:        banner.Name,
		Picture:     function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, banner.Picture),
		JumpType:    banner.JumpType,
		JumpValue:   banner.JumpValue,
		Sort:        banner.Sort,
		Status:      banner.Status,
		CreatedTime: function.FormatTime(banner.CreatedTime),
		UpdateTime:  function.FormatTime(banner.UpdateTime),
		VhUid:       banner.VhUid,
		VhVosName:   banner.VhVosName,
	}

	return
}
