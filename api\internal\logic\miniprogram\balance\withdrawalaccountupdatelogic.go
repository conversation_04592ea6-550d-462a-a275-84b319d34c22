package balance

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalAccountUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalAccountUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalAccountUpdateLogic {
	return &WithdrawalAccountUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalAccountUpdateLogic) WithdrawalAccountUpdate(req *types.WithdrawalAccountUpdateReq) error {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	info, er := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if er != nil && !errors.Is(er, model.ErrNotFound) {
		l.<PERSON>rf("WithdrawalAccountUpdateLogic UserModel.FindOne error: %v", er)
		return xerr.NewErrCode(xerr.DbError)
	}
	if errors.Is(er, model.ErrNotFound) {
		return xerr.NewErrCode(xerr.UserNotExist)
	}

	info.AliAccount = req.AliAccount
	info.AliName = req.AliName
	err := l.svcCtx.UserModel.Update(l.ctx, info)
	if err != nil {
		l.Errorf("WithdrawalAccountUpdateLogic UserModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
