package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftRejectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftRejectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftRejectLogic {
	return &GiftRejectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftRejectLogic) GiftReject(req *types.BoxGiftRejectReq) error {
	// todo: add your logic here and delete this line

	return nil
}
