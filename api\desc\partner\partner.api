syntax = "v1"

info(
    title: "合伙人接口"
    desc: "合伙人签约相关接口"
    author: "system"
    email: ""
    version: "v1"
)

type (
    // 申请合伙人请求
    ApplyPartnerReq {
        Level int64 `json:"level" validate:"required,min=1" v:"合伙人等级"`
    }

    // 申请合伙人响应
    ApplyPartnerResp {
        Message  string `json:"message"`
        Level    int64  `json:"level"`
        EndTime  string `json:"end_time"`
    }

    // 查询合伙人任务请求
    PartnerTasksReq {
        Level int64 `json:"level" validate:"required,min=1" v:"合伙人等级"`
    }

    // 任务详情
    TaskInfo {
        TaskId    int64  `json:"task_id"`    // 任务ID
        TaskName  string `json:"task_name"`  // 任务名称
        TaskType  int64  `json:"task_type"`  // 任务类型：1=分享数,2=考试
        Required  int64  `json:"required"`   // 要求完成数量
        Completed int64  `json:"completed"`  // 已完成数量
        Status    string `json:"status"`     // 状态：completed=已完成,incomplete=未完成
    }

    // 查询合伙人任务响应
    PartnerTasksResp {
        Tasks        []TaskInfo `json:"tasks"`         // 任务列表
        AllCompleted bool       `json:"all_completed"` // 是否全部完成
    }
)

// 小程序合伙人接口
@server(
    middleware: Global,Auth
    group: mini/partner
    prefix: /mulandoGreateDestiny/v1/mini/partner
    timeout: 10s
)
service mulandoGreateDestiny {
    // 申请合伙人
    @handler ApplyPartner
    post /apply (ApplyPartnerReq) returns (ApplyPartnerResp)

    // 查询合伙人任务情况
    @handler PartnerTasks
    post /tasks (PartnerTasksReq) returns (PartnerTasksResp)
}
