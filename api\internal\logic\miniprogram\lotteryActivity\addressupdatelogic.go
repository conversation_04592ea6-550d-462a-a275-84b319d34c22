package lotteryActivity

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressUpdateLogic {
	return &AddressUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressUpdateLogic) AddressUpdate(req *types.ActiveLotteryActivityAddressUpdateReq) error {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	//验证地址是否存在
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.AddressId)
	if err != nil && errors.Is(err, model.ErrNotFound) {
		l.Logger.Error("AddressUpdate UserAddressModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	//验证是否是当前用户
	if address.Uid != uid {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	//更新参与记录
	participant, err := l.svcCtx.LotteryParticipantModel.FindOneByQuery(l.ctx, l.svcCtx.LotteryParticipantModel.RowBuilder().Where(squirrel.Eq{"activity_id": req.Id, "uid": uid}))
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Logger.Error("AddressUpdate LotteryParticipantModel.FindOneByQuery error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	//验证是否参与过活动
	if errors.Is(err, model.ErrNotFound) {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	//更新地址
	participant.AddressId = uint64(req.AddressId)
	err = l.svcCtx.LotteryParticipantModel.Update(l.ctx, participant)
	if err != nil {
		l.Logger.Error("AddressUpdate LotteryParticipantModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
