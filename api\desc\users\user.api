syntax = "v1"

info (
	title:   "mulando"
	author:  "ligenhui"
	email:   "<EMAIL>"
	version: "v3"
)

type (
	// 用户信息
	UserInfo {
		Id             int64   `json:"id"`
		AppletOpenid   string  `json:"applet_openid"`
		Telephone      string  `json:"telephone"`
		Nickname       string  `json:"nickname"`
		AvatarImage    string  `json:"avatar_image"`
		Sex            int64   `json:"sex"`
		Birthday       string  `json:"birthday"`
		IsDisabled     int64   `json:"is_disabled"`
		Balance        float64 `json:"balance"`
		Level          int64   `json:"level"`
		LastLoginTime  string  `json:"last_login_time"`
		CreatedTime    string  `json:"created_time"`
		UpdateTime     string  `json:"update_time"`
		TotalBalance   float64 `json:"total_balance"`
		LevelStartTime string  `json:"level_start_time"`
		LevelEndTime   string  `json:"level_end_time"`
		Type           int64   `json:"type"`
		LevelStatus    int64   `json:"level_status"`
	}
	// 后台用户列表请求
	AdminUserListReq {
		Page        int64  `form:"page,default=1"`
		Limit       int64  `form:"limit,default=10"`
		Telephone   string `form:"telephone,optional"`
		Nickname    string `form:"nickname,optional"`
		Level       int64  `form:"level,optional"`
		Type        int64  `form:"type,optional"`
		LevelStatus int64  `form:"level_status,optional"`
		IsDisabled  int64  `form:"is_disabled,optional"`
	}
	// 后台用户列表响应
	AdminUserListResp {
		List      []UserInfo `json:"list"`
		Total     int64      `json:"total"`
		TotalUser int64      `json:"total_user"`
	}
	// 设置用户禁用状态请求
	UserSetDisabledReq {
		Id         int64 `json:"id" validate:"required" v:"用户ID"`
		IsDisabled int64 `json:"is_disabled" validate:"min=1,max=2" v:"禁用状态"`
	}
	// 微信授权登录请求
	WeChatLoginReq {
		Code        string `json:"code" validate:"required" v:"临时授权码"`
		PCode       string `json:"p_code" validate:"required" v:"手机号获取凭证"`
		SourceUid   string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
		Nickname    string `json:"nickname,optional" validate:"omitempty" v:"用户昵称"`
		AvatarImage string `json:"avatar_image,optional" validate:"omitempty" v:"用户头像"`
	}
	// 微信授权登录响应
	WeChatLoginResp {
		UID      int64    `json:"uid"`
		Token    string   `json:"token"`
		OpenID   string   `json:"openid"`
		UnionID  string   `json:"unionid"`
		Name     string   `json:"name"`
		Avatar   string   `json:"avatar"`
		UserInfo UserInfo `json:"user_info"`
	}
	// 获取用户信息响应
	GetUserInfoResp {
		UserInfo UserInfo `json:"user_info"`
	}
	// 用户地址信息
	UserAddressInfo {
		Id             int64  `json:"id"`
		Uid            int64  `json:"uid"`
		ProvinceId     int64  `json:"province_id"`
		CityId         int64  `json:"city_id"`
		TownId         int64  `json:"town_id"`
		Address        string `json:"address"`
		IsDefault      int64  `json:"is_default"`
		Label          string `json:"label"`
		Code           string `json:"code"`
		Consignee      string `json:"consignee"`
		ConsigneePhone string `json:"consignee_phone"`
		ProvinceName   string `json:"province_name"`
		CityName       string `json:"city_name"`
		TownName       string `json:"town_name"`
		CreatedTime    string `json:"created_time"`
		UpdateTime     string `json:"update_time"`
	}
	// 地址列表请求
	AddressListReq {
		Page  int64 `form:"page,default=1"`
		Limit int64 `form:"limit,default=10"`
	}
	// 地址列表响应
	AddressListResp {
		List  []UserAddressInfo `json:"list"`
		Total int64             `json:"total"`
	}
	// 地址详情请求
	AddressDetailReq {
		Id int64 `form:"id" validate:"required" v:"地址ID"`
	}
	// 创建地址请求
	AddressCreateReq {
		ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
		CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
		TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
		Address        string `json:"address" validate:"required" v:"详细地址"`
		Consignee      string `json:"consignee" validate:"required" v:"收货人"`
		ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
		ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
		CityName       string `json:"city_name" validate:"required" v:"城市名称"`
		TownName       string `json:"town_name" validate:"required" v:"区县名称"`
		Label          string `json:"label,optional" v:"标签"`
		Code           string `json:"code,optional" v:"邮编"`
		IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
	}

	// 创建地址响应
	AddressCreateResp {
		Id int64 `json:"id"`
	}
	// 更新地址请求
	AddressUpdateReq {
		Id             int64  `json:"id" validate:"required" v:"地址ID"`
		ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
		CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
		TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
		Address        string `json:"address" validate:"required" v:"详细地址"`
		Consignee      string `json:"consignee" validate:"required" v:"收货人"`
		ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
		ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
		CityName       string `json:"city_name" validate:"required" v:"城市名称"`
		TownName       string `json:"town_name" validate:"required" v:"区县名称"`
		Label          string `json:"label,optional" v:"标签"`
		Code           string `json:"code,optional" v:"邮编"`
		IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
	}
	// 删除地址请求
	AddressDeleteReq {
		Id int64 `json:"id" validate:"required" v:"地址ID"`
	}
	// 更新用户信息请求
	UserUpdateInfoReq {
		Nickname    string `json:"nickname" validate:"required" v:"用户昵称"`
		AvatarImage string `json:"avatar_image" validate:"required" v:"用户头像"`
	}
)

// 后台用户管理接口
@server (
	middleware: Global,Admin
	group:      admin/user
	prefix:     /mulandoGreateDestiny/v1/admin/user
	timeout:    3s
)
service mulandoGreateDestiny {
	// 用户列表
	@handler AdminList
	get /list (AdminUserListReq) returns (AdminUserListResp)

	// 设置用户禁用状态
	@handler SetDisabled
	post /set_disabled (UserSetDisabledReq)
}

// 小程序用户接口
@server (
	middleware: Global
	group:      miniprogram/user
	prefix:     /mulandoGreateDestiny/v1/miniprogram/user
	timeout:    10s
)
service mulandoGreateDestiny {
	// 微信授权登录
	@handler WeChatLogin
	post /wechat_login (WeChatLoginReq) returns (WeChatLoginResp)
}

@server (
	middleware: Global,Auth
	group:      miniprogram/user
	prefix:     /mulandoGreateDestiny/v1/miniprogram/user
	timeout:    3s
)
service mulandoGreateDestiny {
	// 退出登录
	@handler Logout
	post /logout

	// 获取用户信息
	@handler GetUserInfo
	get /info returns (GetUserInfoResp)

	// 更新用户信息
	@handler UpdateUserInfo
	post /update_info (UserUpdateInfoReq)
}

// 小程序用户地址接口
@server (
	middleware: Global,Auth
	group:      miniprogram/address
	prefix:     /mulandoGreateDestiny/v1/miniprogram/address
	timeout:    3s
)
service mulandoGreateDestiny {
	// 地址列表
	@handler AddressList
	get /list (AddressListReq) returns (AddressListResp)

	// 地址详情
	@handler AddressDetail
	get /detail (AddressDetailReq) returns (UserAddressInfo)

	// 创建地址
	@handler AddressCreate
	post /create (AddressCreateReq) returns (AddressCreateResp)

	// 更新地址
	@handler AddressUpdate
	post /update (AddressUpdateReq)

	// 删除地址
	@handler AddressDelete
	post /delete (AddressDeleteReq)
}

