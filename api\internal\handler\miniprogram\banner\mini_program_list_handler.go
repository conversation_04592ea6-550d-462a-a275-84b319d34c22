package banner

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/banner"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func MiniProgramListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := banner.NewMiniProgramListLogic(r.Context(), svcCtx)
		resp, err := l.MiniProgramList()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
