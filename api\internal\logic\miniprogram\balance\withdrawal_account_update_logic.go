package balance

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalAccountUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalAccountUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalAccountUpdateLogic {
	return &WithdrawalAccountUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalAccountUpdateLogic) WithdrawalAccountUpdate(req *types.WithdrawalAccountUpdateReq) error {
	// todo: add your logic here and delete this line

	return nil
}
