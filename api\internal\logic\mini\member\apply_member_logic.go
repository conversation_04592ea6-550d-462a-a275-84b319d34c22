package member

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ApplyMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewApplyMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApplyMemberLogic {
	return &ApplyMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApplyMemberLogic) ApplyMember(req *types.ApplyMemberReq) (resp *types.ApplyMemberResp, err error) {
	// todo: add your logic here and delete this line

	return
}
