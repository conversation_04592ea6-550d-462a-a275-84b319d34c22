package goods

import (
	"net/http"

	"engine/api/internal/logic/admin/goods"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func SetOnsaleStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GoodsSetOnsaleStatusReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := goods.NewSetOnsaleStatusLogic(r.Context(), svcCtx)
		err := l.SetOnsaleStatus(&req)
		result.HttpResult(r, w, result.<PERSON>ull<PERSON>son{}, err)
	}
}
