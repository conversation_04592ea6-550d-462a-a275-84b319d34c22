package label

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SimpleListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSimpleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SimpleListLogic {
	return &SimpleListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SimpleListLogic) SimpleList(req *types.SimpleLabelListReq) (resp *types.SimpleLabelListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
