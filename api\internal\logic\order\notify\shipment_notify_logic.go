package notify

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShipmentNotifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShipmentNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShipmentNotifyLogic {
	return &ShipmentNotifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShipmentNotifyLogic) ShipmentNotify(req *types.ShipmentNotifyReq) (resp *types.ShipmentNotifyResp, err error) {
	// todo: add your logic here and delete this line

	return
}
