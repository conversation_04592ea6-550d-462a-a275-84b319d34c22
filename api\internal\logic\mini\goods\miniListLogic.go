package goods

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type MiniListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniListLogic {
	return &MiniListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniListLogic) MiniList(req *types.MiniGoodsListReq) (resp *types.MiniGoodsListResp, err error) {
	var goods []*model.VhGoods

	// 构建查询条件 - 固定筛选项：未删除的，上架的
	builder := l.svcCtx.GoodsModel.RowBuilder().
		Where(squirrel.Eq{"delete_time": 0}). // 未删除
		Where(squirrel.Eq{"type": 1}). // 类型:1=普通商品,2=抽奖商品
		Where(squirrel.Eq{"onsale_status": 2}) // 上架状态

	// 商品名称筛选
	if req.Title != "" {
		builder = builder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
	}

	// 标签名称筛选
	if req.LabelName != "" {
		// 通过子查询筛选有指定标签的商品
		subQuery := squirrel.Select("period_id").From("vh_goods_label").
			Where(squirrel.Like{"name": "%" + req.LabelName + "%"})
		builder = builder.Where(squirrel.Expr("id IN (?)", subQuery))
	}

	// 排序处理
	orderBy := "sort DESC, id DESC" // 默认排序
	if req.SalesUserNumSort != "" {
		if req.SalesUserNumSort == "asc" {
			orderBy = "sales_user_num ASC, " + orderBy
		} else if req.SalesUserNumSort == "desc" {
			orderBy = "sales_user_num DESC, " + orderBy
		}
	}
	if req.PriceSort != "" {
		if req.PriceSort == "asc" {
			orderBy = "price ASC, " + orderBy
		} else if req.PriceSort == "desc" {
			orderBy = "price DESC, " + orderBy
		}
	}
	builder = builder.OrderBy(orderBy)

	// 分页
	offset := (req.Page - 1) * req.Limit
	builder = builder.Limit(uint64(req.Limit)).Offset(uint64(offset))

	// 查询数据
	err = l.svcCtx.GoodsModel.FindRows(l.ctx, builder, &goods)
	if err != nil {
		l.Logger.Error("MiniList GoodsModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From(l.svcCtx.GoodsModel.TableName()).
		Where(squirrel.Eq{"delete_time": 0}).
		Where(squirrel.Eq{"type": 1}). // 类型:1=普通商品,2=抽奖商品
		Where(squirrel.Eq{"onsale_status": 2})

	if req.Title != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
	}
	if req.LabelName != "" {
		subQuery := squirrel.Select("period_id").From("vh_goods_label").
			Where(squirrel.Like{"name": "%" + req.LabelName + "%"})
		countBuilder = countBuilder.Where(squirrel.Expr("id IN (?)", subQuery))
	}

	total, err := l.svcCtx.GoodsModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("MiniList GoodsModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.MiniGoodsListResp{
		List:  make([]types.MiniGoodsInfo, 0),
		Total: total,
	}

	for _, item := range goods {
		goodsInfo := types.MiniGoodsInfo{
			Id:               int64(item.Id),
			Title:            item.Title,
			Brief:            item.Brief,
			Type:             item.Type,
			Price:            item.Price,
			Inventory:        int64(item.Inventory),
			CashbackAmount:   item.CashbackAmount,
			DeductibleAmount: item.DeductibleAmount,
			AvatarImage:      l.processImage(item.AvatarImage),
			SalesUserNum:     int64(item.SalesUserNum),
		}

		// 查询商品标签
		labels, err := l.svcCtx.GoodsLabelModel.FindByPeriodId(l.ctx, int64(item.Id))
		if err == nil {
			for _, label := range labels {
				goodsInfo.Labels = append(goodsInfo.Labels, types.GoodsLabelInfo{
					LabelId: label.LabelId.Int64,
					Name:    label.Name,
				})
			}
		}

		resp.List = append(resp.List, goodsInfo)
	}

	return
}

// processImage 处理单个图片
func (l *MiniListLogic) processImage(image string) string {
	if image == "" {
		return ""
	}
	return l.svcCtx.Config.ITEM.ALIURL + image
}
