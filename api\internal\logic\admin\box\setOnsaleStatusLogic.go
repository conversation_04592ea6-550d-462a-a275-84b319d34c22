package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type SetOnsaleStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetOnsaleStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetOnsaleStatusLogic {
	return &SetOnsaleStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetOnsaleStatusLogic) SetOnsaleStatus(req *types.BoxSetOnsaleStatusReq) error {
	// 查询盲盒是否存在
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("SetOnsaleStatus BoxModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已删除
	if box.DeleteTime > 0 {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 更新上下架状态
	updateBuilder := squirrel.Update(l.svcCtx.BoxModel.TableName()).
		Set("onsale_status", req.OnsaleStatus).
		Set("vh_uid", adminUid).
		Set("vh_vos_name", adminVosName).
		Where(squirrel.Eq{"id": req.Id})

	_, err = l.svcCtx.BoxModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Error("SetOnsaleStatus BoxModel.UpdateCustom error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
