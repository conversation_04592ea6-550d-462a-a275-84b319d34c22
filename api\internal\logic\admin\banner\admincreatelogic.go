package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCreateLogic {
	return &AdminCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCreateLogic) AdminCreate(req *types.BannerCreateReq) error {
	// 从context中获取管理员信息
	adminUid, err := function.GetAdminUid(l.ctx)
	if err != nil {
		l.Logger.Error("AdminCreateLogic GetAdminUid error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	adminVosName, err := function.GetAdminVosName(l.ctx)
	if err != nil {
		l.Logger.Error("AdminCreateLogic GetAdminVosName error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	banner := &model.Banner{
		Name:      req.Name,
		Picture:   req.Picture,
		JumpType:  req.JumpType,
		JumpValue: req.JumpValue,
		Sort:      req.Sort,
		Status:    req.Status,
		VhUid:     adminUid,
		VhVosName: adminVosName,
	}

	_, err = l.svcCtx.BannerModel.Insert(l.ctx, banner)
	if err != nil {
		l.Logger.Error("AdminCreateLogic BannerModel.Insert error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
