package share

import (
	"context"
	"engine/common/xerr"
	"github.com/sashabaranov/go-openai"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RefineTitleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRefineTitleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefineTitleLogic {
	return &RefineTitleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefineTitleLogic) RefineTitle(req *types.RefineTitleReq) (resp *types.RefineTitleResp, err error) {
	rsp, err := l.svcCtx.DeepSeek.CreateChatCompletion(l.ctx, openai.ChatCompletionRequest{
		Model:       "deepseek-chat",
		Temperature: 1.5,
		Messages: []openai.ChatCompletionMessage{
			{
				Role: openai.ChatMessageRoleSystem,
				Content: `
你的角色是对文案进行润色。用户会给一段推荐词（场景：用户将葡萄酒平台的商品分享给好友，需要附上一段推荐词），请先理解用户想要表达的意思，然后进行润色。润色后的文字尽量精简，保留原意。输出:润色后的文本,不要输出多余解释
`,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: req.Title,
			},
		},
	})

	if err != nil {
		l.Errorf("RefineTitleLogic DeepSeek.CreateChatCompletion err: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "生成失败,请稍后重试")
	}

	resp = new(types.RefineTitleResp)
	resp.Title = rsp.Choices[0].Message.Content

	return
}
