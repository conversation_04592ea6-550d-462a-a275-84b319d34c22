package goods

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDeleteLogic {
	return &AdminDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDeleteLogic) AdminDelete(req *types.GoodsDeleteReq) error {
	// 检查商品是否存在
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDelete GoodsModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if goods.DeleteTime > 0 {
		return xerr.NewErrMsg("商品已被删除")
	}

	// 软删除：设置删除时间
	now := time.Now().Unix()
	updateBuilder := squirrel.Update(l.svcCtx.GoodsModel.TableName()).
		Set("delete_time", now).
		Where(squirrel.Eq{"id": req.Id})

	_, err = l.svcCtx.GoodsModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Error("AdminDelete GoodsModel.UpdateCustom error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
