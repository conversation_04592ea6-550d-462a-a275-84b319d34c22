package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminBannerListReq) (resp *types.AdminBannerListResp, err error) {
	var banners []*model.Banner

	// 构建查询条件
	builder := l.svcCtx.BannerModel.RowBuilder().OrderBy("sort DESC, id DESC")

	// 标题筛选
	if req.Title != "" {
		builder = builder.Where(squirrel.Like{"name": "%" + req.Title + "%"})
	}

	// 状态筛选
	if req.Status != 0 {
		builder = builder.Where(squirrel.Eq{"status": req.Status})
	}

	// 分页
	offset := (req.Page - 1) * req.Limit
	builder = builder.Limit(uint64(req.Limit)).Offset(uint64(offset))

	// 查询数据
	err = l.svcCtx.BannerModel.FindRows(l.ctx, builder, &banners)
	if err != nil {
		l.Logger.Error("AdminListLogic BannerModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From(l.svcCtx.BannerModel.TableName())
	if req.Title != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"name": "%" + req.Title + "%"})
	}
	if req.Status != 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"status": req.Status})
	}

	total, err := l.svcCtx.BannerModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminListLogic BannerModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.AdminBannerListResp{
		List:  make([]types.BannerInfo, 0),
		Total: total,
	}

	for _, banner := range banners {
		resp.List = append(resp.List, types.BannerInfo{
			Id:          banner.Id,
			Name:        banner.Name,
			Picture:     function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, banner.Picture),
			JumpType:    banner.JumpType,
			JumpValue:   banner.JumpValue,
			Sort:        banner.Sort,
			Status:      banner.Status,
			CreatedTime: function.FormatTime(banner.CreatedTime),
			UpdateTime:  function.FormatTime(banner.UpdateTime),
			VhUid:       banner.VhUid,
			VhVosName:   banner.VhVosName,
		})
	}

	return
}
