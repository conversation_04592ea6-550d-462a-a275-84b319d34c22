package winenotes

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminConvertWineNotesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 后台通过UUID转换WineNotes为HTML
func NewAdminConvertWineNotesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminConvertWineNotesLogic {
	return &AdminConvertWineNotesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminConvertWineNotesLogic) AdminConvertWineNotes(req *types.WineNotesConvertReq) (resp *types.WineNotesConvertResp, err error) {
	// todo: add your logic here and delete this line

	return
}
