package balance

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalCreateLogic {
	return &WithdrawalCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalCreateLogic) WithdrawalCreate(req *types.WithdrawalCreateReq) error {
	// todo: add your logic here and delete this line

	return nil
}
