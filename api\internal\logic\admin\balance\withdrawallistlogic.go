package balance

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalListLogic {
	return &WithdrawalListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalListLogic) WithdrawalList(req *types.WithdrawalListReq) (resp *types.WithdrawalListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.WithdrawalListInfo
	)

	// 构建查询条件
	where := squirrel.And{}

	// 添加状态筛选条件
	if req.Status > 0 {
		where = append(where, squirrel.Eq{"w.status": req.Status})
	}
	if req.AliName != "" {
		where = append(where, squirrel.Like{"u.ali_name": "%" + req.AliName + "%"})
	}
	if req.AliAccount != "" {
		where = append(where, squirrel.Like{"u.ali_account": "%" + req.AliAccount + "%"})
	}
	if req.Telephone != "" {
		where = append(where, squirrel.Like{"u.telephone": "%" + req.Telephone + "%"})
	}
	if req.StartTime != "" && req.EndTime != "" {
		where = append(where,
			squirrel.GtOrEq{"w.create_time": req.StartTime},
			squirrel.LtOrEq{"w.create_time": req.EndTime},
		)
	}

	// 获取总数
	wait.Go(func() error {
		ct, er := l.svcCtx.WithdrawalModel.FindCount(l.ctx, squirrel.Select("COUNT(*) as count").
			From("vh_withdrawal w").
			LeftJoin("vh_user u on w.uid = u.id").
			Where(where))
		if er != nil {
			l.Errorf("ListLogic.WithdrawalModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		er := l.svcCtx.WithdrawalModel.FindRows(l.ctx, squirrel.Select("w.*,u.ali_name,u.ali_account,u.telephone").
			From("vh_withdrawal w").
			LeftJoin("vh_user u on w.uid = u.id").
			Where(where).
			OrderBy("w.status asc, w.id desc").
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)), &list)
		if er != nil {
			l.Errorf("WithdrawalListLogic.WithdrawalModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.WithdrawalListResp)
	resp.Total = total
	resp.List = make([]types.WithdrawalListInfo, 0, len(list))
	for _, ls := range list {
		info := types.WithdrawalListInfo{
			Id:         ls.Id,
			Uid:        ls.Uid,
			AliName:    ls.AliName,
			AliAccount: ls.AliAccount,
			Telephone:  ls.Telephone,
			Amount:     ls.Amount,
			Status:     ls.Status,
			BackNote:   ls.BackNote,
			CreateTime: common.TimeToString(ls.CreateTime),
		}
		if ls.HandleTime.Valid {
			info.HandleTime = common.TimeToString(ls.HandleTime.Time)
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
