package balance

import (
	"context"
	"database/sql"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalReviewLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalReviewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalReviewLogic {
	return &WithdrawalReviewLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalReviewLogic) WithdrawalReview(req *types.WithdrawalReviewReq) error {
	info, er := l.svcCtx.WithdrawalModel.FindOne(l.ctx, req.Id)
	if er != nil && !errors.Is(er, model.ErrNotFound) {
		l.<PERSON>("WithdrawalReviewLogic WithdrawalModel.FindOne error: %v", er)
		return xerr.NewErrCode(xerr.DataNoExistError)
	}
	if errors.Is(er, model.ErrNotFound) {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	//只有审核中的能处理
	if info.Status != 1 {
		return nil
	}

	if req.Status == 4 {
		user, er := l.svcCtx.UserModel.FindOne(l.ctx, cast.ToInt64(info.Uid))
		if er != nil && !errors.Is(er, model.ErrNotFound) {
			l.Errorf("WithdrawalReviewLogic UserModel.FindOne error: %v", er)
			return xerr.NewErrCode(xerr.DbError)
		}
		if errors.Is(er, model.ErrNotFound) {
			return xerr.NewErrCode(xerr.UserNotExist)
		}
		//驳回
		err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
			//更新提现记录
			_, er = l.svcCtx.WithdrawalModel.UpdateCustomTx(l.ctx, tx, squirrel.Update(l.svcCtx.WithdrawalModel.TableName()).
				Where(squirrel.Eq{"id": req.Id}).
				Set("status", 4).
				Set("back_note", req.BackNote).
				Set("handle_time", time.Now()))
			if er != nil {
				l.Errorf("WithdrawalReviewLogic Trans.WithdrawalModel.UpdateCustomTx error: %v", er)
				return er
			}

			//插入余额历史记录
			_, er = l.svcCtx.BalanceHistory.InsertTx(ctx, tx, &model.VhBalanceHistory{
				UniqueCode:    logic.GenerateWithdrawalNo(user.Id, cast.ToInt64(info.Id)) + "_back",
				Uid:           info.Uid,
				Type:          1,
				Amount:        info.Amount,
				AfterAmount:   user.Balance + info.Amount,
				OperationType: 6,
				OperationName: "系统",
			})
			if er != nil {
				l.Errorf("WithdrawalReviewLogic Trans.BalanceHistory.InsertTx error: %v", er)
				return er
			}

			//更新用户余额
			result, err := l.svcCtx.UserModel.UpdateCustomTx(ctx, tx, squirrel.Update(l.svcCtx.UserModel.TableName()).
				Where(squirrel.Eq{"id": info.Uid, "balance": user.Balance}).
				Set("balance", user.Balance+info.Amount))
			if err != nil {
				logx.Errorf("WithdrawalReviewLogic Trans.UserModel.UpdateCustomTx err: %v", err)
				return err
			}

			affected, err := result.RowsAffected()
			if err != nil {
				logx.Errorf("WithdrawalReviewLogic Trans.UserModel.RowsAffected err: %v", err)
				return err
			}
			if affected == 0 {
				return errors.New("用户余额发送变动 请重试")
			}

			_ = l.svcCtx.UserModel.ClearCache(l.ctx, user)
			return nil
		})

		if err != nil {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "用户余额发送变动，请重试")
		}

	} else {
		info.Status = 2
		er = l.svcCtx.WithdrawalModel.Update(l.ctx, info)
		if er != nil {
			l.Errorf("WithdrawalReviewLogic WithdrawalModel.Update error: %v", er)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	//更新提现记录
	return nil
}
