package goods

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCopyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCopyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCopyLogic {
	return &AdminCopyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCopyLogic) AdminCopy(req *types.GoodsCopyReq) error {
	// todo: add your logic here and delete this line

	return nil
}
