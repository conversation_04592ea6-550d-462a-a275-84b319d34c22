package goods

import (
	"context"
	"strings"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDetailLogic {
	return &AdminDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDetailLogic) AdminDetail(req *types.GoodsDetailReq) (resp *types.GoodsInfo, err error) {
	// 查找商品
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDetail GoodsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if goods.DeleteTime > 0 {
		return nil, xerr.NewErrMsg("商品已被删除")
	}

	// 构建响应
	resp = &types.GoodsInfo{
		Id:               int64(goods.Id),
		Title:            goods.Title,
		Brief:            goods.Brief,
		Type:             goods.Type,
		ItemsInfo:        goods.ItemsInfo.String,
		Price:            goods.Price,
		Inventory:        int64(goods.Inventory),
		ErpAmount:        goods.ErpAmount,
		CashbackAmount:   goods.CashbackAmount,
		DeductibleAmount: goods.DeductibleAmount,
		ProductImg:       l.processImages(goods.ProductImg),
		AvatarImage:      l.processImage(goods.AvatarImage),
		Detail:           goods.Detail.String,
		OnsaleStatus:     goods.OnsaleStatus,
		WarehouseCode:    goods.WarehouseCode,
		OnsaleTime:       goods.OnsaleTime,
		SoldOutTime:      goods.SoldOutTime,
		Purchased:        int64(goods.Purchased),
		SalesUserNum:     int64(goods.SalesUserNum),
		Pv:               int64(goods.Pv),
		Sort:             goods.Sort,
		VhUid:            goods.VhUid,
		VhVosName:        goods.VhVosName.String,
		CreatedTime:      function.FormatTime(goods.CreatedTime),
		UpdateTime:       function.FormatTime(goods.UpdateTime),
		Labels:           make([]types.GoodsLabelInfo, 0),
	}

	// 查询商品标签
	labels, err := l.svcCtx.GoodsLabelModel.FindByPeriodId(l.ctx, int64(goods.Id))
	if err == nil {
		for _, label := range labels {
			resp.Labels = append(resp.Labels, types.GoodsLabelInfo{
				LabelId: label.LabelId.Int64,
				Name:    label.Name,
			})
		}
	}

	return
}

// processImage 处理单个图片URL
func (l *AdminDetailLogic) processImage(image string) string {
	if image == "" {
		return ""
	}
	if strings.HasPrefix(image, "http") {
		return image
	}
	return l.svcCtx.Config.ITEM.ALIURL + image
}

// processImages 处理多个图片URL（逗号分隔）
func (l *AdminDetailLogic) processImages(images string) string {
	if images == "" {
		return ""
	}

	imageList := strings.Split(images, ",")
	var processedImages []string

	for _, img := range imageList {
		img = strings.TrimSpace(img)
		if img != "" {
			if strings.HasPrefix(img, "http") {
				processedImages = append(processedImages, img)
			} else {
				processedImages = append(processedImages, l.svcCtx.Config.ITEM.ALIURL+img)
			}
		}
	}

	return strings.Join(processedImages, ",")
}
