package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type RepealCardLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRepealCardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RepealCardLogic {
	return &RepealCardLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RepealCardLogic) RepealCard(req *types.BoxRepealCardReq) error {
	// 查询礼品卡是否存在
	card, err := l.svcCtx.BoxCardsModel.FindOne(l.ctx, req.CardNo)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrMsg("礼品卡不存在")
		}
		l.Logger.Error("RepealCard BoxCardsModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查卡状态，只能作废未使用的卡
	if card.Status != 1 {
		switch card.Status {
		case 2:
			return xerr.NewErrMsg("礼品卡已使用，无法作废")
		case 5:
			return xerr.NewErrMsg("礼品卡已作废")
		default:
			return xerr.NewErrMsg("礼品卡状态异常")
		}
	}

	// 获取管理员信息
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 作废礼品卡
	err = l.svcCtx.BoxCardsModel.RepealCard(l.ctx, req.CardNo, adminVosName, req.Remark)
	if err != nil {
		l.Logger.Error("RepealCard BoxCardsModel.RepealCard error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
