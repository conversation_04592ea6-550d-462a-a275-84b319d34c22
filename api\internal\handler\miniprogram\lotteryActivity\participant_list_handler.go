package lotteryActivity

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/lotteryActivity"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ParticipantListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ActiveLotteryActivityParticipantListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := lotteryActivity.NewParticipantListLogic(r.Context(), svcCtx)
		resp, err := l.ParticipantList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
