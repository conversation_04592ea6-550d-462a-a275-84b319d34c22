package order

import (
	"context"
	"database/sql"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelOrderLogic {
	return &CancelOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CancelOrderLogic) CancelOrder(req *types.CancelOrderReq) (resp *types.CancelOrderResp, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, req.MainOrderNo)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("订单不存在")
		}
		l.Logger.Errorf("CancelOrder FindOneByMainOrderNo error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证订单所有权
	if mainOrder.Uid != uid {
		return nil, xerr.NewErrMsg("无权操作此订单")
	}

	// 验证订单状态：只能取消待支付订单
	if mainOrder.MainOrderStatus != 0 {
		switch mainOrder.MainOrderStatus {
		case 1:
			return nil, xerr.NewErrMsg("订单已支付，无法取消")
		case 2:
			return nil, xerr.NewErrMsg("订单已发货，无法取消")
		case 3:
			return nil, xerr.NewErrMsg("订单已完成，无法取消")
		case 4:
			return nil, xerr.NewErrMsg("订单已取消")
		default:
			return nil, xerr.NewErrMsg("订单状态异常，无法取消")
		}
	}

	// 获取子订单信息
	subOrders, err := l.svcCtx.OrderModel.FindByMainOrderId(l.ctx, mainOrder.Id)
	if err != nil {
		l.Logger.Errorf("CancelOrder FindByMainOrderId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if len(subOrders) == 0 {
		return nil, xerr.NewErrMsg("订单数据异常")
	}

	// 执行取消订单操作
	refundAmount, err := l.cancelOrderTransaction(mainOrder, subOrders)
	if err != nil {
		return nil, err
	}

	l.Logger.Infof("用户手动取消订单成功: uid=%d, main_order_no=%s, refund_amount=%.2f",
		uid, req.MainOrderNo, refundAmount)

	return &types.CancelOrderResp{
		Message:      "订单取消成功",
		RefundAmount: refundAmount,
	}, nil
}

// cancelOrderTransaction 取消订单的事务处理
func (l *CancelOrderLogic) cancelOrderTransaction(mainOrder *model.VhOrderMain, subOrders []*model.VhOrder) (float64, error) {
	var refundAmount float64 = 0

	// 开启事务处理订单取消
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		cancelTime := time.Now().Unix()

		// 更新主订单状态为已取消
		updateMainOrderQuery := `UPDATE vh_order_main SET main_order_status = 4, cancel_time = ? WHERE id = ? AND main_order_status = 0`
		result, err := tx.ExecContext(ctx, updateMainOrderQuery, cancelTime, mainOrder.Id)
		if err != nil {
			l.Logger.Errorf("cancelOrderTransaction update main order error: %v", err)
			return err
		}

		affected, _ := result.RowsAffected()
		if affected == 0 {
			// 订单状态已经不是待支付，可能已经被其他进程处理了
			return xerr.NewErrMsg("订单状态已变更，无法取消")
		}

		// 更新子订单状态为已取消
		for _, subOrder := range subOrders {
			// 更新子订单状态为已取消，记录礼金退回金额
			updateSubOrderQuery := `UPDATE vh_order SET
				sub_order_status = 4,
				cancel_time = ?,
				refund_deductible = ?
				WHERE id = ? AND sub_order_status = 0`

			_, err = tx.ExecContext(ctx, updateSubOrderQuery, cancelTime, subOrder.DeductibleAmount, subOrder.Id)
			if err != nil {
				l.Logger.Errorf("cancelOrderTransaction update sub order error: %v", err)
				return err
			}

			// 回滚商品库存
			updateGoodsQuery := `UPDATE vh_goods SET inventory = inventory + ? WHERE id = ?`
			_, err = tx.ExecContext(ctx, updateGoodsQuery, subOrder.OrderQty, subOrder.GoodsId)
			if err != nil {
				l.Logger.Errorf("cancelOrderTransaction rollback goods inventory error: %v", err)
				return err
			}

			l.Logger.Infof("cancelOrderTransaction sub order cancelled: sub_order_no=%s, deductible_returned=%.2f",
				subOrder.SubOrderNo, subOrder.DeductibleAmount)
		}

		// 回滚用户礼金余额并记录余额历史
		if mainOrder.DeductibleAmount > 0 {
			// 获取用户当前余额
			user, err := l.svcCtx.UserModel.FindOne(ctx, mainOrder.Uid)
			if err != nil {
				l.Logger.Errorf("cancelOrderTransaction get user info error: %v", err)
				return err
			}

			updateUserQuery := `UPDATE vh_user SET balance = balance + ? WHERE id = ?`
			_, err = tx.ExecContext(ctx, updateUserQuery, mainOrder.DeductibleAmount, mainOrder.Uid)
			if err != nil {
				l.Logger.Errorf("cancelOrderTransaction rollback user balance error: %v", err)
				return err
			}

			// 记录礼金返还的余额历史
			uniqueCode := mainOrder.MainOrderNo + "_cancel_refund"
			afterAmount := user.Balance + mainOrder.DeductibleAmount
			_, err = l.svcCtx.BalanceHistory.InsertTx(ctx, tx, &model.VhBalanceHistory{
				UniqueCode:    uniqueCode,
				Uid:           uint64(mainOrder.Uid),
				Type:          1, // 增加
				Amount:        mainOrder.DeductibleAmount,
				AfterAmount:   afterAmount,
				OperationType: 5, // 订单取消退款
				OperationName: "用户",
			})
			if err != nil {
				l.Logger.Errorf("cancelOrderTransaction insert balance history error: %v", err)
				return err
			}

			refundAmount = mainOrder.DeductibleAmount
			l.Logger.Infof("用户手动取消订单返还礼金: uid=%d, order_no=%s, amount=%.2f, after_balance=%.2f",
				mainOrder.Uid, mainOrder.MainOrderNo, mainOrder.DeductibleAmount, afterAmount)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	// 清理用户缓存（如果返还了礼金）
	if mainOrder.DeductibleAmount > 0 {
		user, err := l.svcCtx.UserModel.FindOne(l.ctx, mainOrder.Uid)
		if err == nil {
			_ = l.svcCtx.UserModel.ClearCache(l.ctx, user)
		}
	}

	return refundAmount, nil
}
