package decrypt

import (
	"net/http"

	"engine/api/internal/logic/decrypt"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ShortUrlRedirectHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ShortUrlRedirectReq
		if err := httpx.Parse(r, &req); err != nil {
			// 参数错误也不暴露具体信息
			result.HttpResult(r, w, nil, xerr.NewErrMsg("参数错误"))
			return
		}

		l := decrypt.NewShortUrlRedirectLogic(r.Context(), svcCtx)
		resp, err := l.ShortUrlRedirect(&req)
		if err != nil {
			// 处理失败，返回错误信息
			result.HttpResult(r, w, nil, err)
			return
		}

		// 返回最终URL
		result.HttpResult(r, w, resp, nil)
	}
}
