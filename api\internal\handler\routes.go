// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package handler

import (
	"net/http"
	"time"

	adminbalance "engine/api/internal/handler/admin/balance"
	adminbanner "engine/api/internal/handler/admin/banner"
	adminbox "engine/api/internal/handler/admin/box"
	admingoods "engine/api/internal/handler/admin/goods"
	adminlabel "engine/api/internal/handler/admin/label"
	adminlotteryActivity "engine/api/internal/handler/admin/lotteryActivity"
	adminorder "engine/api/internal/handler/admin/order"
	adminpay "engine/api/internal/handler/admin/pay"
	adminuser "engine/api/internal/handler/admin/user"
	adminuser_level "engine/api/internal/handler/admin/user_level"
	decrypt "engine/api/internal/handler/decrypt"
	minibox "engine/api/internal/handler/mini/box"
	minigoods "engine/api/internal/handler/mini/goods"
	minimember "engine/api/internal/handler/mini/member"
	miniorder "engine/api/internal/handler/mini/order"
	minipartner "engine/api/internal/handler/mini/partner"
	miniprogramaddress "engine/api/internal/handler/miniprogram/address"
	miniprogrambalance "engine/api/internal/handler/miniprogram/balance"
	miniprogrambanner "engine/api/internal/handler/miniprogram/banner"
	miniprogramlotteryActivity "engine/api/internal/handler/miniprogram/lotteryActivity"
	miniprogramshare "engine/api/internal/handler/miniprogram/share"
	miniprogramuser "engine/api/internal/handler/miniprogram/user"
	ordernotify "engine/api/internal/handler/order/notify"
	pay "engine/api/internal/handler/pay"
	wechat "engine/api/internal/handler/wechat"
	winenotes "engine/api/internal/handler/winenotes"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/withdrawalList",
					Handler: adminbalance.WithdrawalListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/withdrawalReview",
					Handler: adminbalance.WithdrawalReviewHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/balance"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminbanner.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminbanner.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminbanner.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminbanner.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminbanner.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/banner"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/bind_goods",
					Handler: adminbox.BindGoodsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/card_export",
					Handler: adminbox.CardExportHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/card_list",
					Handler: adminbox.CardListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminbox.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminbox.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminbox.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/generate_cards",
					Handler: adminbox.GenerateCardsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminbox.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/repeal_card",
					Handler: adminbox.RepealCardHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/set_onsale_status",
					Handler: adminbox.SetOnsaleStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminbox.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/box"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/copy",
					Handler: admingoods.AdminCopyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: admingoods.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: admingoods.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: admingoods.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: admingoods.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/set_onsale_status",
					Handler: admingoods.SetOnsaleStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: admingoods.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/goods"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminlabel.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminlabel.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminlabel.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminlabel.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/simple_list",
					Handler: adminlabel.SimpleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminlabel.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/label"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminlotteryActivity.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminlotteryActivity.ListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminlotteryActivity.UpdateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/updateStatus",
					Handler: adminlotteryActivity.UpdateStatusHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/order"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminorder.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/refund",
					Handler: adminorder.RefundHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/repush_erp",
					Handler: adminorder.RepushErpHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/order"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/direct_refund",
					Handler: adminpay.DirectRefundHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/pay"),
		rest.WithTimeout(30000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminuser.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/set_disabled",
					Handler: adminuser.SetDisabledHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/user"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminuser_level.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminuser_level.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminuser_level.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminuser_level.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminuser_level.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/user_level"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/card",
					Handler: decrypt.DecryptAESHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/card_url",
					Handler: decrypt.DecryptAESUrlHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/short_url_redirect",
					Handler: decrypt.ShortUrlRedirectHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/rsa",
					Handler: decrypt.DecryptRSAHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/decrypt"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/gift",
					Handler: minibox.GiftHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/gift_accept",
					Handler: minibox.GiftAcceptHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/gift_cancel",
					Handler: minibox.GiftCancelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/gift_reject",
					Handler: minibox.GiftRejectHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/redeem",
					Handler: minibox.RedeemHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/box"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.ExistAuth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/gift_detail",
					Handler: minibox.GiftDetailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/box"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/claim",
					Handler: minibox.ClaimHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/box"),
		rest.WithTimeout(30000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.ExistAuth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: minigoods.MiniDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: minigoods.MiniListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/goods"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/apply",
					Handler: minimember.ApplyMemberHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/member"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/cancel",
					Handler: miniorder.CancelOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/confirm_receipt",
					Handler: miniorder.ConfirmReceiptHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: miniorder.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: miniorder.DetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/get_order_detail_path",
					Handler: miniorder.GetOrderDetailPathHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: miniorder.ListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/payment_status",
					Handler: miniorder.PaymentStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update_order_detail_path",
					Handler: miniorder.UpdateOrderDetailPathHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/wechat_pay",
					Handler: miniorder.WechatPayHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/order"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/apply",
					Handler: minipartner.ApplyPartnerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tasks",
					Handler: minipartner.PartnerTasksHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/mini/partner"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: miniprogramaddress.AddressCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: miniprogramaddress.AddressDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: miniprogramaddress.AddressDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: miniprogramaddress.AddressListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: miniprogramaddress.AddressUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/address"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/getUserWithdrawalAccount",
					Handler: miniprogrambalance.GetUserWithdrawalAccountHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/withdrawalAccountUpdate",
					Handler: miniprogrambalance.WithdrawalAccountUpdateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/withdrawalCreate",
					Handler: miniprogrambalance.WithdrawalCreateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/balance"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: miniprogrambanner.MiniProgramListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/banner"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.ExistAuth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/active",
					Handler: miniprogramlotteryActivity.GetActiveHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/participantList",
					Handler: miniprogramlotteryActivity.ParticipantListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/addressUpdate",
					Handler: miniprogramlotteryActivity.AddressUpdateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/isParticipant",
					Handler: miniprogramlotteryActivity.IsParticipantHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/participant",
					Handler: miniprogramlotteryActivity.ParticipantHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/participantAddress",
					Handler: miniprogramlotteryActivity.ParticipantAddressHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.ExistAuth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: miniprogramshare.DetailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/share"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: miniprogramshare.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/myShare",
					Handler: miniprogramshare.MyShareHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/myShareList",
					Handler: miniprogramshare.MyShareListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shareBuyList",
					Handler: miniprogramshare.ShareBuyListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/share"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/generateTitle",
					Handler: miniprogramshare.GenerateTitleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/refineTitle",
					Handler: miniprogramshare.RefineTitleHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/share"),
		rest.WithTimeout(30000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/wechat_login",
					Handler: miniprogramuser.WeChatLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/user"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/info",
					Handler: miniprogramuser.GetUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/logout",
					Handler: miniprogramuser.LogoutHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update_info",
					Handler: miniprogramuser.UpdateUserInfoHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/user"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/shipment",
					Handler: ordernotify.ShipmentNotifyHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/order/notify"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/wechat_callback",
					Handler: pay.WechatCallbackHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/pay"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/applet_oneclick_login",
					Handler: wechat.AppletOneclickLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/wechat"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					// 通过UUID转换WineNotes为HTML
					Method:  http.MethodPost,
					Path:    "/winenotes/convert",
					Handler: winenotes.ConvertWineNotesHandler(serverCtx),
				},
				{
					// 处理商品的WineNotes数据
					Method:  http.MethodPost,
					Path:    "/winenotes/goods",
					Handler: winenotes.ProcessGoodsWineNotesHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					// 后台通过UUID转换WineNotes为HTML
					Method:  http.MethodPost,
					Path:    "/winenotes/convert",
					Handler: winenotes.AdminConvertWineNotesHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin"),
		rest.WithTimeout(30000*time.Millisecond),
	)
}
