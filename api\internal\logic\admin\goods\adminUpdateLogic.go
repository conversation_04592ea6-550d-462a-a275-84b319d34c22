package goods

import (
	"context"
	"database/sql"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminUpdateLogic {
	return &AdminUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminUpdateLogic) AdminUpdate(req *types.GoodsUpdateReq) error {
	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 检查商品是否存在
	existGoods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminUpdate GoodsModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if existGoods.DeleteTime > 0 {
		return xerr.NewErrMsg("商品已被删除")
	}

	// 处理排序字段：如果传入的 sort 为 0，则保持原有排序值
	sortValue := req.Sort
	if sortValue == 0 {
		sortValue = existGoods.Sort
	}

	// 更新商品信息
	goods := &model.VhGoods{
		Id:               uint64(req.Id),
		Title:            req.Title,
		Brief:            req.Brief,
		Type:             req.Type,
		ItemsInfo:        sql.NullString{String: req.ItemsInfo, Valid: req.ItemsInfo != ""},
		Price:            req.Price,
		Inventory:        uint64(req.Inventory),
		ErpAmount:        req.ErpAmount,
		CashbackAmount:   req.CashbackAmount,
		DeductibleAmount: req.Price, // 自动设置为等于价格
		ProductImg:       req.ProductImg,
		AvatarImage:      req.AvatarImage,
		Detail:           sql.NullString{String: req.Detail, Valid: req.Detail != ""},
		AiResource:       sql.NullString{String: req.AiResource, Valid: req.AiResource != ""},
		OnsaleStatus:     req.OnsaleStatus,
		WarehouseCode:    existGoods.WarehouseCode,
		OnsaleTime:       existGoods.OnsaleTime,
		SoldOutTime:      existGoods.SoldOutTime,
		Purchased:        existGoods.Purchased,
		SalesUserNum:     existGoods.SalesUserNum,
		Pv:               existGoods.Pv,
		DeleteTime:       existGoods.DeleteTime,
		Sort:             sortValue,
		VhUid:            adminUid,
		VhVosName:        sql.NullString{String: adminVosName, Valid: adminVosName != ""},
		// CreatedTime 和 UpdateTime 由数据库自动维护，不需要手动设置
	}

	// 记录上下架时间变化
	now := time.Now().Unix()
	if existGoods.OnsaleStatus != req.OnsaleStatus {
		if req.OnsaleStatus == 2 { // 上架
			goods.OnsaleTime = now
		} else { // 下架
			goods.SoldOutTime = now
		}
	}

	err = l.svcCtx.GoodsModel.Update(l.ctx, goods)
	if err != nil {
		l.Logger.Error("AdminUpdate GoodsModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 更新商品标签：先删除原有标签，再插入新标签
	err = l.svcCtx.GoodsLabelModel.DeleteByPeriodId(l.ctx, req.Id)
	if err != nil {
		l.Logger.Error("AdminUpdate GoodsLabelModel.DeleteByPeriodId error: %v", err)
		// 继续执行，不中断
	}

	// 插入新标签
	if len(req.Labels) > 0 {
		for _, label := range req.Labels {
			goodsLabel := &model.VhGoodsLabel{
				PeriodId: sql.NullInt64{Int64: req.Id, Valid: true},
				LabelId:  sql.NullInt64{Int64: label.LabelId, Valid: true},
				Name:     label.Name,
			}
			_, err = l.svcCtx.GoodsLabelModel.Insert(l.ctx, goodsLabel)
			if err != nil {
				l.Logger.Error("AdminUpdate GoodsLabelModel.Insert error: %v", err)
				// 继续处理其他标签，不中断
			}
		}
	}

	return nil
}
