syntax = "v1"

info(
    title: "会员申请接口"
    desc: "会员申请相关接口"
    author: "system"
    email: ""
    version: "v1"
)

type (
    // 申请会员请求
    ApplyMemberReq {
        Platform string `json:"platform" validate:"required" v:"平台名称"`
        OrderNo  string `json:"order_no" validate:"required" v:"订单号"`
        Phone    string `json:"phone" validate:"required" v:"手机号"`
    }

    // 申请会员响应
    ApplyMemberResp {
        Message string `json:"message"`
    }
)

// 小程序会员申请接口
@server(
    middleware: Global,Auth
    group: mini/member
    prefix: /mulandoGreateDestiny/v1/mini/member
    timeout: 10s
)
service mulandoGreateDestiny {
    // 申请会员
    @handler ApplyMember
    post /apply (ApplyMemberReq) returns (ApplyMemberResp)
}
