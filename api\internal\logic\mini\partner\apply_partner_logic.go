package partner

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ApplyPartnerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewApplyPartnerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApplyPartnerLogic {
	return &ApplyPartnerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApplyPartnerLogic) ApplyPartner(req *types.ApplyPartnerReq) (resp *types.ApplyPartnerResp, err error) {
	// todo: add your logic here and delete this line

	return
}
