package share

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MyShareListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMyShareListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MyShareListLogic {
	return &MyShareListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MyShareListLogic) MyShareList(req *types.MyShareListReq) (resp *types.MyShareListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
