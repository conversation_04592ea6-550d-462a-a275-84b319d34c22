package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftCancelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftCancelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftCancelLogic {
	return &GiftCancelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftCancelLogic) GiftCancel(req *types.BoxGiftCancelReq) error {
	// todo: add your logic here and delete this line

	return nil
}
