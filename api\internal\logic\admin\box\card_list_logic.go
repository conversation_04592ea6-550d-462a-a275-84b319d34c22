package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CardListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCardListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CardListLogic {
	return &CardListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CardListLogic) CardList(req *types.BoxCardListReq) (resp *types.BoxCardListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
