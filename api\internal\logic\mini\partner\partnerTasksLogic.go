package partner

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type PartnerTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPartnerTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PartnerTasksLogic {
	return &PartnerTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PartnerTasksLogic) PartnerTasks(req *types.PartnerTasksReq) (resp *types.PartnerTasksResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	// 1. 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("用户不存在")
		}
		l.Logger.Errorf("PartnerTasks UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 2. 查询该等级的所有任务
	tasks, err := l.getUserLevelTasks(req.Level)
	if err != nil {
		l.Logger.Errorf("PartnerTasks getUserLevelTasks error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 3. 验证任务完成情况
	taskInfos, allCompleted, err := l.validateTasks(user, tasks)
	if err != nil {
		l.Logger.Errorf("PartnerTasks validateTasks error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	return &types.PartnerTasksResp{
		Tasks:        taskInfos,
		AllCompleted: allCompleted,
	}, nil
}

// getUserLevelTasks 获取指定等级的所有任务
func (l *PartnerTasksLogic) getUserLevelTasks(level int64) ([]*model.UserLevelTask, error) {
	builder := l.svcCtx.UserLevelTaskModel.RowBuilder().Where(squirrel.Eq{"level": level})
	var tasks []*model.UserLevelTask
	err := l.svcCtx.UserLevelTaskModel.FindRows(l.ctx, builder, &tasks)
	return tasks, err
}

// validateTasks 验证任务完成情况
func (l *PartnerTasksLogic) validateTasks(user *model.VhUser, tasks []*model.UserLevelTask) ([]types.TaskInfo, bool, error) {
	var taskInfos []types.TaskInfo
	allCompleted := true

	for _, task := range tasks {
		var completed int64
		var err error

		switch task.Type {
		case 1: // 分享任务
			completed, err = l.getShareCount(user.Id, user.LevelStartTime)
			if err != nil {
				return nil, false, err
			}
		case 2: // 考试任务
			completed, err = l.getBadgeCount(user.Id)
			if err != nil {
				return nil, false, err
			}
		default:
			l.Logger.Infof("Unknown task type: %d", task.Type)
			completed = 0
		}

		// 如果完成数量大于任务要求数量，则返回任务数量
		if completed > task.Num {
			completed = task.Num
		}

		status := "completed"
		if completed < task.Num {
			status = "incomplete"
			allCompleted = false
		}

		taskInfos = append(taskInfos, types.TaskInfo{
			TaskId:    task.Id,
			TaskName:  task.Name,
			TaskType:  task.Type,
			Required:  task.Num,
			Completed: completed,
			Status:    status,
		})
	}

	return taskInfos, allCompleted, nil
}

// getShareCount 获取分享数量
func (l *PartnerTasksLogic) getShareCount(uid int64, startTime int64) (int64, error) {
	builder := squirrel.Select("COUNT(*)").
		From(l.svcCtx.ShareModel.TableName()).
		Where(squirrel.Eq{"uid": uid}).
		Where(squirrel.GtOrEq{"create_time": startTime})

	count, err := l.svcCtx.ShareModel.FindCount(l.ctx, builder)
	return count, err
}

// getBadgeCount 获取用户在vh_academy数据库中的证书数量
func (l *PartnerTasksLogic) getBadgeCount(uid int64) (int64, error) {
	query := "SELECT COUNT(*) FROM vh_user_badge WHERE uid = ?"
	var count int64
	err := l.svcCtx.AcademyDB.QueryRowCtx(l.ctx, &count, query, uid)
	if err != nil {
		l.Logger.Errorf("getBadgeCount query error: %v", err)
		return 0, err
	}
	return count, nil
}
