package lotteryActivity

import (
	"context"
	"engine/api/internal/logic"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateLogic) Create(req *types.LotteryActivityCreateReq) error {
	// 解析开始时间
	startTime, err := common.ParseTime(req.StartTime)
	if err != nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "解析开始时间失败:"+err.<PERSON>rror())
	}

	if req.Status == 1 {
		//查询是否有已开启的活动
		err = logic.HasActiveActivity(l.ctx, l.svcCtx)
		if err != nil {
			return err
		}
	}

	// 创建抽奖活动数据
	activity := &model.VhLotteryActivity{
		Title:       req.Title,
		Describe:    req.Describe,
		Status:      req.Status,
		StartTime:   startTime,
		GoodsId:     req.GoodsId,
		GoodsTitle:  req.GoodsTitle,
		GoodsImg:    req.GoodsImg,
		Total:       req.Total,
		WinnerCount: req.WinnerCount,
	}

	// 插入数据库
	_, err = l.svcCtx.LotteryActivityModel.Insert(l.ctx, activity)
	if err != nil {
		l.Errorf("CreateLogic.LotteryActivityModel.Insert:创建抽奖活动失败: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
