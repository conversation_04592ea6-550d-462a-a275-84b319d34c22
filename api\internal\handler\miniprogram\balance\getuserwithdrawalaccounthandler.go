package balance

import (
	"engine/api/internal/logic/miniprogram/balance"
	"engine/api/internal/svc"
	"engine/common/result"
	"net/http"
)

func GetUserWithdrawalAccountHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := balance.NewGetUserWithdrawalAccountLogic(r.Context(), svcCtx)
		resp, err := l.GetUserWithdrawalAccount()
		result.HttpResult(r, w, resp, err)
	}
}
