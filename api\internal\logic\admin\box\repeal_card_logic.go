package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RepealCardLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRepealCardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RepealCardLogic {
	return &RepealCardLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RepealCardLogic) RepealCard(req *types.BoxRepealCardReq) error {
	// todo: add your logic here and delete this line

	return nil
}
