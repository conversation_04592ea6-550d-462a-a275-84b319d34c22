package order

import (
	"context"
	"database/sql"
	"encoding/json"
	"strings"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/golang-jwt/jwt/v4"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateLogic) Create(req *types.OrderCreateReq) (resp *types.OrderCreateResp, err error) {
	// 获取用户信息
	jwtUser := l.ctx.Value("jwt_user").(jwt.MapClaims)
	uid := cast.ToInt64(jwtUser["uid"])

	// 验证商品信息
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.GoodsId))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("商品不存在")
		}
		l.Logger.Error("Create GoodsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证商品状态
	if goods.DeleteTime > 0 {
		return nil, xerr.NewErrMsg("商品不存在")
	}
	if goods.OnsaleStatus != 2 {
		return nil, xerr.NewErrMsg("商品未上架")
	}
	if goods.Type != 1 {
		return nil, xerr.NewErrMsg("该商品不支持普通订单")
	}

	// 验证库存
	if goods.Inventory < uint64(req.Quantity) {
		return nil, xerr.NewErrMsg("商品库存不足")
	}

	// 验证礼金抵扣额度（需要考虑购买数量）
	maxDeductibleAmount := goods.DeductibleAmount * float64(req.Quantity)
	if req.DeductibleAmount > maxDeductibleAmount {
		return nil, xerr.NewErrMsg("礼金抵扣额度超过商品限制")
	}

	// 获取用户信息验证余额
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		l.Logger.Error("Create UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if user.Balance < req.DeductibleAmount {
		return nil, xerr.NewErrMsg("礼金余额不足")
	}

	// 获取购买人角色
	buyerLevel := user.Type

	// 获取分享人角色
	var sharerLevel int64 = 0 // 默认无分享人
	if req.ShareId > 0 {
		share, err := l.svcCtx.ShareModel.FindOne(l.ctx, uint64(req.ShareId))
		if err != nil {
			if err != model.ErrNotFound {
				l.Logger.Error("Create ShareModel.FindOne error: %v", err)
				return nil, xerr.NewErrCode(xerr.DbError)
			}
			// 分享不存在，设置为0
			sharerLevel = 0
		} else {
			// 获取分享人信息
			shareUser, err := l.svcCtx.UserModel.FindOne(l.ctx, int64(share.Uid))
			if err != nil {
				if err != model.ErrNotFound {
					l.Logger.Error("Create ShareUser.FindOne error: %v", err)
					return nil, xerr.NewErrCode(xerr.DbError)
				}
				sharerLevel = 0
			} else {
				sharerLevel = shareUser.Type
			}
		}
	}

	// 验证收货地址
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.AddressId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("收货地址不存在")
		}
		l.Logger.Error("Create UserAddressModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if address.Uid != uid {
		return nil, xerr.NewErrMsg("收货地址不属于当前用户")
	}

	// 计算金额
	totalAmount := goods.Price * float64(req.Quantity)
	cashAmount := totalAmount - req.DeductibleAmount
	if cashAmount < 0 {
		cashAmount = 0
	}
	// payment_amount = cash_amount + deductible_amount (总共需要支付金额)
	paymentAmount := cashAmount + req.DeductibleAmount

	// 生成订单号
	mainOrderNo := function.GenerateMainOrderNo()
	subOrderNo := function.GenerateSubOrderNo()

	// 创建商品快照
	snapshot, err := l.createGoodsSnapshot(goods)
	if err != nil {
		return nil, err
	}

	// 开启事务创建订单
	var mainOrderId int64
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 扣减商品库存
		updateQuery := `UPDATE vh_goods SET inventory = inventory - ? WHERE id = ? AND inventory >= ?`
		result, err := tx.ExecContext(ctx, updateQuery, req.Quantity, req.GoodsId, req.Quantity)
		if err != nil {
			l.Logger.Error("Create update goods inventory error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		affected, _ := result.RowsAffected()
		if affected == 0 {
			return xerr.NewErrMsg("商品库存不足")
		}

		// 扣减用户礼金余额并记录余额历史
		if req.DeductibleAmount > 0 {
			updateUserQuery := `UPDATE vh_user SET balance = balance - ? WHERE id = ? AND balance >= ?`
			result, err = tx.ExecContext(ctx, updateUserQuery, req.DeductibleAmount, uid, req.DeductibleAmount)
			if err != nil {
				l.Logger.Error("Create update user balance error: %v", err)
				return xerr.NewErrCode(xerr.DbError)
			}

			affected, _ = result.RowsAffected()
			if affected == 0 {
				return xerr.NewErrMsg("礼金余额不足")
			}

			// 记录礼金扣除的余额历史
			uniqueCode := mainOrderNo + "_deduct"
			afterAmount := user.Balance - req.DeductibleAmount
			_, err = l.svcCtx.BalanceHistory.InsertTx(ctx, tx, &model.VhBalanceHistory{
				UniqueCode:    uniqueCode,
				Uid:           uint64(uid),
				Type:          2, // 减少
				Amount:        req.DeductibleAmount,
				AfterAmount:   afterAmount,
				OperationType: 3, // 商品购买
				OperationName: "系统",
			})
			if err != nil {
				l.Logger.Error("Create insert balance history error: %v", err)
				return xerr.NewErrCode(xerr.DbError)
			}

			l.Logger.Infof("订单创建扣除礼金: uid=%d, amount=%.2f, after_balance=%.2f", uid, req.DeductibleAmount, afterAmount)
		}

		// 判断是否为100%礼金支付
		isFullGiftPayment := cashAmount == 0
		var mainOrderStatus int64 = 0 // 默认待支付
		var paymentTime int64 = 0

		if isFullGiftPayment {
			mainOrderStatus = 1 // 已支付
			paymentTime = time.Now().Unix()
		}

		// 创建主订单
		mainOrder := &model.VhOrderMain{
			Uid:              uid,
			MainOrderNo:      mainOrderNo,
			MainOrderStatus:  mainOrderStatus,
			PaymentAmount:    paymentAmount, // 总共需要支付金额 = cash_amount + deductible_amount
			PaymentTime:      paymentTime,
			PaymentMethod:    sql.NullInt64{Int64: 4, Valid: true}, // 微信小程序
			DeductibleAmount: req.DeductibleAmount,
			CashAmount:       cashAmount,
			Snapshot:         sql.NullString{}, // 商品订单snapshot为空，只有盲盒订单才使用
			ShareId:          req.ShareId,
		}

		// 使用原生SQL插入主订单（添加 buyer_level 和 sharer_level 字段）
		mainOrderQuery := `INSERT INTO vh_order_main (uid, main_order_no, main_order_status, payment_amount, payment_time, payment_method, deductible_amount, cash_amount, snapshot, share_id, buyer_level, sharer_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
		result, err = tx.ExecContext(ctx, mainOrderQuery,
			mainOrder.Uid, mainOrder.MainOrderNo, mainOrder.MainOrderStatus,
			mainOrder.PaymentAmount, mainOrder.PaymentTime, mainOrder.PaymentMethod.Int64,
			mainOrder.DeductibleAmount, mainOrder.CashAmount, nil, mainOrder.ShareId,
			buyerLevel, sharerLevel)
		if err != nil {
			l.Logger.Error("Create insert main order error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		mainOrderId, _ = result.LastInsertId()

		// 创建子订单
		var subOrderStatus int64 = 0 // 默认待支付
		if isFullGiftPayment {
			subOrderStatus = 1 // 已支付
		}

		subOrder := &model.VhOrder{
			Snapshot:         sql.NullString{String: snapshot, Valid: true}, // 子订单保留商品快照
			Uid:              uid,
			SubOrderNo:       subOrderNo,
			SubOrderStatus:   subOrderStatus,
			MainOrderId:      mainOrderId,
			GoodsId:          req.GoodsId,
			OrderQty:         req.Quantity,
			PaymentAmount:    paymentAmount, // 总共需要支付金额 = cash_amount + deductible_amount
			CashAmount:       cashAmount,
			ErpAmount:        goods.ErpAmount * float64(req.Quantity),
			DeductibleAmount: req.DeductibleAmount,
			ExpressType:      0,
			PaymentTime:      paymentTime,
			ProvinceId:       sql.NullInt64{Int64: address.ProvinceId, Valid: true},
			Province:         sql.NullString{String: address.ProvinceName, Valid: true},
			CityId:           sql.NullInt64{Int64: address.CityId, Valid: true},
			City:             sql.NullString{String: address.CityName, Valid: true},
			DistrictId:       sql.NullInt64{Int64: address.TownId, Valid: true},
			District:         sql.NullString{String: address.TownName, Valid: true},
			Address:          sql.NullString{String: address.Address, Valid: true},
			Consignee:        sql.NullString{String: address.Consignee, Valid: true},
			ConsigneePhone:   sql.NullString{String: address.ConsigneePhone, Valid: true},
			Type:             1, // 普通商品
		}

		// 使用原生SQL插入子订单
		subOrderQuery := `INSERT INTO vh_order (snapshot, uid, sub_order_no, sub_order_status, main_order_id, goods_id, order_qty, payment_amount, cash_amount, erp_amount, deductible_amount, express_type, payment_time, province_id, province, city_id, city, district_id, district, address, consignee, consignee_phone, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
		_, err = tx.ExecContext(ctx, subOrderQuery,
			subOrder.Snapshot.String, subOrder.Uid, subOrder.SubOrderNo, subOrder.SubOrderStatus,
			subOrder.MainOrderId, subOrder.GoodsId, subOrder.OrderQty, subOrder.PaymentAmount,
			subOrder.CashAmount, subOrder.ErpAmount, subOrder.DeductibleAmount, subOrder.ExpressType,
			subOrder.PaymentTime, subOrder.ProvinceId.Int64, subOrder.Province.String,
			subOrder.CityId.Int64, subOrder.City.String, subOrder.DistrictId.Int64, subOrder.District.String,
			subOrder.Address.String, subOrder.Consignee.String, subOrder.ConsigneePhone.String, subOrder.Type)
		if err != nil {
			l.Logger.Error("Create insert sub order error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 如果是100%礼金支付，执行支付成功后的操作
		if isFullGiftPayment {
			// 更新商品销量统计
			updateGoodsQuery := `UPDATE vh_goods SET purchased = purchased + ?, sales_user_num = sales_user_num + 1 WHERE id = ?`
			_, err = tx.ExecContext(ctx, updateGoodsQuery, req.Quantity, req.GoodsId)
			if err != nil {
				l.Logger.Error("Create update goods sales error: %v", err)
				return xerr.NewErrCode(xerr.DbError)
			}

			// 检查并升级用户类型
			err = l.upgradeUserTypeIfNeeded(ctx, tx, uid)
			if err != nil {
				l.Logger.Error("Create upgrade user type error: %v", err)
				// 不返回错误，因为订单创建成功更重要
			}

			l.Logger.Infof("100%礼金支付订单创建成功: main_order_no=%s, sub_order_no=%s", mainOrderNo, subOrderNo)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 清理用户缓存（如果使用了礼金或升级了用户类型）
	if req.DeductibleAmount > 0 || cashAmount == 0 {
		_ = l.svcCtx.UserModel.ClearCache(l.ctx, user)
	}

	// 如果是100%礼金支付，推送订单到中台
	if cashAmount == 0 {
		go func() {
			// 创建独立的context，避免HTTP请求结束后context被取消
			ctx := context.Background()
			// 使用订单推送服务推送到中台
			pushService := service.NewOrderPushService(ctx, l.svcCtx)
			err := pushService.PushOrderToMiddlePlatform(mainOrderNo)
			if err != nil {
				l.Logger.Errorf("100%礼金支付推送订单到中台失败: main_order_no=%s, error=%v", mainOrderNo, err)
			} else {
				l.Logger.Infof("100%礼金支付订单推送中台成功: main_order_no=%s", mainOrderNo)
			}
		}()
	}

	// 计算剩余超时时间（5分钟 = 300秒）
	cancelTimeout := int64(300)

	// 确定订单状态
	var orderStatus int64 = 0 // 默认待支付
	if cashAmount == 0 {
		orderStatus = 1 // 已支付
		cancelTimeout = 0 // 已支付订单无需超时时间
	}

	return &types.OrderCreateResp{
		MainOrderNo:      mainOrderNo,
		SubOrderNo:       subOrderNo,
		PaymentAmount:    paymentAmount, // 总共需要支付金额 = cash_amount + deductible_amount
		CashAmount:       cashAmount,
		DeductibleAmount: req.DeductibleAmount,
		OrderStatus:      orderStatus,
		NeedPayment:      cashAmount > 0,
		CancelTimeout:    cancelTimeout,
	}, nil
}

// createGoodsSnapshot 创建商品快照
func (l *CreateLogic) createGoodsSnapshot(goods *model.VhGoods) (string, error) {
	// 获取商品标签
	labels, err := l.svcCtx.GoodsLabelModel.FindByPeriodId(l.ctx, int64(goods.Id))
	if err != nil {
		l.Logger.Error("createGoodsSnapshot FindByPeriodId error: %v", err)
		// 标签获取失败不影响订单创建，使用空标签
		labels = []*model.VhGoodsLabel{}
	}

	// 构建标签字符串
	var labelNames []string
	for _, label := range labels {
		labelNames = append(labelNames, label.Name)
	}
	labelsStr := strings.Join(labelNames, ",")

	// 解析商品简码信息，使用统一的解析函数
	var itemsInfo []map[string]interface{}
	if goods.ItemsInfo.Valid {
		itemsInfo = function.ParseItemsInfo(goods.ItemsInfo.String)
	}

	// 构建商品快照
	snapshot := map[string]interface{}{
		"avatar_image":      function.NormalizeImagePath(goods.AvatarImage), // 统一存储半路径
		"title":             goods.Title,
		"brief":             goods.Brief,
		"box_id":            0, // 非盲盒取0
		"valid_time_unit":   "", // 非盲盒取空字符串
		"valid_time_num":    0, // 非盲盒取0
		"price":             goods.Price,
		"cashback_amount":   goods.CashbackAmount,
		"erp_amount":        goods.ErpAmount,
		"deductible_amount": goods.DeductibleAmount,
		"product_img":       function.NormalizeImagePath(goods.ProductImg), // 统一存储半路径
		"detail":            goods.Detail,
		"goods_id":          goods.Id,
		"inventory":         goods.Inventory,
		"items_info":        itemsInfo,
		"labels":            labelsStr,
		"snapshot_time":     function.FormatTime(time.Now()),
		"type":              goods.Type,
		"warehouse_code":    goods.WarehouseCode,
	}

	snapshotJson, err := json.Marshal(snapshot)
	if err != nil {
		l.Logger.Error("createGoodsSnapshot json.Marshal error: %v", err)
		return "", xerr.NewErrCode(xerr.ServerCommonError)
	}

	return string(snapshotJson), nil
}

// upgradeUserTypeIfNeeded 检查并升级用户类型
func (l *CreateLogic) upgradeUserTypeIfNeeded(ctx context.Context, tx *sql.Tx, uid int64) error {
	// 查询用户当前类型
	var userType int64
	getUserTypeQuery := `SELECT type FROM vh_user WHERE id = ?`
	err := tx.QueryRowContext(ctx, getUserTypeQuery, uid).Scan(&userType)
	if err != nil {
		l.Logger.Error("Get user type error: %v", err)
		return err
	}

	// 如果用户类型是1（普通用户），升级为2（会员）
	if userType == 1 {
		updateUserTypeQuery := `UPDATE vh_user SET type = 2 WHERE id = ? AND type = 1`
		result, err := tx.ExecContext(ctx, updateUserTypeQuery, uid)
		if err != nil {
			l.Logger.Error("Update user type error: %v", err)
			return err
		}

		affected, _ := result.RowsAffected()
		if affected > 0 {
			l.Logger.Info("User type upgraded from 1 to 2 for uid: %d", uid)

			// 清除用户缓存
			user, err := l.svcCtx.UserModel.FindOne(ctx, uid)
			if err == nil {
				clearErr := l.svcCtx.UserModel.ClearCache(ctx, user)
				if clearErr != nil {
					l.Logger.Error("Clear user cache error: %v", clearErr)
				}
			}
		}
	}

	return nil
}
