package goods

import (
	"net/http"

	"engine/api/internal/logic/admin/goods"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func AdminDeleteHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GoodsDeleteReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := goods.NewAdminDeleteLogic(r.Context(), svcCtx)
		err := l.AdminDelete(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
