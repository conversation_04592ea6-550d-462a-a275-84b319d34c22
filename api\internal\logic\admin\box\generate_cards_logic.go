package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateCardsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateCardsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateCardsLogic {
	return &GenerateCardsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateCardsLogic) GenerateCards(req *types.BoxGenerateCardsReq) (resp *types.BoxGenerateCardsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
