package order

import (
	"context"
	"database/sql"
	"time"

	"engine/api/internal/logic"
	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type ConfirmReceiptLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConfirmReceiptLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConfirmReceiptLogic {
	return &ConfirmReceiptLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConfirmReceiptLogic) ConfirmReceipt(req *types.ConfirmReceiptReq) (resp *types.ConfirmReceiptResp, err error) {
	// 获取用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		l.Logger.Error("uid not found in context")
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询订单信息
	order, err := l.svcCtx.OrderModel.FindOneBySubOrderNo(l.ctx, req.SubOrderNo)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("订单不存在")
		}
		l.Logger.Errorf("ConfirmReceipt FindOneBySubOrderNo error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证订单归属
	if int64(order.Uid) != uid {
		return nil, xerr.NewErrMsg("订单不存在")
	}

	// 验证订单状态 - 只有已发货状态(2)才能确认收货
	if order.SubOrderStatus != 2 {
		return nil, xerr.NewErrMsg("订单状态不允许确认收货")
	}

	// 获取主订单信息以获取share_id
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(l.ctx, order.MainOrderId)
	if err != nil {
		l.Logger.Errorf("ConfirmReceipt FindMainOrder error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 使用事务处理确认收货和分佣
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 更新订单状态为已完成(3)
		now := time.Now()
		updateBuilder := squirrel.Update(l.svcCtx.OrderModel.TableName()).
			Set("sub_order_status", 3).
			Set("goods_receipt_time", now.Unix()). // 使用时间戳
			Set("update_time", now.Format("2006-01-02 15:04:05")).
			Where(squirrel.Eq{"sub_order_no": req.SubOrderNo}).
			Where(squirrel.Eq{"sub_order_status": 2}) // 确保状态是已发货

		_, er := l.svcCtx.OrderModel.UpdateCustomTx(ctx, tx, updateBuilder)
		if er != nil {
			l.Logger.Errorf("ConfirmReceipt UpdateCustomTx error: %v", er)
			return er
		}

		// 确认收货成功后调用分佣方法
		shareId := mainOrder.ShareId
		buyerLevel := mainOrder.BuyerLevel   // 下单时购买人角色
		sharerLevel := mainOrder.SharerLevel // 下单时分享人角色

		er = logic.CashBack(ctx, tx, l.svcCtx, uid, shareId, order.GoodsId, order.SubOrderNo, buyerLevel, sharerLevel, order.OrderQty)
		if er != nil {
			l.Logger.Errorf("ConfirmReceipt CashBack error: %v", er)
			return er
		}

		return nil
	})

	if err != nil {
		l.Logger.Errorf("ConfirmReceipt transaction error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	l.Logger.Infof("用户确认收货成功 - 用户ID: %d, 订单号: %s", uid, req.SubOrderNo)

	// 检查并更新主订单状态
	err = l.checkAndUpdateMainOrderStatus(order)
	if err != nil {
		l.Logger.Errorf("ConfirmReceipt checkAndUpdateMainOrderStatus error: %v", err)
		// 这里不返回错误，因为子订单确认收货已经成功，主订单状态更新失败不影响用户体验
	}

	// 确认收货成功后，通知三方订单完成
	err = l.notifyThirdPartyOrderComplete(order)
	if err != nil {
		l.Logger.Errorf("notifyThirdPartyOrderComplete error: %v", err)
		// 通知三方失败不影响确认收货成功，只记录错误日志
	}

	return &types.ConfirmReceiptResp{
		Message: "确认收货成功",
	}, nil
}

// checkAndUpdateMainOrderStatus 检查并更新主订单状态
func (l *ConfirmReceiptLogic) checkAndUpdateMainOrderStatus(order *model.VhOrder) error {
	// 获取主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(l.ctx, order.MainOrderId)
	if err != nil {
		l.Logger.Errorf("checkAndUpdateMainOrderStatus FindOne error: %v", err)
		return err
	}

	// 根据订单类型判断是否需要更新主订单状态
	switch order.Type {
	case 3: // 盲盒订单
		return l.checkBlindBoxOrderCompletion(mainOrder)
	case 1, 2: // 普通商品订单和抽奖订单
		return l.checkRegularOrderCompletion(mainOrder)
	default:
		l.Logger.Infof("Unknown order type: %d", order.Type)
		return nil
	}
}

// checkBlindBoxOrderCompletion 检查盲盒订单是否完成
func (l *ConfirmReceiptLogic) checkBlindBoxOrderCompletion(mainOrder *model.VhOrderMain) error {
	// 查询盲盒订单的所有项目
	boxItems, err := l.svcCtx.OrderMainBoxModel.FindByMainOrderNo(l.ctx, mainOrder.MainOrderNo)
	if err != nil {
		l.Logger.Errorf("checkBlindBoxOrderCompletion FindByMainOrderNo error: %v", err)
		return err
	}

	// 检查是否所有项目都已领取完成
	allClaimed := true
	for _, item := range boxItems {
		// box_type: 1=待领取, 2=自己领取, 3=赠送
		// 只有 box_type 为 2 或 3 才算已领取
		if !item.BoxType.Valid || (item.BoxType.Int64 != 2 && item.BoxType.Int64 != 3) {
			allClaimed = false
			break
		}
	}

	if !allClaimed {
		l.Logger.Infof("盲盒订单 %s 还有未领取的项目", mainOrder.MainOrderNo)
		return nil
	}

	// 检查所有关联的子订单是否都已确认收货
	subOrders, err := l.svcCtx.OrderModel.FindByMainOrderId(l.ctx, mainOrder.Id)
	if err != nil {
		l.Logger.Errorf("checkBlindBoxOrderCompletion FindByMainOrderId error: %v", err)
		return err
	}

	allConfirmed := true
	for _, subOrder := range subOrders {
		if subOrder.SubOrderStatus != 3 { // 3=已完成
			allConfirmed = false
			break
		}
	}

	if allConfirmed {
		return l.updateMainOrderToCompleted(mainOrder.MainOrderNo)
	}

	return nil
}

// checkRegularOrderCompletion 检查普通订单是否完成
func (l *ConfirmReceiptLogic) checkRegularOrderCompletion(mainOrder *model.VhOrderMain) error {
	// 查询主订单下的所有子订单
	subOrders, err := l.svcCtx.OrderModel.FindByMainOrderId(l.ctx, mainOrder.Id)
	if err != nil {
		l.Logger.Errorf("checkRegularOrderCompletion FindByMainOrderId error: %v", err)
		return err
	}

	// 检查是否所有子订单都已确认收货
	allConfirmed := true
	for _, subOrder := range subOrders {
		if subOrder.SubOrderStatus != 3 { // 3=已完成
			allConfirmed = false
			break
		}
	}

	if allConfirmed {
		return l.updateMainOrderToCompleted(mainOrder.MainOrderNo)
	}

	return nil
}

// updateMainOrderToCompleted 更新主订单状态为已完成
func (l *ConfirmReceiptLogic) updateMainOrderToCompleted(mainOrderNo string) error {
	now := time.Now()
	updateBuilder := squirrel.Update(l.svcCtx.OrderMainModel.TableName()).
		Set("main_order_status", 3). // 3=已完成
		Set("update_time", now.Format("2006-01-02 15:04:05")).
		Where(squirrel.Eq{"main_order_no": mainOrderNo}).
		Where(squirrel.Eq{"main_order_status": 2}) // 确保当前状态是已发货

	_, err := l.svcCtx.OrderMainModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Errorf("updateMainOrderToCompleted UpdateCustom error: %v", err)
		return err
	}

	l.Logger.Infof("主订单状态更新为已完成 - 订单号: %s", mainOrderNo)
	return nil
}

// notifyThirdPartyOrderComplete 通知三方订单完成
func (l *ConfirmReceiptLogic) notifyThirdPartyOrderComplete(order *model.VhOrder) error {
	// 获取主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(l.ctx, order.MainOrderId)
	if err != nil {
		l.Logger.Errorf("FindOne main order error: %v", err)
		return err
	}

	l.Logger.Infof("订单确认收货完成，准备通知三方: main_order_no=%s, sub_order_no=%s",
		mainOrder.MainOrderNo, order.SubOrderNo)

	// 使用订单推送服务通知三方订单完成
	// 这里可以创建一个专门的订单完成通知服务，或者复用现有的推送服务
	// 为了简化，我们先使用现有的推送服务，后续可以根据需要创建专门的完成通知服务
	pushService := service.NewOrderPushService(l.ctx, l.svcCtx)

	// 这里应该调用专门的订单完成通知方法，暂时使用推送方法作为占位符
	// 实际实现中，可能需要调用不同的API端点来通知订单完成
	err = pushService.NotifyOrderComplete(mainOrder.MainOrderNo)
	if err != nil {
		l.Logger.Errorf("通知三方订单完成失败: main_order_no=%s, sub_order_no=%s, error=%v",
			mainOrder.MainOrderNo, order.SubOrderNo, err)
		return err
	}

	l.Logger.Infof("通知三方订单完成成功: main_order_no=%s, sub_order_no=%s",
		mainOrder.MainOrderNo, order.SubOrderNo)
	return nil
}
