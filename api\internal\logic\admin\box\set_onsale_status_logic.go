package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetOnsaleStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetOnsaleStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetOnsaleStatusLogic {
	return &SetOnsaleStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetOnsaleStatusLogic) SetOnsaleStatus(req *types.BoxSetOnsaleStatusReq) error {
	// todo: add your logic here and delete this line

	return nil
}
