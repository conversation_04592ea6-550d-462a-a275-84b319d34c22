package order

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/wechat"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateOrderDetailPathLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateOrderDetailPathLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateOrderDetailPathLogic {
	return &UpdateOrderDetailPathLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateOrderDetailPathLogic) UpdateOrderDetailPath(req *types.UpdateOrderDetailPathReq) (resp *types.UpdateOrderDetailPathResp, err error) {
	// 验证路径格式
	if !strings.Contains(req.Path, "${商品订单号}") {
		return &types.UpdateOrderDetailPathResp{
			ErrorCode: 10060035,
			ErrorMsg:  "请检查path是否有误，必须包含${商品订单号}",
		}, nil
	}

	// 获取access_token
	accessToken, err := l.getAccessToken()
	if err != nil {
		l.Logger.Errorf("获取access_token失败: %v", err)
		return &types.UpdateOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
		}, nil
	}

	// 调用微信API
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/sec/order/update_order_detail_path?access_token=%s", accessToken)

	requestBody := map[string]interface{}{
		"path": req.Path,
	}

	bodyBytes, _ := json.Marshal(requestBody)

	httpResp, err := http.Post(url, "application/json", bytes.NewReader(bodyBytes))
	if err != nil {
		l.Logger.Errorf("调用微信API失败: %v", err)
		return &types.UpdateOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
		}, nil
	}
	defer httpResp.Body.Close()

	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		l.Logger.Errorf("读取微信API响应失败: %v", err)
		return &types.UpdateOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
		}, nil
	}

	var wechatResp types.UpdateOrderDetailPathResp
	if err := json.Unmarshal(respBody, &wechatResp); err != nil {
		l.Logger.Errorf("解析微信API响应失败: %v, body: %s", err, string(respBody))
		return &types.UpdateOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
		}, nil
	}

	l.Logger.Infof("配置订单详情路径: path=%s, result: %+v", req.Path, wechatResp)
	return &wechatResp, nil
}

// getAccessToken 获取微信access_token
func (l *UpdateOrderDetailPathLogic) getAccessToken() (string, error) {
	// 使用系统统一的微信服务获取AccessToken
	weChatService := wechat.NewWeChatService(l.svcCtx.Config)

	res, err := weChatService.GetAccessToken()
	if err != nil {
		l.Logger.Errorf("GetAccessToken failed: %v", err)
		return "", xerr.NewErrMsg("获取access_token失败: " + err.Error())
	}

	if res.AccessToken == "" {
		l.Logger.Errorf("AccessToken is empty: code=%d, msg=%s", res.Code, res.Msg)
		return "", xerr.NewErrMsg("access_token为空: " + res.Msg)
	}

	return res.AccessToken, nil
}
