package balance

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalReviewLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalReviewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalReviewLogic {
	return &WithdrawalReviewLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalReviewLogic) WithdrawalReview(req *types.WithdrawalReviewReq) error {
	// todo: add your logic here and delete this line

	return nil
}
