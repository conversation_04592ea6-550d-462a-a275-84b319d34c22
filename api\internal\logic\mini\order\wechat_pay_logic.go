package order

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WechatPayLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWechatPayLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WechatPayLogic {
	return &WechatPayLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WechatPayLogic) WechatPay(req *types.WechatPayReq) (resp *types.WechatPayResp, err error) {
	// todo: add your logic here and delete this line

	return
}
