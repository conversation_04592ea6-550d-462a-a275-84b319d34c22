package lotteryActivity

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetActiveLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetActiveLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetActiveLogic {
	return &GetActiveLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetActiveLogic) GetActive() (resp *types.ActiveLotteryActivityResp, err error) {
	// todo: add your logic here and delete this line

	return
}
