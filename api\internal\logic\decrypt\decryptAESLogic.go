package decrypt

import (
	"context"
	"engine/common/xerr"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptAESLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptAESLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptAESLogic {
	return &DecryptAESLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptAESLogic) DecryptAES(req *types.AESDecryptReq) (resp *types.AESDecryptResp, err error) {
	// 创建解密服务
	decryptService := service.NewDecryptService(l.svcCtx.Config)

	l.Logger.Errorf("AES 接收到的请求数据: %s", req.Q)

	// 调用AES解密服务
	decryptedData, err := decryptService.DecryptAES(req.Q)
	if err != nil {
		// 记录详细错误信息到日志，但不暴露给用户
		l.Logger.Errorf("AES解密失败 - 加密数据: %s, 错误详情: %v", req.Q, err)
		return nil, xerr.NewErrMsg("操作失败")
	}

	// 记录成功的解密操作
	l.Logger.Infof("AES解密成功 - 加密数据: %s, 解密结果: %s", req.Q, decryptedData)

	return &types.AESDecryptResp{
		DecryptedData: decryptedData,
	}, nil
}
