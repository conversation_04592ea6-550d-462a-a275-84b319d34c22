package lotteryActivity

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantLogic {
	return &ParticipantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantLogic) Participant(req *types.ActiveLotteryActivityParticipantReq) error {
	// todo: add your logic here and delete this line

	return nil
}
