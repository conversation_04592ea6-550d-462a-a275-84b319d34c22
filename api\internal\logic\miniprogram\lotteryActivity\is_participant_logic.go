package lotteryActivity

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type IsParticipantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewIsParticipantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *IsParticipantLogic {
	return &IsParticipantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *IsParticipantLogic) IsParticipant(req *types.ActiveLotteryActivityisParticipantReq) (resp *types.ActiveLotteryActivityisParticipantResp, err error) {
	// todo: add your logic here and delete this line

	return
}
