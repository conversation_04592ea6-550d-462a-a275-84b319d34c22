package address

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressDetailLogic {
	return &AddressDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressDetailLogic) AddressDetail(req *types.AddressDetailReq) (resp *types.UserAddressInfo, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询地址信息
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AddressDetail UserAddressModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否是当前用户的地址
	if address.Uid != uid {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 转换数据 - 临时兼容性修复
	addressInfo := &types.UserAddressInfo{
		Id:             address.Id,
		Uid:            address.Uid,
		ProvinceId:     address.ProvinceId,
		CityId:         address.CityId,
		TownId:         address.TownId,
		Address:        address.Address,
		IsDefault:      address.IsDefault,
		Label:          address.Label,
		Code:           address.Code,
		Consignee:      address.Consignee,
		ConsigneePhone: address.ConsigneePhone,
		ProvinceName:   address.ProvinceName,
		CityName:       address.CityName,
		TownName:       address.TownName,
		CreatedTime:    common.TimeToString(address.CreatedTime),
		UpdateTime:     common.TimeToString(address.UpdateTime),
	}

	return addressInfo, nil
}
