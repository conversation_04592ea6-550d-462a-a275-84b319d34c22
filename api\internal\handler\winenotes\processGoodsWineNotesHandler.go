package winenotes

import (
	"net/http"

	"engine/api/internal/logic/winenotes"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ProcessGoodsWineNotesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GoodsWineNotesReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := winenotes.NewProcessGoodsWineNotesLogic(r.Context(), svcCtx)
		resp, err := l.ProcessGoodsWineNotes(&req)
		result.HttpResult(r, w, resp, err)
	}
}
