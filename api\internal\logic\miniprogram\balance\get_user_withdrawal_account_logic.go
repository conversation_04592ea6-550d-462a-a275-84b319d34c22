package balance

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserWithdrawalAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserWithdrawalAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserWithdrawalAccountLogic {
	return &GetUserWithdrawalAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserWithdrawalAccountLogic) GetUserWithdrawalAccount() (resp *types.GetUserWithdrawalAccountResp, err error) {
	// todo: add your logic here and delete this line

	return
}
