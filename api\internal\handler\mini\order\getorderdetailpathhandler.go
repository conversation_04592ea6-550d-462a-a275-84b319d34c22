package order

import (
	"net/http"

	"engine/api/internal/logic/mini/order"
	"engine/api/internal/svc"
	"engine/common/result"
)

func GetOrderDetailPathHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := order.NewGetOrderDetailPathLogic(r.Context(), svcCtx)
		resp, err := l.GetOrderDetailPath()
		result.HttpResult(r, w, resp, err)
	}
}
