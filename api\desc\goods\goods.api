syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    // 商品标签信息
    GoodsLabelInfo {
        LabelId int64 `json:"label_id"`
        Name string `json:"name"`
    }

    // 商品产品信息
    ProductInfo {
        Code string `json:"code"` // 简码
        Quantity int64 `json:"quantity"` // 数量
    }

    // 商品信息
    GoodsInfo {
        Id int64 `json:"id"`
        Title string `json:"title"`
        Brief string `json:"brief"`
        Type int64 `json:"type"`
        ItemsInfo string `json:"items_info"`
        Price float64 `json:"price"`
        Inventory int64 `json:"inventory"`
        ErpAmount float64 `json:"erp_amount"`
        CashbackAmount float64 `json:"cashback_amount"`
        DeductibleAmount float64 `json:"deductible_amount"`
        ProductImg string `json:"product_img"`
        AvatarImage string `json:"avatar_image"`
        Detail string `json:"detail"`
        OnsaleStatus int64 `json:"onsale_status"`
        WarehouseCode string `json:"warehouse_code"`
        OnsaleTime int64 `json:"onsale_time"`
        SoldOutTime int64 `json:"sold_out_time"`
        Purchased int64 `json:"purchased"`
        SalesUserNum int64 `json:"sales_user_num"`
        Pv int64 `json:"pv"`
        Sort int64 `json:"sort"`
        VhUid int64 `json:"vh_uid"`
        VhVosName string `json:"vh_vos_name"`
        CreatedTime string `json:"created_time"`
        UpdateTime string `json:"update_time"`
        Labels []GoodsLabelInfo `json:"labels"`
        Products []ProductInfo `json:"products"` // 商品产品列表
    }

    // 后台商品列表请求
    AdminGoodsListReq {
        Paging
        Title string `form:"title,optional"` // 商品名称筛选，可选
        Type int64 `form:"type,optional"` // 商品类型筛选，可选
        OnsaleStatus int64 `form:"onsale_status,optional"` // 上架状态筛选，可选
        ShortCode string `form:"short_code,optional"` // 商品简码筛选，可选
        StartDate string `form:"start_date,optional"` // 开始日期 (格式: 2025-07-02)
        EndDate string `form:"end_date,optional"` // 结束日期 (格式: 2025-07-04)
    }

    // 后台商品列表响应
    AdminGoodsListResp {
        List []GoodsInfo `json:"list"`
        Total int64 `json:"total"`
    }

    // 商品创建请求
    GoodsCreateReq {
        Title string `json:"title" validate:"required" v:"商品标题"`
        Brief string `json:"brief,optional" v:"商品副标题"`
        Type int64 `json:"type,default=1" validate:"min=1,max=2" v:"商品类型"`
        ItemsInfo string `json:"items_info,optional" v:"商品简码"`
        Price float64 `json:"price,default=0" validate:"min=0" v:"售价"`
        Inventory int64 `json:"inventory,default=0" validate:"min=0" v:"库存"`
        ErpAmount float64 `json:"erp_amount,default=0" validate:"min=0" v:"财务核算金额"`
        CashbackAmount float64 `json:"cashback_amount,default=0" validate:"min=0" v:"返现金额"`
        ProductImg string `json:"product_img" validate:"required" v:"商品图"`
        AvatarImage string `json:"avatar_image" validate:"required" v:"列表图"`
        Detail string `json:"detail,optional" v:"商品描述"`
        OnsaleStatus int64 `json:"onsale_status,default=3" validate:"min=2,max=3" v:"上架状态"`
        Sort int64 `json:"sort,default=1000" validate:"min=0" v:"排序"`
        Labels []GoodsLabelInfo `json:"labels,optional" v:"商品标签"`
    }

    // 商品更新请求
    GoodsUpdateReq {
        IdJ
        Title string `json:"title" validate:"required" v:"商品标题"`
        Brief string `json:"brief,optional" v:"商品副标题"`
        Type int64 `json:"type" validate:"min=1,max=2" v:"商品类型"`
        ItemsInfo string `json:"items_info,optional" v:"商品简码"`
        Price float64 `json:"price" validate:"min=0" v:"售价"`
        Inventory int64 `json:"inventory" validate:"min=0" v:"库存"`
        ErpAmount float64 `json:"erp_amount" validate:"min=0" v:"财务核算金额"`
        CashbackAmount float64 `json:"cashback_amount" validate:"min=0" v:"返现金额"`
        ProductImg string `json:"product_img" validate:"required" v:"商品图"`
        AvatarImage string `json:"avatar_image" validate:"required" v:"列表图"`
        Detail string `json:"detail,optional" v:"商品描述"`
        OnsaleStatus int64 `json:"onsale_status" validate:"min=2,max=3" v:"上架状态"`
        Sort int64 `json:"sort,optional" validate:"omitempty,min=0" v:"排序"`
        Labels []GoodsLabelInfo `json:"labels,optional" v:"商品标签"`
    }

    // 商品复制请求
    GoodsCopyReq {
        IdJ
    }

    // 商品删除请求
    GoodsDeleteReq {
        IdJ
    }

    // 商品详情请求
    GoodsDetailReq {
        IdF
    }

    // 设置上下架状态请求
    GoodsSetOnsaleStatusReq {
        IdJ
        OnsaleStatus int64 `json:"onsale_status" validate:"min=2,max=3" v:"上架状态"`
    }

    // 小程序商品列表请求
    MiniGoodsListReq {
        Paging
        Title string `form:"title,optional"` // 商品名称筛选，可选
        SalesUserNumSort string `form:"sales_user_num_sort,optional,options=asc|desc"` // 购买人数排序，可选
        PriceSort string `form:"price_sort,optional,options=asc|desc"` // 价格排序，可选
        LabelName string `form:"label_name,optional"` // 标签名称筛选，可选
    }

    // 小程序商品列表响应
    MiniGoodsListResp {
        List []MiniGoodsInfo `json:"list"`
        Total int64 `json:"total"`
    }

    // 小程序商品信息
    MiniGoodsInfo {
        Id int64 `json:"id"`
        Title string `json:"title"`
        Brief string `json:"brief"`
        Type int64 `json:"type"`
        Price float64 `json:"price"`
        Inventory int64 `json:"inventory"`
        CashbackAmount float64 `json:"cashback_amount"`
        DeductibleAmount float64 `json:"deductible_amount"`
        AvatarImage string `json:"avatar_image"`
        SalesUserNum int64 `json:"sales_user_num"`
        Labels []GoodsLabelInfo `json:"labels"`
    }

    // 小程序商品详情请求
    MiniGoodsDetailReq {
        IdF
    }

    // 小程序商品详情响应
    MiniGoodsDetailResp {
        Id int64 `json:"id"`
        Title string `json:"title"`
        Brief string `json:"brief"`
        Type int64 `json:"type"`
        ItemsInfo string `json:"items_info"`
        Price float64 `json:"price"`
        Inventory int64 `json:"inventory"`
        ErpAmount float64 `json:"erp_amount"`
        CashbackAmount float64 `json:"cashback_amount"`
        DeductibleAmount float64 `json:"deductible_amount"`
        ProductImg string `json:"product_img"`
        AvatarImage string `json:"avatar_image"`
        Detail string `json:"detail"`
        OnsaleStatus int64 `json:"onsale_status"`
        Purchased int64 `json:"purchased"`
        SalesUserNum int64 `json:"sales_user_num"`
        Pv int64 `json:"pv"`
    }
)

// 后台商品管理接口
@server(
    middleware: Global,Admin
    group: admin/goods
    prefix: /mulandoGreateDestiny/v1/admin/goods
    timeout: 3s
)
service mulandoGreateDestiny {
    // 商品列表
    @handler AdminList
    get /list (AdminGoodsListReq) returns (AdminGoodsListResp)

    // 商品详情
    @handler AdminDetail
    get /detail (GoodsDetailReq) returns (GoodsInfo)

    // 创建商品
    @handler AdminCreate
    post /create (GoodsCreateReq)

    // 更新商品
    @handler AdminUpdate
    post /update (GoodsUpdateReq)

    // 复制商品
    @handler AdminCopy
    post /copy (GoodsCopyReq)

    // 删除商品
    @handler AdminDelete
    post /delete (GoodsDeleteReq)

    // 设置上下架状态
    @handler SetOnsaleStatus
    post /set_onsale_status (GoodsSetOnsaleStatusReq)
}

// 小程序商品接口
@server(
    middleware: Global,ExistAuth
    group: mini/goods
    prefix: /mulandoGreateDestiny/v1/mini/goods
    timeout: 3s
)
service mulandoGreateDestiny {
    // 商品列表
    @handler MiniList
    get /list (MiniGoodsListReq) returns (MiniGoodsListResp)

    // 商品详情
    @handler MiniDetail
    get /detail (MiniGoodsDetailReq) returns (MiniGoodsDetailResp)
}
