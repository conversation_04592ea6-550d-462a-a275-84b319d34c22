syntax = "v1"

info (
    title:   "后台订单管理接口"
    desc:    "后台订单相关接口定义"
    author:  "system"
    version: "v1"
)

import "../common/common.api"

type (
    // 后台订单列表请求
    AdminOrderListReq {
        Page                int64  `form:"page,default=1" validate:"min=1" v:"页码"`
        Limit               int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
        SubOrderNo          string `form:"sub_order_no,optional" v:"订单号(可搜索子订单号和主订单号)"`
        GoodsName           string `form:"goods_name,optional" v:"商品名称"`
        ConsigneeName       string `form:"consignee_name,optional" v:"收货人姓名"`
        ConsigneePhone      string `form:"consignee_phone,optional" v:"收货人手机"`
        SubOrderStatus      int64  `form:"sub_order_status,optional" validate:"omitempty,oneof=0 1 2 3 4" v:"子订单状态"`
        MainOrderStatus     int64  `form:"main_order_status,optional" validate:"omitempty,oneof=0 1 2 3 4" v:"主订单状态"`
        ErpPushStatus       int64  `form:"erp_push_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"ERP推送状态"`
        MiddlePushStatus    int64  `form:"middle_push_status,optional" validate:"omitempty,oneof=0 1 2" v:"推送中台状态"`
        PushZtStatus        int64  `form:"push_zt_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"中台推送状态"`
        PushTStatus         int64  `form:"push_t_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"T+推送状态"`
        GoodsType           int64  `form:"goods_type,optional" validate:"omitempty,oneof=1 2 3" v:"商品类型"`
        BoxId               int64  `form:"box_id,optional" v:"盲盒ID"`
        PaymentTimeStart    string `form:"payment_time_start,optional" v:"支付开始日期"`
        PaymentTimeEnd      string `form:"payment_time_end,optional" v:"支付结束日期"`
    }

    // 后台订单列表响应
    AdminOrderListResp {
        List  []AdminOrderItem `json:"list"`
        Total int64             `json:"total"`
    }

    // 后台订单项
    AdminOrderItem {
        // 订单基本信息
        Id                  int64   `json:"id"`                    // 子订单ID
        SubOrderNo          string  `json:"sub_order_no"`          // 子订单号
        MainOrderNo         string  `json:"main_order_no"`         // 主订单号
        SubOrderStatus      int64   `json:"sub_order_status"`      // 子订单状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消
        MainOrderStatus     int64   `json:"main_order_status"`     // 主订单状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消

        // 商品信息
        GoodsId             int64   `json:"goods_id"`              // 商品ID
        GoodsName           string  `json:"goods_name"`            // 商品名称(从快照获取)
        GoodsType           int64   `json:"goods_type"`            // 商品类型:1=普通商品,2=抽奖商品,3=盲盒商品
        OrderQty            int64   `json:"order_qty"`             // 购买数量

        // 金额信息
        PaymentAmount       float64 `json:"payment_amount"`        // 支付金额
        CashAmount          float64 `json:"cash_amount"`           // 现金支付金额
        DeductibleAmount    float64 `json:"deductible_amount"`     // 礼金抵扣金额
        ErpAmount           float64 `json:"erp_amount"`            // ERP推单金额
        RefundAmount        float64 `json:"refund_amount"`         // 已退款金额
        RefundStatus        int64   `json:"refund_status"`         // 退款状态

        // 时间信息
        CreatedTime         string  `json:"created_time"`          // 下单时间
        PaymentTime         string  `json:"payment_time"`          // 支付时间
        DeliveryTime        string  `json:"delivery_time"`         // 发货时间
        CancelTime          string  `json:"cancel_time"`           // 取消时间

        // 收货人信息
        ConsigneeName       string  `json:"consignee_name"`        // 收货人姓名(原始数据)
        ConsigneePhone      string  `json:"consignee_phone"`       // 收货人电话(原始数据)
        ConsigneeNameMask   string  `json:"consignee_name_mask"`   // 收货人姓名(脱敏)
        ConsigneePhoneMask  string  `json:"consignee_phone_mask"`  // 收货人电话(脱敏)
        Province            string  `json:"province"`              // 省份
        City                string  `json:"city"`                  // 城市
        District            string  `json:"district"`              // 区县
        Address             string  `json:"address"`               // 详细地址

        // 物流信息
        ExpressType         int64   `json:"express_type"`          // 快递方式
        ExpressNumber       string  `json:"express_number"`        // 快递单号

        // 推送状态
        PushTStatus         int64   `json:"push_t_status"`         // T+推送状态:0=未推送,1=推送成功,2=推送失败
        PushWmsStatus       int64   `json:"push_wms_status"`       // 萌芽推送状态:0=未推送,1=推送成功,2=推送失败
        PushZtStatus        int64   `json:"push_zt_status"`        // 中台推送状态:0=未推送,1=推送成功,2=推送失败

        // 支付信息
        PaymentMethod       int64   `json:"payment_method"`        // 支付方式
        Tradeno             string  `json:"tradeno"`               // 支付流水号

        // 其他信息
        Remarks             string  `json:"remarks"`               // 订单备注
        WarehouseCode       string  `json:"warehouse_code"`        // 仓库编码
    }

    // 订单重推ERP请求
    OrderRepushErpReq {
        SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
    }

    // 订单重推ERP响应
    OrderRepushErpResp {
        ErrorCode int64  `json:"error_code"`
        ErrorMsg  string `json:"error_msg"`
        Data      bool   `json:"data"`
    }

    // 订单退款请求
    OrderRefundReq {
        SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
        Reason     string `json:"reason,optional" v:"退款原因"`
    }

    // 订单退款响应
    OrderRefundResp {
        ErrorCode int64  `json:"error_code"`
        ErrorMsg  string `json:"error_msg"`
        Data      bool   `json:"data"`
    }
)

// 后台订单管理接口
@server(
    middleware: Global,Admin
    group: admin/order
    prefix: /mulandoGreateDestiny/v1/admin/order
    timeout: 10s
)
service mulandoGreateDestiny {
    // 后台订单列表
    @handler AdminList
    get /list (AdminOrderListReq) returns (AdminOrderListResp)

    // 订单重推ERP
    @handler RepushErp
    post /repush_erp (OrderRepushErpReq) returns (OrderRepushErpResp)

    // 订单退款
    @handler Refund
    post /refund (OrderRefundReq) returns (OrderRefundResp)
}
