package goods

import (
	"context"
	"database/sql"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCreateLogic {
	return &AdminCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCreateLogic) AdminCreate(req *types.GoodsCreateReq) error {
	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 创建商品
	goods := &model.VhGoods{
		Title:            req.Title,
		Brief:            req.Brief,
		Type:             req.Type,
		ItemsInfo:        sql.NullString{String: req.ItemsInfo, Valid: req.ItemsInfo != ""},
		Price:            req.Price,
		Inventory:        uint64(req.Inventory),
		ErpAmount:        req.ErpAmount,
		CashbackAmount:   req.CashbackAmount,
		DeductibleAmount: req.Price, // 自动设置为等于价格
		ProductImg:       req.ProductImg,
		AvatarImage:      req.AvatarImage,
		Detail:           sql.NullString{String: req.Detail, Valid: req.Detail != ""},
		OnsaleStatus:     req.OnsaleStatus,
		WarehouseCode:    "",
		OnsaleTime:       0,
		SoldOutTime:      0,
		Purchased:        0,
		SalesUserNum:     0,
		Pv:               0,
		DeleteTime:       0,
		Sort:             req.Sort,
		VhUid:            adminUid,
		VhVosName:        sql.NullString{String: adminVosName, Valid: adminVosName != ""},
		// CreatedTime 和 UpdateTime 由数据库自动维护，不需要手动设置
	}

	// 记录上下架时间
	now := time.Now().Unix()
	if req.OnsaleStatus == 2 { // 上架
		goods.OnsaleTime = now
	} else { // 下架
		goods.SoldOutTime = now
	}

	result, err := l.svcCtx.GoodsModel.Insert(l.ctx, goods)
	if err != nil {
		l.Logger.Error("AdminCreate GoodsModel.Insert error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 获取插入的商品ID
	goodsId, _ := result.LastInsertId()

	// 处理商品标签
	if len(req.Labels) > 0 {
		for _, label := range req.Labels {
			goodsLabel := &model.VhGoodsLabel{
				PeriodId: sql.NullInt64{Int64: goodsId, Valid: true},
				LabelId:  sql.NullInt64{Int64: label.LabelId, Valid: true},
				Name:     label.Name,
			}
			_, err = l.svcCtx.GoodsLabelModel.Insert(l.ctx, goodsLabel)
			if err != nil {
				l.Logger.Error("AdminCreate GoodsLabelModel.Insert error: %v", err)
				// 继续处理其他标签，不中断
			}
		}
	}

	return nil
}
