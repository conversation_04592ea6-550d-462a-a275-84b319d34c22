package box

import (
	"context"
	"encoding/json"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDetailLogic {
	return &AdminDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDetailLogic) AdminDetail(req *types.BoxDetailReq) (resp *types.BoxDetailResp, err error) {
	// 查询盲盒详情
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDetail BoxModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已删除
	if box.DeleteTime > 0 {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 查询盲盒项目
	boxItems, err := l.svcCtx.BoxItemsModel.FindByBoxId(l.ctx, int64(box.Id))
	if err != nil {
		l.Logger.Error("AdminDetail BoxItemsModel.FindByBoxId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换盲盒项目数据
	items := make([]types.BoxItem, 0, len(boxItems))
	for _, item := range boxItems {
		var itemsInfo []types.BoxItemInfo
		if item.ItemsInfo != "" {
			err := json.Unmarshal([]byte(item.ItemsInfo), &itemsInfo)
			if err != nil {
				l.Logger.Error("AdminDetail json.Unmarshal ItemsInfo error: %v", err)
				itemsInfo = []types.BoxItemInfo{}
			}
		}

		// 统计该期的已领取人数
		claimedCount, err := l.getClaimedCount(int64(item.Id))
		if err != nil {
			l.Logger.Error("AdminDetail getClaimedCount error: %v", err)
			claimedCount = 0
		}

		items = append(items, types.BoxItem{
			Id:           int64(item.Id),
			BoxId:        item.BoxId,
			Type:         item.Type,
			ItemsInfo:    itemsInfo,
			ClaimedCount: claimedCount,
		})
	}

	// 处理图片URL
	avatarImage := function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, box.AvatarImage)

	resp = &types.BoxDetailResp{
		BoxInfo: types.BoxInfo{
			Id:            int64(box.Id),
			Title:         box.Title,
			AvatarImage:   avatarImage,
			ValidTimeUnit: box.ValidTimeUnit,
			ValidTimeNum:  int64(box.ValidTimeNum),
			Price:         box.Price,
			OnsaleStatus:  box.OnsaleStatus,
			CreateNum:     box.CreateNum,
			ActiveNum:     box.ActiveNum,
			GetNum:        box.GetNum,
			CreatedTime:   function.FormatTime(box.CreatedTime),
			UpdateTime:    function.FormatTime(box.UpdateTime),
		},
		Items: items,
	}

	return resp, nil
}

// getClaimedCount 获取指定盲盒项目的已领取人数
// box_type = 2 或 3 都算领取
func (l *AdminDetailLogic) getClaimedCount(boxItemId int64) (int64, error) {
	count, err := l.svcCtx.OrderMainBoxModel.CountByBoxItemIdAndClaimed(l.ctx, boxItemId)
	if err != nil {
		return 0, err
	}
	return count, nil
}
