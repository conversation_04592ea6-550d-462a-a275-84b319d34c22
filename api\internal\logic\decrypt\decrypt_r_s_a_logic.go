package decrypt

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptRSALogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptRSALogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptRSALogic {
	return &DecryptRSALogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptRSALogic) DecryptRSA(req *types.RSADecryptReq) (resp *types.RSADecryptResp, err error) {
	// todo: add your logic here and delete this line

	return
}
