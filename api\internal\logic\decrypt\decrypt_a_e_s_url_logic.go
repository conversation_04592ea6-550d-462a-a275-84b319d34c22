package decrypt

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptAESUrlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptAESUrlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptAESUrlLogic {
	return &DecryptAESUrlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptAESUrlLogic) DecryptAESUrl(req *types.AESDecryptUrlReq) (resp *types.AESDecryptUrlResp, err error) {
	// todo: add your logic here and delete this line

	return
}
