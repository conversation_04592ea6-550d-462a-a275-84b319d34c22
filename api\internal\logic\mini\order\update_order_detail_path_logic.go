package order

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateOrderDetailPathLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateOrderDetailPathLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateOrderDetailPathLogic {
	return &UpdateOrderDetailPathLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateOrderDetailPathLogic) UpdateOrderDetailPath(req *types.UpdateOrderDetailPathReq) (resp *types.UpdateOrderDetailPathResp, err error) {
	// todo: add your logic here and delete this line

	return
}
