syntax = "v1"

info(
    title: "盲盒领取接口"
    desc: "盲盒领取相关接口"
    author: "system"
    email: ""
    version: "v1"
)

type (
    // 盲盒领取请求
    BoxClaimReq {
        Id        int64 `json:"id" validate:"required" v:"盲盒订单ID"`
        BoxType   int   `json:"box_type" validate:"required,oneof=2 3" v:"领取类型:2=自己领取,3=赠送"`
        AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
    }

    // 盲盒领取响应
    BoxClaimResp {
        SubOrderNo string `json:"sub_order_no"`
        Message    string `json:"message"`
    }
)

// 小程序盲盒领取接口
@server(
    middleware: Global,Auth
    group: mini/box
    prefix: /mulandoGreateDestiny/v1/mini/box
    timeout: 30s
)
service mulandoGreateDestiny {
    // 盲盒领取
    @handler Claim
    post /claim (BoxClaimReq) returns (BoxClaimResp)
}
