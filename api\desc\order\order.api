syntax = "v1"

info (
    title:   "订单管理接口"
    desc:    "订单相关接口定义"
    author:  "system"
    version: "v1"
)

import "../common/common.api"

type (
    // 创建订单请求
    OrderCreateReq {
        GoodsId          int64   `json:"goods_id" validate:"required" v:"商品ID"`
        Quantity         int64   `json:"quantity" validate:"required,min=1" v:"购买数量"`
        AddressId        int64   `json:"address_id" validate:"required" v:"收货地址ID"`
        DeductibleAmount float64 `json:"deductible_amount,default=0" validate:"min=0" v:"使用礼金额度"`
        ShareId          int64   `json:"share_id,optional" v:"分享ID"`
    }

    // 创建订单响应
    OrderCreateResp {
        MainOrderNo      string  `json:"main_order_no"`
        SubOrderNo       string  `json:"sub_order_no"`
        PaymentAmount    float64 `json:"payment_amount"`
        CashAmount       float64 `json:"cash_amount"`
        DeductibleAmount float64 `json:"deductible_amount"`
        OrderStatus      int64   `json:"order_status"`
        NeedPayment      bool    `json:"need_payment"`
        CancelTimeout    int64   `json:"cancel_timeout"`
    }

    // 批量创建抽奖订单请求项
    LotteryOrderItem {
        GoodsId   int64 `json:"goods_id" validate:"required" v:"商品ID"`
        AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
        Uid       int64 `json:"uid" validate:"required" v:"用户ID"`
    }

    // 批量创建抽奖订单响应项
    LotteryOrderResult {
        Uid         int64  `json:"uid"`           // 用户ID
        MainOrderNo string `json:"main_order_no"` // 主订单号
        SubOrderNo  string `json:"sub_order_no"`  // 子订单号
    }

    // 微信支付请求
    WechatPayReq {
        MainOrderNo string `json:"main_order_no" validate:"required" v:"主订单号"`
    }

    // 订单列表请求
    OrderListReq {
        Page   int64 `form:"page,default=1" validate:"min=1" v:"页码"`
        Limit  int64 `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
        Status int64 `form:"status,optional" validate:"min=0,max=4" v:"订单状态"`
    }

    // 订单状态统计
    OrderStatusCount {
        PendingPayment int64 `json:"pending_payment"` // 待支付数量
        PendingShipment int64 `json:"pending_shipment"` // 待发货数量
        Shipped int64 `json:"shipped"` // 已发货数量
        Completed int64 `json:"completed"` // 已完成数量
    }

    // 订单列表响应
    OrderListResp {
        List  []OrderItem `json:"list"`
        Total int64       `json:"total"`
        StatusCount OrderStatusCount `json:"status_count"` // 状态统计
    }

    // 订单详情请求
    OrderDetailReq {
        SubOrderNo  string `form:"sub_order_no,optional" v:"子订单号"`
        MainOrderNo string `form:"main_order_no,optional" v:"主订单号"`
        Id          string `form:"id,optional" v:"订单号(兼容微信跳转)"`
        Channel     int64  `form:"channel,optional" v:"来源渠道"`
    }

    // 订单详情响应
    OrderDetailResp {
        // 基本订单信息
        SubOrderNo       string  `json:"sub_order_no"`       // 子订单号
        MainOrderNo      string  `json:"main_order_no"`      // 主订单号
        OrderType        int64   `json:"order_type"`         // 订单类型:1=普通商品,2=抽奖商品,3=盲盒商品
        OrderStatus      int64   `json:"order_status"`       // 订单状态
        OrderStatusText  string  `json:"order_status_text"`  // 订单状态文本
        OrderQty         int64   `json:"order_qty"`          // 购买数量
        GoodsTitle       string  `json:"goods_title"`        // 商品名称(从快照获取)
        GoodsImage       string  `json:"goods_image"`        // 商品图片(从快照获取)
        GoodsPrice       float64 `json:"goods_price"`        // 商品价格(从快照获取)

        // 商品信息
        GoodsInfo        OrderGoodsInfo `json:"goods_info"`    // 商品详情

        // 时间信息
        CreatedTime      string  `json:"created_time"`       // 下单时间
        PaymentTime      string  `json:"payment_time"`       // 支付时间
        DeliveryTime     string  `json:"delivery_time"`      // 发货时间
        CompletedTime    string  `json:"completed_time"`     // 完成时间
        CancelTime       string  `json:"cancel_time"`        // 取消时间

        // 费用明细
        GoodsAmount      float64 `json:"goods_amount"`       // 商品金额
        DeductibleAmount float64 `json:"deductible_amount"`  // 礼金抵扣
        PaymentAmount    float64 `json:"payment_amount"`     // 实付金额
        CashAmount       float64 `json:"cash_amount"`        // 现金支付金额

        // 物流信息
        ExpressType      int64   `json:"express_type"`       // 快递方式
        ExpressNumber    string  `json:"express_number"`     // 快递单号
        ExpressName      string  `json:"express_name"`       // 快递公司名称

        // 收货地址信息
        ConsigneeName    string  `json:"consignee_name"`     // 收货人姓名
        ConsigneePhone   string  `json:"consignee_phone"`    // 收货人电话
        Province         string  `json:"province"`           // 省份
        City             string  `json:"city"`               // 城市
        District         string  `json:"district"`           // 区县
        Address          string  `json:"address"`            // 详细地址

        // 操作按钮控制
        CanPay           bool    `json:"can_pay"`            // 是否可以支付
        CanCancel        bool    `json:"can_cancel"`         // 是否可以取消
        CanConfirm       bool    `json:"can_confirm"`        // 是否可以确认收货
        CanRepurchase    bool    `json:"can_repurchase"`     // 是否可以再次购买

        // 其他信息
        Remarks          string  `json:"remarks"`            // 订单备注
        RefundStatus     int64   `json:"refund_status"`      // 退款状态
        RefundAmount     float64 `json:"refund_amount"`      // 退款金额
    }

    // 订单项
    OrderItem {
        Uid             int64                   `json:"uid"`               // 用户ID
        OrderType       int64                   `json:"order_type"`        // 订单类型:1=普通商品,2=抽奖商品,3=盲盒商品
        Title           string                  `json:"title"`             // 订单标题(商品名称或盲盒名称)
        AvatarImage     string                  `json:"avatar_image"`      // 订单图片
        MainOrderNo     string                  `json:"main_order_no"`     // 主订单号
        SubOrderNo      string                  `json:"sub_order_no"`      // 子订单号(普通/抽奖订单有值，盲盒订单为空)
        OrderQty        int64                   `json:"order_qty"`         // 订单数量
        OrderStatus     int64                   `json:"order_status"`      // 订单状态
        PaymentAmount   float64                 `json:"payment_amount"`    // 支付总金额
        CashAmount      float64                 `json:"cash_amount"`       // 现金支付金额
        CreatedTime     string                  `json:"created_time"`      // 创建时间
        PaymentTime     string                  `json:"payment_time"`      // 支付时间
        ValidTimeUnit   string                  `json:"valid_time_unit"`   // 有效期单位(盲盒专用，普通订单为空)
        ValidTimeNum    int64                   `json:"valid_time_num"`    // 有效期数量(盲盒专用，普通订单为0)
        BoxItems        []OrderBoxItemDetail    `json:"box_items"`         // 盲盒项目列表(盲盒订单专用，普通订单为空)
        HasUnclaimedBox bool                    `json:"-"`                 // 是否有未领取的盲盒项目(仅用于排序，不输出到JSON)
    }

    // 订单商品信息
    OrderGoodsInfo {
        GoodsId      int64   `json:"goods_id"`
        Title        string  `json:"title"`
        AvatarImage  string  `json:"avatar_image"`
        Price        float64 `json:"price"`
        Quantity     int64   `json:"quantity"`
    }

    // 订单盲盒项目详情
    OrderBoxItemDetail {
        Id           int64  `json:"id"`            // vh_order_main_box.id
        BoxType      int64  `json:"box_type"`      // 盲盒领取类型:1=待领取,2=自己领取,3=赠送
        MonthLabel   string `json:"month_label"`   // 月份标识(如"2025年01月")
        CanClaim     bool   `json:"can_claim"`     // 是否可以领取
        CanTransfer  bool   `json:"can_transfer"`  // 是否可以转赠
        BeginTime    string `json:"begin_time"`    // 开始领取时间
        EndTime      string `json:"end_time"`      // 结束领取时间
        GetTime      string `json:"get_time"`      // 领取时间
        OrderNo      string `json:"order_no"`      // 子订单号 (vh_order_main_box.order_no)
        ClaimedPhone string `json:"claimed_phone"` // 领取人手机号(已脱敏)
    }

    // 订单盲盒信息
    OrderBoxInfo {
        BoxId       int64                   `json:"box_id"`
        Title       string                  `json:"title"`
        AvatarImage string                  `json:"avatar_image"`
        BoxItems    []OrderBoxItemDetail    `json:"box_items"`    // 盲盒项目详情列表
    }

    // 盲盒商品详情
    OrderBoxItemInfo {
        Name      string `json:"name"`       // 产品名称
        ShortCode string `json:"short_code"` // 简码
        Num       int64  `json:"num"`        // 数量
    }

    // 微信支付响应
    WechatPayResp {
        AppId     string `json:"appId"`
        TimeStamp string `json:"timeStamp"`
        NonceStr  string `json:"nonceStr"`
        Package   string `json:"package"`
        SignType  string `json:"signType"`
        PaySign   string `json:"paySign"`
    }

    // 确认收货请求
    ConfirmReceiptReq {
        SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
    }

    // 确认收货响应
    ConfirmReceiptResp {
        Message string `json:"message"`
    }

    // 支付状态查询请求
    PaymentStatusReq {
        MainOrderNo string `form:"main_order_no" validate:"required" v:"主订单号"`
    }

    // 支付状态查询响应
    PaymentStatusResp {
        PaymentStatus   int64   `json:"payment_status"`   // 支付状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消
        MainOrderNo     string  `json:"main_order_no"`    // 主订单号
        CashAmount      float64 `json:"cash_amount"`      // 微信支付金额
        DeductibleAmount float64 `json:"deductible_amount"` // 礼金支付金额
        PaymentTime     string  `json:"payment_time"`     // 支付时间
        CashbackAmount  float64 `json:"cashback_amount"`  // 返现金额
    }

    // 取消订单请求
    CancelOrderReq {
        MainOrderNo string `json:"main_order_no" validate:"required" v:"主订单号"`
    }

    // 取消订单响应
    CancelOrderResp {
        Message         string  `json:"message"`
        RefundAmount    float64 `json:"refund_amount"`    // 退还的礼金金额
    }

    // 配置订单详情路径请求
    UpdateOrderDetailPathReq {
        Path string `json:"path" validate:"required" v:"订单详情路径"`
    }

    // 配置订单详情路径响应
    UpdateOrderDetailPathResp {
        ErrorCode int64  `json:"errcode"`
        ErrorMsg  string `json:"errmsg"`
    }

    // 查询订单详情路径响应
    GetOrderDetailPathResp {
        ErrorCode int64  `json:"errcode"`
        ErrorMsg  string `json:"errmsg"`
        Path      string `json:"path"`
    }

    // 发货通知请求
    ShipmentNotifyReq {
        MainOrderNo string `json:"main_order_no,optional" v:"主订单号"`
        OrderNo     string `json:"orderNo" validate:"required" v:"子订单号"`
        WayBill     string `json:"wayBill" validate:"required" v:"运单号"`
        ExpressType int64  `json:"expressType" validate:"required" v:"快递方式"`
        Platform    int64  `json:"platform,optional" v:"平台"`
        IsUp        int64  `json:"is_up,optional" v:"是否上架"`
    }

    // 发货通知响应
    ShipmentNotifyResp {
        ErrorCode int64  `json:"error_code"`
        ErrorMsg  string `json:"error_msg"`
        Message   string `json:"message"`
    }
)

// 小程序订单接口
@server(
    middleware: Global,Auth
    group: mini/order
    prefix: /mulandoGreateDestiny/v1/mini/order
    timeout: 10s
)
service mulandoGreateDestiny {
    // 创建订单
    @handler Create
    post /create (OrderCreateReq) returns (OrderCreateResp)

    // 微信支付
    @handler WechatPay
    post /wechat_pay (WechatPayReq) returns (WechatPayResp)

    // 订单列表
    @handler List
    get /list (OrderListReq) returns (OrderListResp)

    // 订单详情
    @handler Detail
    get /detail (OrderDetailReq) returns (OrderDetailResp)

    // 确认收货
    @handler ConfirmReceipt
    post /confirm_receipt (ConfirmReceiptReq) returns (ConfirmReceiptResp)

    // 支付状态查询
    @handler PaymentStatus
    get /payment_status (PaymentStatusReq) returns (PaymentStatusResp)

    // 配置订单详情路径
    @handler UpdateOrderDetailPath
    post /update_order_detail_path (UpdateOrderDetailPathReq) returns (UpdateOrderDetailPathResp)

    // 查询订单详情路径
    @handler GetOrderDetailPath
    get /get_order_detail_path returns (GetOrderDetailPathResp)

    // 取消订单
    @handler CancelOrder
    post /cancel (CancelOrderReq) returns (CancelOrderResp)
}

// 第三方发货通知接口（无需认证）
@server(
    middleware: Global
    group: order/notify
    prefix: /mulandoGreateDestiny/v1/order/notify
    timeout: 10s
)
service mulandoGreateDestiny {
    // 发货通知
    @handler ShipmentNotify
    post /shipment (ShipmentNotifyReq) returns (ShipmentNotifyResp)
}

// 后台订单接口 - 删除了创建抽奖订单接口，改为内部服务调用
@server(
    middleware: Global,Admin
    group: admin/order
    prefix: /mulandoGreateDestiny/v1/admin/order
    timeout: 3s
)
service mulandoGreateDestiny {
    // 创建抽奖订单接口已删除，改为内部服务调用
}
