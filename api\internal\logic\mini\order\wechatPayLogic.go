package order

import (
	"context"
	"math/rand"
	"time"
	"unicode/utf8"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/go-pay/gopay"
	wechat "github.com/go-pay/gopay/wechat/v3"
	"github.com/golang-jwt/jwt/v4"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

type WechatPayLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWechatPayLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WechatPayLogic {
	return &WechatPayLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WechatPayLogic) WechatPay(req *types.WechatPayReq) (resp *types.WechatPayResp, err error) {
	// 获取用户信息
	jwtUser := l.ctx.Value("jwt_user").(jwt.MapClaims)
	uid := cast.ToInt64(jwtUser["uid"])

	// 查询订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, req.MainOrderNo)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("订单不存在")
		}
		l.Logger.Error("WechatPay OrderMainModel.FindOneByMainOrderNo error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证订单属于当前用户
	if mainOrder.Uid != uid {
		return nil, xerr.NewErrMsg("订单不属于当前用户")
	}

	// 验证订单状态
	if mainOrder.MainOrderStatus != 0 {
		return nil, xerr.NewErrMsg("订单状态异常，无法支付")
	}

	// 验证支付金额
	if mainOrder.CashAmount <= 0 {
		return nil, xerr.NewErrMsg("订单无需支付")
	}

	// 检查订单是否超时（5分钟）
	if time.Since(mainOrder.CreatedTime) > 5*time.Minute {
		return nil, xerr.NewErrMsg("订单已超时，请重新下单")
	}

	// 获取子订单信息用于商品描述
	subOrders, err := l.svcCtx.OrderModel.FindByMainOrderId(l.ctx, mainOrder.Id)
	if err != nil || len(subOrders) == 0 {
		l.Logger.Error("WechatPay OrderModel.FindByMainOrderId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取商品信息用于描述
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(subOrders[0].GoodsId))
	if err != nil {
		l.Logger.Error("WechatPay GoodsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 调用微信支付统一下单
	payResp, err := l.createWechatPayOrder(mainOrder, goods, uid)
	if err != nil {
		return nil, err
	}

	return payResp, nil
}

// createWechatPayOrder 创建微信支付订单
func (l *WechatPayLogic) createWechatPayOrder(order *model.VhOrderMain, goods *model.VhGoods, uid int64) (*types.WechatPayResp, error) {

	// 获取用户的openid
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		l.Logger.Error("createWechatPayOrder UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查用户是否有openid
	l.Logger.Infof("createWechatPayOrder user info: uid=%d, applet_openid='%s', length=%d",
		uid, user.AppletOpenid, len(user.AppletOpenid))

	if user.AppletOpenid == "" {
		l.Logger.Error("createWechatPayOrder user has no openid: uid=%d", uid)
		return nil, xerr.NewErrMsg("用户未绑定微信")
	}

	// 验证openid是否为有效UTF-8
	if !utf8.ValidString(user.AppletOpenid) {
		l.Logger.Errorf("createWechatPayOrder user openid contains invalid UTF-8: uid=%d, openid=%s", uid, user.AppletOpenid)
		return nil, xerr.NewErrMsg("用户微信信息异常")
	}

	// 构建商品描述
	var description string
	if goods.Title != "" {
		description = l.truncateUTF8String(goods.Title, 127) // V3版本描述限制127个字符
	} else if goods.Brief != "" {
		description = l.truncateUTF8String(goods.Brief, 127)
	} else {
		description = "商品订单"
	}

	// 确保商品描述不为空
	if description == "" {
		description = "商品订单"
	}

	l.Logger.Infof("createWechatPayOrder goods info: id=%d, title='%s', brief='%s', final_description='%s'",
		goods.Id, goods.Title, goods.Brief, description)

	// 构建V3版本统一下单请求参数
	totalAmount := int64(order.CashAmount * 100) // 转换为分

	bm := make(gopay.BodyMap)
	bm.Set("description", description)
	bm.Set("out_trade_no", order.MainOrderNo)
	bm.Set("notify_url", l.svcCtx.Config.WePay.NotifyUrl)

	// 金额信息
	amountMap := make(gopay.BodyMap)
	amountMap.Set("total", totalAmount)
	amountMap.Set("currency", "CNY")
	bm.Set("amount", amountMap)

	// 根据配置判断是否为服务商模式
	var isPartnerMode bool
	payerMap := make(gopay.BodyMap)

	if l.svcCtx.Config.WePay.SubMiniAppId != "" && l.svcCtx.Config.WePay.SubMchId != "" {
		// 服务商模式
		bm.Set("sp_appid", l.svcCtx.Config.WePay.AppID)  // 服务商应用ID
		bm.Set("sp_mchid", l.svcCtx.Config.WePay.MchId)  // 服务商商户号
		bm.Set("sub_appid", l.svcCtx.Config.WePay.SubMiniAppId)  // 子商户应用ID
		bm.Set("sub_mchid", l.svcCtx.Config.WePay.SubMchId)      // 子商户号
		payerMap.Set("sub_openid", user.AppletOpenid)  // 子商户openid
		isPartnerMode = true
		l.Logger.Infof("Service provider mode using sub_openid: '%s' (sp_appid: %s, sp_mchid: %s, sub_appid: %s, sub_mchid: %s)",
			user.AppletOpenid, l.svcCtx.Config.WePay.AppID, l.svcCtx.Config.WePay.MchId,
			l.svcCtx.Config.WePay.SubMiniAppId, l.svcCtx.Config.WePay.SubMchId)
	} else {
		// 直连模式
		bm.Set("appid", l.svcCtx.Config.WePay.AppID)
		bm.Set("mchid", l.svcCtx.Config.WePay.MchId)
		payerMap.Set("openid", user.AppletOpenid)
		isPartnerMode = false
		l.Logger.Infof("Direct mode using openid: '%s'", user.AppletOpenid)
	}
	bm.Set("payer", payerMap)

	// 验证payer信息是否正确设置
	l.Logger.Infof("createWechatPayOrder payer map: %+v", payerMap)

	l.Logger.Infof("createWechatPayOrder calling wechat jsapi: order=%s, amount=%.2f, total_amount=%d, openid=%s",
		order.MainOrderNo, order.CashAmount, totalAmount, user.AppletOpenid)
	l.Logger.Infof("createWechatPayOrder jsapi params: appid=%s, mchid=%s, sub_appid=%s, sub_mchid=%s, description='%s', out_trade_no=%s",
		l.svcCtx.Config.WePay.AppID, l.svcCtx.Config.WePay.MchId,
		l.svcCtx.Config.WePay.SubMiniAppId, l.svcCtx.Config.WePay.SubMchId, description, order.MainOrderNo)

	// 打印完整的请求参数用于调试
	l.Logger.Infof("createWechatPayOrder full BodyMap: %+v", bm)

	// 根据模式调用不同的微信V3版本JSAPI下单接口
	var wxRsp *wechat.PrepayRsp
	if isPartnerMode {
		// 服务商模式
		wxRsp, err = l.svcCtx.WePay.V3PartnerTransactionJsapi(l.ctx, bm)
		if err != nil {
			l.Logger.Errorf("createWechatPayOrder V3PartnerTransactionJsapi error: %v", err)
			return nil, xerr.NewErrMsg("调用微信支付失败")
		}
		l.Logger.Infof("createWechatPayOrder using partner mode API")
	} else {
		// 直连模式
		wxRsp, err = l.svcCtx.WePay.V3TransactionJsapi(l.ctx, bm)
		if err != nil {
			l.Logger.Errorf("createWechatPayOrder V3TransactionJsapi error: %v", err)
			return nil, xerr.NewErrMsg("调用微信支付失败")
		}
		l.Logger.Infof("createWechatPayOrder using direct mode API")
	}

	// 检查响应状态
	if wxRsp.Code != 0 {
		l.Logger.Errorf("createWechatPayOrder WeChat V3 error: code=%d, error=%s", wxRsp.Code, wxRsp.Error)
		return nil, xerr.NewErrMsg("微信支付下单失败")
	}

	// 记录微信响应
	l.Logger.Infof("createWechatPayOrder WeChat V3 response: prepay_id=%s", wxRsp.Response.PrepayId)

	// 确定使用的AppID
	var miniAppId string
	if l.svcCtx.Config.WePay.SubMiniAppId != "" {
		miniAppId = l.svcCtx.Config.WePay.SubMiniAppId
	} else {
		miniAppId = l.svcCtx.Config.WePay.AppID
	}

	// 使用gopay库生成小程序支付参数
	jsapiParams, err := l.svcCtx.WePay.PaySignOfJSAPI(miniAppId, wxRsp.Response.PrepayId)
	if err != nil {
		l.Logger.Errorf("createWechatPayOrder PaySignOfJSAPI error: %v", err)
		return nil, xerr.NewErrMsg("生成支付签名失败")
	}

	l.Logger.Infof("createWechatPayOrder using RSA signature (V3 version)")
	l.Logger.Infof("createWechatPayOrder success: order=%s, prepay_id=%s, mini_appid=%s", order.MainOrderNo, wxRsp.Response.PrepayId, miniAppId)
	l.Logger.Infof("createWechatPayOrder sign params: appId=%s, timeStamp=%s, nonceStr=%s, package=%s, signType=%s",
		jsapiParams.AppId, jsapiParams.TimeStamp, jsapiParams.NonceStr, jsapiParams.Package, jsapiParams.SignType)
	l.Logger.Infof("createWechatPayOrder generated paySign: %s", jsapiParams.PaySign)

	return &types.WechatPayResp{
		AppId:     jsapiParams.AppId,
		TimeStamp: jsapiParams.TimeStamp,
		NonceStr:  jsapiParams.NonceStr,
		Package:   jsapiParams.Package,
		SignType:  jsapiParams.SignType,
		PaySign:   jsapiParams.PaySign,
	}, nil
}

// getRandomString 生成指定长度的随机字符串
func (l *WechatPayLogic) getRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}



// truncateUTF8String 安全地截断UTF-8字符串到指定字节长度，确保不会破坏UTF-8字符
func (l *WechatPayLogic) truncateUTF8String(s string, maxBytes int) string {
	if s == "" {
		return "商品订单" // 如果原字符串为空，返回默认值
	}

	if len(s) <= maxBytes {
		return s
	}

	// 从maxBytes位置向前查找有效的UTF-8字符边界
	for i := maxBytes; i >= 1; i-- {
		if utf8.ValidString(s[:i]) {
			return s[:i]
		}
	}

	// 如果找不到有效边界，返回默认值
	return "商品订单"
}
