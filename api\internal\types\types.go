// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package types

type AESDecryptReq struct {
	Q string `form:"q" validate:"required" v:"加密数据"`
}

type AESDecryptResp struct {
	DecryptedData string `json:"decrypted_data"`
}

type AESDecryptUrlReq struct {
	Q string `form:"q" validate:"required" v:"加密数据"`
}

type AESDecryptUrlResp struct {
	Url string `json:"url"`
}

type ActiveLotteryActivityAddressUpdateReq struct {
	ActiveLotteryActivityParticipantReq
}

type ActiveLotteryActivityParticipantAddressReq struct {
	IdFU
}

type ActiveLotteryActivityParticipantAddressResp struct {
	UserAddressInfo
}

type ActiveLotteryActivityParticipantListReq struct {
	Paging
	IdFU
}

type ActiveLotteryActivityParticipantListResp struct {
	List  []ParticipantUserInfo `json:"list"`
	Total int64                 `json:"total"`
}

type ActiveLotteryActivityParticipantReq struct {
	IdJU
	AddressId int64 `json:"address_id" validate:"required" v:"地址ID"`
}

type ActiveLotteryActivityResp struct {
	LotteryActivityInfo
}

type ActiveLotteryActivityisParticipantReq struct {
	IdFU
}

type ActiveLotteryActivityisParticipantResp struct {
	IsParticipant bool  `json:"is_participant"`
	IsWinner      int64 `json:"is_winner"`
}

type AddressCreateReq struct {
	ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
	CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
	TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
	Address        string `json:"address" validate:"required" v:"详细地址"`
	Consignee      string `json:"consignee" validate:"required" v:"收货人"`
	ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
	ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
	CityName       string `json:"city_name" validate:"required" v:"城市名称"`
	TownName       string `json:"town_name" validate:"required" v:"区县名称"`
	Label          string `json:"label,optional" v:"标签"`
	Code           string `json:"code,optional" v:"邮编"`
	IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
}

type AddressCreateResp struct {
	Id int64 `json:"id"`
}

type AddressDeleteReq struct {
	Id int64 `json:"id" validate:"required" v:"地址ID"`
}

type AddressDetailReq struct {
	Id int64 `form:"id" validate:"required" v:"地址ID"`
}

type AddressListReq struct {
	Page  int64 `form:"page,default=1"`
	Limit int64 `form:"limit,default=10"`
}

type AddressListResp struct {
	List  []UserAddressInfo `json:"list"`
	Total int64             `json:"total"`
}

type AddressUpdateReq struct {
	Id             int64  `json:"id" validate:"required" v:"地址ID"`
	ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
	CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
	TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
	Address        string `json:"address" validate:"required" v:"详细地址"`
	Consignee      string `json:"consignee" validate:"required" v:"收货人"`
	ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
	ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
	CityName       string `json:"city_name" validate:"required" v:"城市名称"`
	TownName       string `json:"town_name" validate:"required" v:"区县名称"`
	Label          string `json:"label,optional" v:"标签"`
	Code           string `json:"code,optional" v:"邮编"`
	IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
}

type AdminBannerListReq struct {
	Paging
	Title  string `form:"title,optional"`  // 标题筛选，可选
	Status int64  `form:"status,optional"` // 状态筛选，可选
}

type AdminBannerListResp struct {
	List  []BannerInfo `json:"list"`
	Total int64        `json:"total"`
}

type AdminBoxListReq struct {
	Page          int64  `form:"page,default=1" validate:"min=1" v:"页码"`
	Limit         int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
	Title         string `form:"title,optional" v:"盲盒名称"`
	Id            int64  `form:"id,optional" v:"盲盒ID"`
	ValidTimeUnit string `form:"valid_time_unit,optional" validate:"omitempty,oneof=hour day week month year" v:"周期单位"`
	OnsaleStatus  int64  `form:"onsale_status,optional" validate:"omitempty,oneof=2 3" v:"上架状态"`
	StartDate     string `form:"start_date,optional" v:"开始日期"`
	EndDate       string `form:"end_date,optional" v:"结束日期"`
}

type AdminBoxListResp struct {
	List  []BoxInfo `json:"list"`
	Total int64     `json:"total"`
}

type AdminGoodsListReq struct {
	Paging
	Title        string `form:"title,optional"`         // 商品名称筛选，可选
	Type         int64  `form:"type,optional"`          // 商品类型筛选，可选
	OnsaleStatus int64  `form:"onsale_status,optional"` // 上架状态筛选，可选
	ShortCode    string `form:"short_code,optional"`    // 商品简码筛选，可选
	StartDate    string `form:"start_date,optional"`    // 开始日期 (格式: 2025-07-02)
	EndDate      string `form:"end_date,optional"`      // 结束日期 (格式: 2025-07-04)
}

type AdminGoodsListResp struct {
	List  []GoodsInfo `json:"list"`
	Total int64       `json:"total"`
}

type AdminLabelListReq struct {
	Paging
	Status int64 `form:"status,optional"` // 状态筛选，可选
}

type AdminLabelListResp struct {
	List  []LabelInfo `json:"list"`
	Total int64       `json:"total"`
}

type AdminLotteryActivityListReq struct {
	Paging
	Title  string `form:"title,optional"`  // 活动名称搜索
	Status int64  `form:"status,optional"` // 状态筛选
}

type AdminLotteryActivityListResp struct {
	List  []LotteryActivityInfo `json:"list"`
	Total int64                 `json:"total"`
}

type AdminOrderItem struct {
	Id                 int64   `json:"id"`                   // 子订单ID
	SubOrderNo         string  `json:"sub_order_no"`         // 子订单号
	MainOrderNo        string  `json:"main_order_no"`        // 主订单号
	SubOrderStatus     int64   `json:"sub_order_status"`     // 子订单状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消
	MainOrderStatus    int64   `json:"main_order_status"`    // 主订单状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消
	GoodsId            int64   `json:"goods_id"`             // 商品ID
	GoodsName          string  `json:"goods_name"`           // 商品名称(从快照获取)
	GoodsType          int64   `json:"goods_type"`           // 商品类型:1=普通商品,2=抽奖商品,3=盲盒商品
	OrderQty           int64   `json:"order_qty"`            // 购买数量
	PaymentAmount      float64 `json:"payment_amount"`       // 支付金额
	CashAmount         float64 `json:"cash_amount"`          // 现金支付金额
	DeductibleAmount   float64 `json:"deductible_amount"`    // 礼金抵扣金额
	ErpAmount          float64 `json:"erp_amount"`           // ERP推单金额
	RefundAmount       float64 `json:"refund_amount"`        // 已退款金额
	RefundStatus       int64   `json:"refund_status"`        // 退款状态
	CreatedTime        string  `json:"created_time"`         // 下单时间
	PaymentTime        string  `json:"payment_time"`         // 支付时间
	DeliveryTime       string  `json:"delivery_time"`        // 发货时间
	CancelTime         string  `json:"cancel_time"`          // 取消时间
	ConsigneeName      string  `json:"consignee_name"`       // 收货人姓名(原始数据)
	ConsigneePhone     string  `json:"consignee_phone"`      // 收货人电话(原始数据)
	ConsigneeNameMask  string  `json:"consignee_name_mask"`  // 收货人姓名(脱敏)
	ConsigneePhoneMask string  `json:"consignee_phone_mask"` // 收货人电话(脱敏)
	Province           string  `json:"province"`             // 省份
	City               string  `json:"city"`                 // 城市
	District           string  `json:"district"`             // 区县
	Address            string  `json:"address"`              // 详细地址
	ExpressType        int64   `json:"express_type"`         // 快递方式
	ExpressNumber      string  `json:"express_number"`       // 快递单号
	PushTStatus        int64   `json:"push_t_status"`        // T+推送状态:0=未推送,1=推送成功,2=推送失败
	PushWmsStatus      int64   `json:"push_wms_status"`      // 萌芽推送状态:0=未推送,1=推送成功,2=推送失败
	PushZtStatus       int64   `json:"push_zt_status"`       // 中台推送状态:0=未推送,1=推送成功,2=推送失败
	PaymentMethod      int64   `json:"payment_method"`       // 支付方式
	Tradeno            string  `json:"tradeno"`              // 支付流水号
	Remarks            string  `json:"remarks"`              // 订单备注
	WarehouseCode      string  `json:"warehouse_code"`       // 仓库编码
}

type AdminOrderListReq struct {
	Page             int64  `form:"page,default=1" validate:"min=1" v:"页码"`
	Limit            int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
	SubOrderNo       string `form:"sub_order_no,optional" v:"订单号(可搜索子订单号和主订单号)"`
	GoodsName        string `form:"goods_name,optional" v:"商品名称"`
	ConsigneeName    string `form:"consignee_name,optional" v:"收货人姓名"`
	ConsigneePhone   string `form:"consignee_phone,optional" v:"收货人手机"`
	SubOrderStatus   int64  `form:"sub_order_status,optional" validate:"omitempty,oneof=0 1 2 3 4" v:"子订单状态"`
	MainOrderStatus  int64  `form:"main_order_status,optional" validate:"omitempty,oneof=0 1 2 3 4" v:"主订单状态"`
	ErpPushStatus    int64  `form:"erp_push_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"ERP推送状态"`
	MiddlePushStatus int64  `form:"middle_push_status,optional" validate:"omitempty,oneof=0 1 2" v:"推送中台状态"`
	PushZtStatus     int64  `form:"push_zt_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"中台推送状态"`
	PushTStatus      int64  `form:"push_t_status,optional" validate:"omitempty,oneof=0 1 2 3" v:"T+推送状态"`
	GoodsType        int64  `form:"goods_type,optional" validate:"omitempty,oneof=1 2 3" v:"商品类型"`
	BoxId            int64  `form:"box_id,optional" v:"盲盒ID"`
	PaymentTimeStart string `form:"payment_time_start,optional" v:"支付开始日期"`
	PaymentTimeEnd   string `form:"payment_time_end,optional" v:"支付结束日期"`
}

type AdminOrderListResp struct {
	List  []AdminOrderItem `json:"list"`
	Total int64            `json:"total"`
}

type AdminUserLevelListReq struct {
	Page  int64  `form:"page,default=1"`
	Limit int64  `form:"limit,default=10"`
	Name  string `form:"name,optional"`
	Level int64  `form:"level,optional"`
}

type AdminUserLevelListResp struct {
	List  []UserLevelInfo `json:"list"`
	Total int64           `json:"total"`
}

type AdminUserListReq struct {
	Page        int64  `form:"page,default=1"`
	Limit       int64  `form:"limit,default=10"`
	Telephone   string `form:"telephone,optional"`
	Nickname    string `form:"nickname,optional"`
	Level       int64  `form:"level,optional"`
	Type        int64  `form:"type,optional"`
	LevelStatus int64  `form:"level_status,optional"`
	IsDisabled  int64  `form:"is_disabled,optional"`
}

type AdminUserListResp struct {
	List      []UserInfo `json:"list"`
	Total     int64      `json:"total"`
	TotalUser int64      `json:"total_user"`
}

type ApplyMemberReq struct {
	Platform string `json:"platform" validate:"required" v:"平台名称"`
	OrderNo  string `json:"order_no" validate:"required" v:"订单号"`
	Phone    string `json:"phone" validate:"required" v:"手机号"`
}

type ApplyMemberResp struct {
	Message string `json:"message"`
}

type ApplyPartnerReq struct {
	Level int64 `json:"level" validate:"required,min=1" v:"合伙人等级"`
}

type ApplyPartnerResp struct {
	Message string `json:"message"`
	Level   int64  `json:"level"`
	EndTime string `json:"end_time"`
}

type BannerCreateReq struct {
	Name      string `json:"name" validate:"required" v:"标题"`
	Picture   string `json:"picture" validate:"required" v:"图片"`
	JumpType  int64  `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
	JumpValue string `json:"jump_value" v:"跳转值"`
	Sort      int64  `json:"sort,default=1000" validate:"min=1" v:"排序"`
	Status    int64  `json:"status,default=2" validate:"min=1,max=2" v:"状态"`
}

type BannerDeleteReq struct {
	IdJ
}

type BannerDetailReq struct {
	IdF
}

type BannerInfo struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Picture     string `json:"picture"`
	JumpType    int64  `json:"jump_type"`
	JumpValue   string `json:"jump_value"`
	Sort        int64  `json:"sort"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdateTime  string `json:"update_time"`
	VhUid       int64  `json:"vh_uid"`
	VhVosName   string `json:"vh_vos_name"`
}

type BannerUpdateReq struct {
	IdJ
	Name      string `json:"name" validate:"required" v:"标题"`
	Picture   string `json:"picture" validate:"required" v:"图片"`
	JumpType  int64  `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
	JumpValue string `json:"jump_value" v:"跳转值"`
	Sort      int64  `json:"sort" validate:"min=1" v:"排序"`
	Status    int64  `json:"status" validate:"min=1,max=2" v:"状态"`
}

type BoxBindGoodsReq struct {
	Id    int64           `json:"id" validate:"required" v:"盲盒ID"`
	Items []BoxItemUpdate `json:"items" validate:"required" v:"商品信息"`
}

type BoxCardExportReq struct {
	BoxId      int64 `form:"box_id,optional" v:"盲盒ID"`
	Status     int64 `form:"status,optional" validate:"omitempty,oneof=1 2 5" v:"状态"`
	PrintCount int64 `form:"print_count,optional" v:"打印次数"`
}

type BoxCardInfo struct {
	CardNo       string `json:"card_no"`
	Status       int64  `json:"status"`
	BoxId        int64  `json:"box_id"`
	BoxTitle     string `json:"box_title"`
	MainOrderNo  string `json:"main_order_no"`
	UseTime      string `json:"use_time"`
	RepealTime   string `json:"repeal_time"`
	RepealName   string `json:"repeal_name"`
	RepealRemark string `json:"repeal_remark"`
	CreatedTime  string `json:"created_time"`
}

type BoxCardListReq struct {
	Page   int64  `form:"page,default=1" validate:"min=1" v:"页码"`
	Limit  int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
	BoxId  int64  `form:"box_id,optional" v:"盲盒ID"`
	CardNo string `form:"card_no,optional" v:"卡号"`
	Status int64  `form:"status,optional" validate:"omitempty,oneof=1 2 5" v:"状态"`
}

type BoxCardListResp struct {
	List  []BoxCardInfo `json:"list"`
	Total int64         `json:"total"`
}

type BoxClaimReq struct {
	Id        int64 `json:"id" validate:"required" v:"盲盒订单ID"`
	BoxType   int   `json:"box_type" validate:"required,oneof=2 3" v:"领取类型:2=自己领取,3=赠送"`
	AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
}

type BoxClaimResp struct {
	SubOrderNo string `json:"sub_order_no"`
	Message    string `json:"message"`
}

type BoxCreateReq struct {
	Title         string  `json:"title" validate:"required" v:"盲盒名称"`
	AvatarImage   string  `json:"avatar_image" validate:"required" v:"列表图"`
	ValidTimeUnit string  `json:"valid_time_unit" validate:"required,oneof=hour day week month year" v:"有效期单位"`
	ValidTimeNum  int64   `json:"valid_time_num" validate:"required,min=1" v:"有效期数量"`
	Price         float64 `json:"price,default=0" validate:"min=0" v:"售价"`
	OnsaleStatus  int64   `json:"onsale_status,default=3" validate:"oneof=2 3" v:"上架状态"`
}

type BoxDeleteReq struct {
	Id int64 `json:"id" validate:"required" v:"盲盒ID"`
}

type BoxDetailReq struct {
	Id int64 `form:"id" validate:"required" v:"盲盒ID"`
}

type BoxDetailResp struct {
	BoxInfo
	Items []BoxItem `json:"items"`
}

type BoxGenerateCardsReq struct {
	BoxId int64 `json:"box_id" validate:"required" v:"盲盒ID"`
	Num   int64 `json:"num" validate:"required,min=1,max=1000" v:"生成数量"`
}

type BoxGenerateCardsResp struct {
	GeneratedNum int64 `json:"generated_num"`
}

type BoxGiftAcceptReq struct {
	ShareId   int64 `json:"share_id" validate:"required" v:"分享ID"`
	AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
}

type BoxGiftAcceptResp struct {
	OrderNo string `json:"order_no"`
	Message string `json:"message"`
}

type BoxGiftCancelReq struct {
	ShareId int64 `json:"share_id" validate:"required" v:"分享ID"`
}

type BoxGiftDetailReq struct {
	ShareId int64 `form:"share_id" validate:"required" v:"分享ID"`
}

type BoxGiftDetailResp struct {
	ShareId        int64  `json:"share_id"`
	BoxTitle       string `json:"box_title"`
	BoxAvatarImage string `json:"box_avatar_image"`
	PeriodLabel    string `json:"period_label"`
	ExpireTime     string `json:"expire_time"`
	Status         int64  `json:"status"`
	SenderName     string `json:"sender_name"`
}

type BoxGiftRejectReq struct {
	ShareId int64 `json:"share_id" validate:"required" v:"分享ID"`
}

type BoxGiftReq struct {
	BoxMainOrderId int64 `json:"box_main_order_id" validate:"required" v:"盲盒主订单ID"`
}

type BoxGiftResp struct {
	ShareId        int64  `json:"share_id"`
	BoxTitle       string `json:"box_title"`
	BoxAvatarImage string `json:"box_avatar_image"`
	PeriodLabel    string `json:"period_label"`
	ExpireTime     string `json:"expire_time"`
	Status         int64  `json:"status"`
	SenderName     string `json:"sender_name"`
	ShareUrl       string `json:"share_url"`
}

type BoxInfo struct {
	Id            int64   `json:"id"`
	Title         string  `json:"title"`
	AvatarImage   string  `json:"avatar_image"`
	ValidTimeUnit string  `json:"valid_time_unit"`
	ValidTimeNum  int64   `json:"valid_time_num"`
	Price         float64 `json:"price"`
	OnsaleStatus  int64   `json:"onsale_status"`
	CreateNum     int64   `json:"create_num"`
	ActiveNum     int64   `json:"active_num"`
	GetNum        int64   `json:"get_num"`
	CreatedTime   string  `json:"created_time"`
	UpdateTime    string  `json:"update_time"`
}

type BoxItem struct {
	Id           int64         `json:"id"`
	BoxId        int64         `json:"box_id"`
	Type         int64         `json:"type"`
	ItemsInfo    []BoxItemInfo `json:"items_info"`
	ClaimedCount int64         `json:"claimed_count"` // 已领取人数
}

type BoxItemInfo struct {
	Name      string `json:"name"`
	ShortCode string `json:"short_code"`
	Num       int64  `json:"num"`
}

type BoxItemUpdate struct {
	Id        int64         `json:"id" validate:"required" v:"盲盒项目ID"`
	Type      int64         `json:"type" validate:"required,oneof=1 2" v:"类型"`
	ItemsInfo []BoxItemInfo `json:"items_info" validate:"required,min=1" v:"商品详情"`
}

type BoxRedeemReq struct {
	EncryptedCardNo string `json:"encrypted_card_no" validate:"required" v:"加密的卡号"`
	Password        string `json:"password" validate:"required" v:"密码"`
}

type BoxRedeemResp struct {
	MainOrderNo string `json:"main_order_no"`
}

type BoxRepealCardReq struct {
	CardNo string `json:"card_no" validate:"required" v:"卡号"`
	Remark string `json:"remark,optional" v:"作废备注"`
}

type BoxSetOnsaleStatusReq struct {
	Id           int64 `json:"id" validate:"required" v:"盲盒ID"`
	OnsaleStatus int64 `json:"onsale_status" validate:"required,oneof=2 3" v:"上架状态"`
}

type BoxUpdateReq struct {
	Id          int64  `json:"id" validate:"required" v:"盲盒ID"`
	Title       string `json:"title" validate:"required" v:"盲盒名称"`
	AvatarImage string `json:"avatar_image" validate:"required" v:"列表图"`
}

type CancelOrderReq struct {
	MainOrderNo string `json:"main_order_no" validate:"required" v:"主订单号"`
}

type CancelOrderResp struct {
	Message      string  `json:"message"`
	RefundAmount float64 `json:"refund_amount"` // 退还的礼金金额
}

type ConfirmReceiptReq struct {
	SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
}

type ConfirmReceiptResp struct {
	Message string `json:"message"`
}

type DetailReq struct {
	IdFU
}

type DetailResp struct {
	Title       string `json:"title"`
	Notes       string `json:"notes"`
	Nickname    string `json:"nickname"`
	AvatarImage string `json:"avatar_image"`
}

type DirectRefundItem struct {
	OrderNo  string `json:"order_no"`  // 订单号
	Success  bool   `json:"success"`   // 是否成功
	RefundNo string `json:"refund_no"` // 退款单号
	Message  string `json:"message"`   // 结果消息
}

type DirectRefundReq struct {
	RefundAmount float64 `json:"refund_amount" validate:"required,gt=0" v:"退款金额"`
	OrderNos     string  `json:"order_nos" validate:"required" v:"商家订单号(多个用逗号分割)"`
}

type DirectRefundResp struct {
	ErrorCode int64              `json:"error_code"`
	ErrorMsg  string             `json:"error_msg"`
	Data      []DirectRefundItem `json:"data"`
}

type GenerateTitleReq struct {
	GoodsId        uint64 `json:"goods_id" validate:"required" v:"商品id"`
	Object         string `json:"object,optional" validate:"max=10" v:"对象"`
	Scene          string `json:"scene,optional" validate:"max=10" v:"场景"`
	Vacation       string `json:"vacation,optional" validate:"max=10" v:"节假日"`
	UseGoodsDetail bool   `json:"use_goods_detail,optional" v:"是否使用商品详情"`
}

type GenerateTitleResp struct {
	Title string `json:"title"`
}

type GetOrderDetailPathResp struct {
	ErrorCode int64  `json:"errcode"`
	ErrorMsg  string `json:"errmsg"`
	Path      string `json:"path"`
}

type GetUserInfoResp struct {
	UserInfo UserInfo `json:"user_info"`
}

type GetUserWithdrawalAccountResp struct {
	WithdrawalAccount
}

type GoodsCopyReq struct {
	IdJ
}

type GoodsCreateReq struct {
	Title          string           `json:"title" validate:"required" v:"商品标题"`
	Brief          string           `json:"brief,optional" v:"商品副标题"`
	Type           int64            `json:"type,default=1" validate:"min=1,max=2" v:"商品类型"`
	ItemsInfo      string           `json:"items_info,optional" v:"商品简码"`
	Price          float64          `json:"price,default=0" validate:"min=0" v:"售价"`
	Inventory      int64            `json:"inventory,default=0" validate:"min=0" v:"库存"`
	ErpAmount      float64          `json:"erp_amount,default=0" validate:"min=0" v:"财务核算金额"`
	CashbackAmount float64          `json:"cashback_amount,default=0" validate:"min=0" v:"返现金额"`
	ProductImg     string           `json:"product_img" validate:"required" v:"商品图"`
	AvatarImage    string           `json:"avatar_image" validate:"required" v:"列表图"`
	Detail         string           `json:"detail,optional" v:"商品描述"`
	AiResource     string           `json:"ai_resource,optional" v:"AI资料"`
	OnsaleStatus   int64            `json:"onsale_status,default=3" validate:"min=2,max=3" v:"上架状态"`
	Sort           int64            `json:"sort,default=1000" validate:"min=0" v:"排序"`
	Labels         []GoodsLabelInfo `json:"labels,optional" v:"商品标签"`
}

type GoodsDeleteReq struct {
	IdJ
}

type GoodsDetailReq struct {
	IdF
}

type GoodsInfo struct {
	Id               int64            `json:"id"`
	Title            string           `json:"title"`
	Brief            string           `json:"brief"`
	Type             int64            `json:"type"`
	ItemsInfo        string           `json:"items_info"`
	Price            float64          `json:"price"`
	Inventory        int64            `json:"inventory"`
	ErpAmount        float64          `json:"erp_amount"`
	CashbackAmount   float64          `json:"cashback_amount"`
	DeductibleAmount float64          `json:"deductible_amount"`
	ProductImg       string           `json:"product_img"`
	AvatarImage      string           `json:"avatar_image"`
	Detail           string           `json:"detail"`
	AiResource       string           `json:"ai_resource"`
	OnsaleStatus     int64            `json:"onsale_status"`
	WarehouseCode    string           `json:"warehouse_code"`
	OnsaleTime       int64            `json:"onsale_time"`
	SoldOutTime      int64            `json:"sold_out_time"`
	Purchased        int64            `json:"purchased"`
	SalesUserNum     int64            `json:"sales_user_num"`
	Pv               int64            `json:"pv"`
	Sort             int64            `json:"sort"`
	VhUid            int64            `json:"vh_uid"`
	VhVosName        string           `json:"vh_vos_name"`
	CreatedTime      string           `json:"created_time"`
	UpdateTime       string           `json:"update_time"`
	Labels           []GoodsLabelInfo `json:"labels"`
	Products         []ProductInfo    `json:"products"` // 商品产品列表
}

type GoodsLabelInfo struct {
	LabelId int64  `json:"label_id"`
	Name    string `json:"name"`
}

type GoodsSetOnsaleStatusReq struct {
	IdJ
	OnsaleStatus int64 `json:"onsale_status" validate:"min=2,max=3" v:"上架状态"`
}

type GoodsUpdateReq struct {
	IdJ
	Title          string           `json:"title" validate:"required" v:"商品标题"`
	Brief          string           `json:"brief,optional" v:"商品副标题"`
	Type           int64            `json:"type" validate:"min=1,max=2" v:"商品类型"`
	ItemsInfo      string           `json:"items_info,optional" v:"商品简码"`
	Price          float64          `json:"price" validate:"min=0" v:"售价"`
	Inventory      int64            `json:"inventory" validate:"min=0" v:"库存"`
	ErpAmount      float64          `json:"erp_amount" validate:"min=0" v:"财务核算金额"`
	CashbackAmount float64          `json:"cashback_amount" validate:"min=0" v:"返现金额"`
	ProductImg     string           `json:"product_img" validate:"required" v:"商品图"`
	AvatarImage    string           `json:"avatar_image" validate:"required" v:"列表图"`
	Detail         string           `json:"detail,optional" v:"商品描述"`
	AiResource     string           `json:"ai_resource,optional" v:"AI资料"`
	OnsaleStatus   int64            `json:"onsale_status" validate:"min=2,max=3" v:"上架状态"`
	Sort           int64            `json:"sort,optional" validate:"omitempty,min=0" v:"排序"`
	Labels         []GoodsLabelInfo `json:"labels,optional" v:"商品标签"`
}

type GoodsWineNotesReq struct {
	GoodsId int64 `json:"goods_id" validate:"required"` // 商品ID
}

type GoodsWineNotesResp struct {
	HTML string `json:"html"` // 转换后的HTML内容
}

type IdF struct {
	Id int64 `form:"id" validate:"required" v:"数据id"`
}

type IdFU struct {
	Id uint64 `form:"id" validate:"required" v:"数据id"`
}

type IdJ struct {
	Id int64 `json:"id" validate:"required" v:"数据id"`
}

type IdJU struct {
	Id uint64 `json:"id" validate:"required" v:"数据id"`
}

type LabelCreateReq struct {
	Name string `json:"name" validate:"required" v:"分类名称"`
}

type LabelCreateResp struct {
	Id     int64  `json:"id"`
	Name   string `json:"name"`
	Status int64  `json:"status"`
}

type LabelDeleteReq struct {
	IdJ
}

type LabelDetailReq struct {
	IdF
}

type LabelInfo struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Sort        int64  `json:"sort"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdateTime  string `json:"update_time"`
}

type LabelUpdateReq struct {
	IdJ
	Name   string `json:"name" validate:"required" v:"分类名称"`
	Sort   int64  `json:"sort" validate:"min=0" v:"排序"`
	Status int64  `json:"status" validate:"min=0,max=1" v:"状态"`
}

type LotteryActivityCreateReq struct {
	Title       string `json:"title" validate:"required,max=50" v:"活动名称"`
	Describe    string `json:"describe" validate:"required,max=200" v:"活动描述"`
	Status      uint64 `json:"status,default=0" validate:"min=0,max=1" v:"活动状态"`
	StartTime   string `json:"start_time" validate:"required" v:"开始时间"`
	GoodsId     uint64 `json:"goods_id" validate:"required" v:"商品ID"`
	GoodsTitle  string `json:"goods_title" validate:"required,max=50" v:"商品标题"`
	GoodsImg    string `json:"goods_img" validate:"required" v:"商品图片"`
	Total       uint64 `json:"total" validate:"required,min=1" v:"开奖人数门槛"`
	WinnerCount uint64 `json:"winner_count" validate:"required,min=1" v:"中奖人数配额"`
}

type LotteryActivityInfo struct {
	Id               uint64 `json:"id"`
	Title            string `json:"title"`
	Describe         string `json:"describe"`
	Status           uint64 `json:"status"`
	StartTime        string `json:"start_time"`
	GoodsId          uint64 `json:"goods_id"`
	GoodsTitle       string `json:"goods_title"`
	GoodsImg         string `json:"goods_img"`
	Total            uint64 `json:"total"`
	WinnerCount      uint64 `json:"winner_count"`
	ParticipantCount uint64 `json:"participant_count"`
	CreateTime       string `json:"create_time"`
}

type LotteryActivityStatusUpdateReq struct {
	IdJU
	Status uint64 `json:"status" validate:"min=0,max=1" v:"活动状态"`
}

type LotteryActivityUpdateReq struct {
	IdJU
	LotteryActivityCreateReq
}

type LotteryOrderItem struct {
	GoodsId   int64 `json:"goods_id" validate:"required" v:"商品ID"`
	AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
	Uid       int64 `json:"uid" validate:"required" v:"用户ID"`
}

type LotteryOrderResult struct {
	Uid         int64  `json:"uid"`           // 用户ID
	MainOrderNo string `json:"main_order_no"` // 主订单号
	SubOrderNo  string `json:"sub_order_no"`  // 子订单号
}

type MiniGoodsDetailReq struct {
	IdF
}

type MiniGoodsDetailResp struct {
	Id               int64   `json:"id"`
	Title            string  `json:"title"`
	Brief            string  `json:"brief"`
	Type             int64   `json:"type"`
	ItemsInfo        string  `json:"items_info"`
	Price            float64 `json:"price"`
	Inventory        int64   `json:"inventory"`
	ErpAmount        float64 `json:"erp_amount"`
	CashbackAmount   float64 `json:"cashback_amount"`
	DeductibleAmount float64 `json:"deductible_amount"`
	ProductImg       string  `json:"product_img"`
	AvatarImage      string  `json:"avatar_image"`
	Detail           string  `json:"detail"`
	AiResource       string  `json:"ai_resource"`
	OnsaleStatus     int64   `json:"onsale_status"`
	Purchased        int64   `json:"purchased"`
	SalesUserNum     int64   `json:"sales_user_num"`
	Pv               int64   `json:"pv"`
}

type MiniGoodsInfo struct {
	Id               int64            `json:"id"`
	Title            string           `json:"title"`
	Brief            string           `json:"brief"`
	Type             int64            `json:"type"`
	Price            float64          `json:"price"`
	Inventory        int64            `json:"inventory"`
	CashbackAmount   float64          `json:"cashback_amount"`
	DeductibleAmount float64          `json:"deductible_amount"`
	AvatarImage      string           `json:"avatar_image"`
	SalesUserNum     int64            `json:"sales_user_num"`
	Labels           []GoodsLabelInfo `json:"labels"`
}

type MiniGoodsListReq struct {
	Paging
	Title            string `form:"title,optional"`                                // 商品名称筛选，可选
	SalesUserNumSort string `form:"sales_user_num_sort,optional,options=asc|desc"` // 购买人数排序，可选
	PriceSort        string `form:"price_sort,optional,options=asc|desc"`          // 价格排序，可选
	LabelName        string `form:"label_name,optional"`                           // 标签名称筛选，可选
}

type MiniGoodsListResp struct {
	List  []MiniGoodsInfo `json:"list"`
	Total int64           `json:"total"`
}

type MiniPayResp struct {
	AppId     string `json:"app_id"`
	TimeStamp string `json:"time_stamp"`
	NonceStr  string `json:"nonce_str"`
	Package   string `json:"package"`
	SignType  string `json:"sign_type"`
	PaySign   string `json:"pay_sign"`
}

type MiniProgramBannerInfo struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Picture   string `json:"picture"`
	JumpType  int64  `json:"jump_type"`
	JumpValue string `json:"jump_value"`
}

type MiniProgramBannerListResp struct {
	List []MiniProgramBannerInfo `json:"list"`
}

type MyShareInfo struct {
	Id         uint64  `json:"id"`
	GoodsId    uint64  `json:"goods_id"`
	GoodsTitle string  `json:"goods_title"`
	GoodsImg   string  `json:"goods_img"`
	Channel    uint64  `json:"channel"`
	Title      string  `json:"title"`
	Notes      string  `json:"notes"`
	CreateTime string  `json:"create_time"`
	BuyCount   uint64  `json:"buy_count"`
	BuyMoney   float64 `json:"buy_money"`
}

type MyShareListReq struct {
	Paging
	IsBuy bool `form:"is_buy,optional"`
}

type MyShareListResp struct {
	List  []MyShareInfo `json:"list"`
	Total int64         `json:"total"`
}

type MyShareResp struct {
	ShareCt        int64   `json:"share_ct"`
	BuyCt          int64   `json:"buy_ct"`
	CashbackAmount float64 `json:"cashback_amount"`
}

type OrderBoxInfo struct {
	BoxId       int64                `json:"box_id"`
	Title       string               `json:"title"`
	AvatarImage string               `json:"avatar_image"`
	BoxItems    []OrderBoxItemDetail `json:"box_items"` // 盲盒项目详情列表
}

type OrderBoxItemDetail struct {
	Id           int64  `json:"id"`            // vh_order_main_box.id
	BoxType      int64  `json:"box_type"`      // 盲盒领取类型:1=待领取,2=自己领取,3=赠送
	MonthLabel   string `json:"month_label"`   // 月份标识(如"2025年01月")
	CanClaim     bool   `json:"can_claim"`     // 是否可以领取
	CanTransfer  bool   `json:"can_transfer"`  // 是否可以转赠
	BeginTime    string `json:"begin_time"`    // 开始领取时间
	EndTime      string `json:"end_time"`      // 结束领取时间
	GetTime      string `json:"get_time"`      // 领取时间
	OrderNo      string `json:"order_no"`      // 子订单号 (vh_order_main_box.order_no)
	ClaimedPhone string `json:"claimed_phone"` // 领取人手机号(已脱敏)
}

type OrderBoxItemInfo struct {
	Name      string `json:"name"`       // 产品名称
	ShortCode string `json:"short_code"` // 简码
	Num       int64  `json:"num"`        // 数量
}

type OrderCreateReq struct {
	GoodsId          int64   `json:"goods_id" validate:"required" v:"商品ID"`
	Quantity         int64   `json:"quantity" validate:"required,min=1" v:"购买数量"`
	AddressId        int64   `json:"address_id" validate:"required" v:"收货地址ID"`
	DeductibleAmount float64 `json:"deductible_amount,default=0" validate:"min=0" v:"使用礼金额度"`
	ShareId          int64   `json:"share_id,optional" v:"分享ID"`
}

type OrderCreateResp struct {
	MainOrderNo      string  `json:"main_order_no"`
	SubOrderNo       string  `json:"sub_order_no"`
	PaymentAmount    float64 `json:"payment_amount"`
	CashAmount       float64 `json:"cash_amount"`
	DeductibleAmount float64 `json:"deductible_amount"`
	OrderStatus      int64   `json:"order_status"`
	NeedPayment      bool    `json:"need_payment"`
	CancelTimeout    int64   `json:"cancel_timeout"`
}

type OrderDetailReq struct {
	SubOrderNo  string `form:"sub_order_no,optional" v:"子订单号"`
	MainOrderNo string `form:"main_order_no,optional" v:"主订单号"`
	Id          string `form:"id,optional" v:"订单号(兼容微信跳转)"`
	Channel     int64  `form:"channel,optional" v:"来源渠道"`
}

type OrderDetailResp struct {
	SubOrderNo       string         `json:"sub_order_no"`      // 子订单号
	MainOrderNo      string         `json:"main_order_no"`     // 主订单号
	OrderType        int64          `json:"order_type"`        // 订单类型:1=普通商品,2=抽奖商品,3=盲盒商品
	OrderStatus      int64          `json:"order_status"`      // 订单状态
	OrderStatusText  string         `json:"order_status_text"` // 订单状态文本
	OrderQty         int64          `json:"order_qty"`         // 购买数量
	GoodsTitle       string         `json:"goods_title"`       // 商品名称(从快照获取)
	GoodsImage       string         `json:"goods_image"`       // 商品图片(从快照获取)
	GoodsPrice       float64        `json:"goods_price"`       // 商品价格(从快照获取)
	GoodsInfo        OrderGoodsInfo `json:"goods_info"`        // 商品详情
	CreatedTime      string         `json:"created_time"`      // 下单时间
	PaymentTime      string         `json:"payment_time"`      // 支付时间
	DeliveryTime     string         `json:"delivery_time"`     // 发货时间
	CompletedTime    string         `json:"completed_time"`    // 完成时间
	CancelTime       string         `json:"cancel_time"`       // 取消时间
	GoodsAmount      float64        `json:"goods_amount"`      // 商品金额
	DeductibleAmount float64        `json:"deductible_amount"` // 礼金抵扣
	PaymentAmount    float64        `json:"payment_amount"`    // 实付金额
	CashAmount       float64        `json:"cash_amount"`       // 现金支付金额
	ExpressType      int64          `json:"express_type"`      // 快递方式
	ExpressNumber    string         `json:"express_number"`    // 快递单号
	ExpressName      string         `json:"express_name"`      // 快递公司名称
	ConsigneeName    string         `json:"consignee_name"`    // 收货人姓名
	ConsigneePhone   string         `json:"consignee_phone"`   // 收货人电话
	Province         string         `json:"province"`          // 省份
	City             string         `json:"city"`              // 城市
	District         string         `json:"district"`          // 区县
	Address          string         `json:"address"`           // 详细地址
	CanPay           bool           `json:"can_pay"`           // 是否可以支付
	CanCancel        bool           `json:"can_cancel"`        // 是否可以取消
	CanConfirm       bool           `json:"can_confirm"`       // 是否可以确认收货
	CanRepurchase    bool           `json:"can_repurchase"`    // 是否可以再次购买
	Remarks          string         `json:"remarks"`           // 订单备注
	RefundStatus     int64          `json:"refund_status"`     // 退款状态
	RefundAmount     float64        `json:"refund_amount"`     // 退款金额
}

type OrderGoodsInfo struct {
	GoodsId     int64   `json:"goods_id"`
	Title       string  `json:"title"`
	AvatarImage string  `json:"avatar_image"`
	Price       float64 `json:"price"`
	Quantity    int64   `json:"quantity"`
}

type OrderItem struct {
	Uid             int64                `json:"uid"`             // 用户ID
	OrderType       int64                `json:"order_type"`      // 订单类型:1=普通商品,2=抽奖商品,3=盲盒商品
	Title           string               `json:"title"`           // 订单标题(商品名称或盲盒名称)
	AvatarImage     string               `json:"avatar_image"`    // 订单图片
	MainOrderNo     string               `json:"main_order_no"`   // 主订单号
	SubOrderNo      string               `json:"sub_order_no"`    // 子订单号(普通/抽奖订单有值，盲盒订单为空)
	OrderQty        int64                `json:"order_qty"`       // 订单数量
	OrderStatus     int64                `json:"order_status"`    // 订单状态
	PaymentAmount   float64              `json:"payment_amount"`  // 支付总金额
	CashAmount      float64              `json:"cash_amount"`     // 现金支付金额
	CreatedTime     string               `json:"created_time"`    // 创建时间
	PaymentTime     string               `json:"payment_time"`    // 支付时间
	ValidTimeUnit   string               `json:"valid_time_unit"` // 有效期单位(盲盒专用，普通订单为空)
	ValidTimeNum    int64                `json:"valid_time_num"`  // 有效期数量(盲盒专用，普通订单为0)
	BoxItems        []OrderBoxItemDetail `json:"box_items"`       // 盲盒项目列表(盲盒订单专用，普通订单为空)
	HasUnclaimedBox bool                 `json:"-"`               // 是否有未领取的盲盒项目(仅用于排序，不输出到JSON)
}

type OrderListReq struct {
	Page   int64 `form:"page,default=1" validate:"min=1" v:"页码"`
	Limit  int64 `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
	Status int64 `form:"status,optional" validate:"min=0,max=4" v:"订单状态"`
}

type OrderListResp struct {
	List        []OrderItem      `json:"list"`
	Total       int64            `json:"total"`
	StatusCount OrderStatusCount `json:"status_count"` // 状态统计
}

type OrderRefundReq struct {
	SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
	Reason     string `json:"reason,optional" v:"退款原因"`
}

type OrderRefundResp struct {
	ErrorCode int64  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	Data      bool   `json:"data"`
}

type OrderRepushErpReq struct {
	SubOrderNo string `json:"sub_order_no" validate:"required" v:"子订单号"`
}

type OrderRepushErpResp struct {
	ErrorCode int64  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	Data      bool   `json:"data"`
}

type OrderStatusCount struct {
	PendingPayment  int64 `json:"pending_payment"`  // 待支付数量
	PendingShipment int64 `json:"pending_shipment"` // 待发货数量
	Shipped         int64 `json:"shipped"`          // 已发货数量
	Completed       int64 `json:"completed"`        // 已完成数量
}

type Paging struct {
	Page  int64 `form:"page,default=1"`
	Limit int64 `form:"limit,default=10"`
}

type ParticipantUserInfo struct {
	Nickname string `json:"nickname"`
	JoinTime string `json:"join_time"`
}

type PartnerTasksReq struct {
	Level int64 `json:"level" validate:"required,min=1" v:"合伙人等级"`
}

type PartnerTasksResp struct {
	Tasks        []TaskInfo `json:"tasks"`         // 任务列表
	AllCompleted bool       `json:"all_completed"` // 是否全部完成
}

type PaymentStatusReq struct {
	MainOrderNo string `form:"main_order_no" validate:"required" v:"主订单号"`
}

type PaymentStatusResp struct {
	PaymentStatus    int64   `json:"payment_status"`    // 支付状态:0=待支付,1=已支付,2=已发货,3=已完成,4=已取消
	MainOrderNo      string  `json:"main_order_no"`     // 主订单号
	CashAmount       float64 `json:"cash_amount"`       // 微信支付金额
	DeductibleAmount float64 `json:"deductible_amount"` // 礼金支付金额
	PaymentTime      string  `json:"payment_time"`      // 支付时间
	CashbackAmount   float64 `json:"cashback_amount"`   // 返现金额
}

type ProductInfo struct {
	Code     string `json:"code"`     // 简码
	Quantity int64  `json:"quantity"` // 数量
}

type RSADecryptReq struct {
	EncryptedData string `json:"encrypted_data" validate:"required" v:"加密数据"`
}

type RSADecryptResp struct {
	DecryptedData string `json:"decrypted_data"`
}

type RefineTitleReq struct {
	Title string `json:"title" validate:"required,max=800" v:"推荐语"`
}

type RefineTitleResp struct {
	Title string `json:"title"`
}

type ShareBuyInfo struct {
	Nickname    string  `json:"nickname"`
	AvatarImage string  `json:"avatar_image"`
	Money       float64 `json:"money"`
	CreateTime  string  `json:"create_time"`
}

type ShareBuyListReq struct {
	Paging
	IdFU
}

type ShareBuyListResp struct {
	List  []ShareBuyInfo `json:"list"`
	Total int64          `json:"total"`
}

type ShareCreateReq struct {
	GoodsId uint64 `json:"goods_id" validate:"required" v:"商品id"`
	Channel uint64 `json:"channel" validate:"min=1,max=4" v:"分享渠道"`
	Title   string `json:"title,optional" validate:"max=800" v:"推荐词"`
	Notes   string `json:"notes,optional" validate:"max=50" v:"分享备注"`
}

type ShareCreateResp struct {
	ShareId int64 `json:"share_id"`
}

type ShipmentNotifyReq struct {
	MainOrderNo string `json:"main_order_no,optional" v:"主订单号"`
	OrderNo     string `json:"orderNo" validate:"required" v:"子订单号"`
	WayBill     string `json:"wayBill" validate:"required" v:"运单号"`
	ExpressType int64  `json:"expressType" validate:"required" v:"快递方式"`
	Platform    int64  `json:"platform,optional" v:"平台"`
	IsUp        int64  `json:"is_up,optional" v:"是否上架"`
}

type ShipmentNotifyResp struct {
	ErrorCode int64  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	Message   string `json:"message"`
}

type ShortUrlRedirectReq struct {
	Url string `json:"url" validate:"required" v:"短链接地址"`
}

type ShortUrlRedirectResp struct {
	FinalUrl      string `json:"final_url"`
	RedirectCount int    `json:"redirect_count"`
}

type SimpleLabelInfo struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type SimpleLabelListReq struct {
}

type SimpleLabelListResp struct {
	List []SimpleLabelInfo `json:"list"`
}

type TaskInfo struct {
	TaskId    int64  `json:"task_id"`   // 任务ID
	TaskName  string `json:"task_name"` // 任务名称
	TaskType  int64  `json:"task_type"` // 任务类型：1=分享数,2=考试
	Required  int64  `json:"required"`  // 要求完成数量
	Completed int64  `json:"completed"` // 已完成数量
	Status    string `json:"status"`    // 状态：completed=已完成,incomplete=未完成
}

type UpdateOrderDetailPathReq struct {
	Path string `json:"path" validate:"required" v:"订单详情路径"`
}

type UpdateOrderDetailPathResp struct {
	ErrorCode int64  `json:"errcode"`
	ErrorMsg  string `json:"errmsg"`
}

type UserAddressInfo struct {
	Id             int64  `json:"id"`
	Uid            int64  `json:"uid"`
	ProvinceId     int64  `json:"province_id"`
	CityId         int64  `json:"city_id"`
	TownId         int64  `json:"town_id"`
	Address        string `json:"address"`
	IsDefault      int64  `json:"is_default"`
	Label          string `json:"label"`
	Code           string `json:"code"`
	Consignee      string `json:"consignee"`
	ConsigneePhone string `json:"consignee_phone"`
	ProvinceName   string `json:"province_name"`
	CityName       string `json:"city_name"`
	TownName       string `json:"town_name"`
	CreatedTime    string `json:"created_time"`
	UpdateTime     string `json:"update_time"`
}

type UserInfo struct {
	Id             int64   `json:"id"`
	AppletOpenid   string  `json:"applet_openid"`
	Telephone      string  `json:"telephone"`
	Nickname       string  `json:"nickname"`
	AvatarImage    string  `json:"avatar_image"`
	Sex            int64   `json:"sex"`
	Birthday       string  `json:"birthday"`
	IsDisabled     int64   `json:"is_disabled"`
	Balance        float64 `json:"balance"`
	Level          int64   `json:"level"`
	LastLoginTime  string  `json:"last_login_time"`
	CreatedTime    string  `json:"created_time"`
	UpdateTime     string  `json:"update_time"`
	TotalBalance   float64 `json:"total_balance"`
	LevelStartTime string  `json:"level_start_time"`
	LevelEndTime   string  `json:"level_end_time"`
	Type           int64   `json:"type"`
	LevelStatus    int64   `json:"level_status"`
}

type UserLevelCreateReq struct {
	Level            int64                    `json:"level" validate:"required" v:"等级"`
	Name             string                   `json:"name" validate:"required" v:"等级名称"`
	LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
	ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
	PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
	CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
	CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
	Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
	Tasks            []UserLevelTaskCreateReq `json:"tasks,optional"`
}

type UserLevelDeleteReq struct {
	Id int64 `json:"id" validate:"required" v:"等级ID"`
}

type UserLevelDetailReq struct {
	Id int64 `form:"id" validate:"required" v:"等级ID"`
}

type UserLevelInfo struct {
	Id               int64               `json:"id"`
	Level            int64               `json:"level"`
	Name             string              `json:"name"`
	LevelName        string              `json:"level_name"`
	ShareBrokerage   float64             `json:"share_brokerage"`
	PaymentBrokerage float64             `json:"payment_brokerage"`
	CashBrokerage    float64             `json:"cash_brokerage"`
	CashMinAmount    float64             `json:"cash_min_amount"`
	Duration         int64               `json:"duration"`
	CreatedTime      string              `json:"created_time"`
	UpdateTime       string              `json:"update_time"`
	Tasks            []UserLevelTaskInfo `json:"tasks"`
}

type UserLevelTaskCreateReq struct {
	Name string `json:"name" validate:"required" v:"任务名称"`
	Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
	Num  int64  `json:"num" validate:"required" v:"完成数量"`
}

type UserLevelTaskInfo struct {
	Id    int64  `json:"id"`
	Level int64  `json:"level"`
	Name  string `json:"name"`
	Type  int64  `json:"type"`
	Num   int64  `json:"num"`
}

type UserLevelTaskUpdateReq struct {
	Id   int64  `json:"id,optional" v:"任务ID"`
	Name string `json:"name" validate:"required" v:"任务名称"`
	Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
	Num  int64  `json:"num" validate:"required" v:"完成数量"`
}

type UserLevelUpdateReq struct {
	Id               int64                    `json:"id" validate:"required" v:"等级ID"`
	Level            int64                    `json:"level" validate:"required" v:"等级"`
	Name             string                   `json:"name" validate:"required" v:"等级名称"`
	LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
	ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
	PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
	CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
	CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
	Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
	Tasks            []UserLevelTaskUpdateReq `json:"tasks,optional"`
}

type UserSetDisabledReq struct {
	Id         int64 `json:"id" validate:"required" v:"用户ID"`
	IsDisabled int64 `json:"is_disabled" validate:"min=1,max=2" v:"禁用状态"`
}

type UserUpdateInfoReq struct {
	Nickname    string `json:"nickname" validate:"required" v:"用户昵称"`
	AvatarImage string `json:"avatar_image" validate:"required" v:"用户头像"`
}

type WeChatAppletOneclickLoginReq struct {
	Code      string `json:"code" validate:"required" v:"临时授权码"`
	PCode     string `json:"p_code" validate:"required" v:"手机号获取凭证"`
	SourceUid string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
}

type WeChatAppletOneclickLoginResp struct {
	UID     int64  `json:"uid"`
	Token   string `json:"token"`
	OpenID  string `json:"openid"`
	UnionID string `json:"unionid"`
	Name    string `json:"name"`
	Avatar  string `json:"avatar"`
}

type WeChatLoginReq struct {
	Code        string `json:"code" validate:"required" v:"临时授权码"`
	PCode       string `json:"p_code" validate:"required" v:"手机号获取凭证"`
	SourceUid   string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
	Nickname    string `json:"nickname,optional" validate:"omitempty" v:"用户昵称"`
	AvatarImage string `json:"avatar_image,optional" validate:"omitempty" v:"用户头像"`
}

type WeChatLoginResp struct {
	UID      int64    `json:"uid"`
	Token    string   `json:"token"`
	OpenID   string   `json:"openid"`
	UnionID  string   `json:"unionid"`
	Name     string   `json:"name"`
	Avatar   string   `json:"avatar"`
	UserInfo UserInfo `json:"user_info"`
}

type WechatPayReq struct {
	MainOrderNo string `json:"main_order_no" validate:"required" v:"主订单号"`
}

type WechatPayResp struct {
	AppId     string `json:"appId"`
	TimeStamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	SignType  string `json:"signType"`
	PaySign   string `json:"paySign"`
}

type WineNotesConvertReq struct {
	UUID string `json:"uuid" validate:"required"` // WineNotes文章UUID
}

type WineNotesConvertResp struct {
	HTML string `json:"html"` // 转换后的HTML内容
}

type WithdrawalAccount struct {
	AliAccount string `json:"ali_account" validate:"required" v:"支付宝账号"`
	AliName    string `json:"ali_name" validate:"required" v:"姓名"`
}

type WithdrawalAccountUpdateReq struct {
	WithdrawalAccount
}

type WithdrawalCreateReq struct {
	Amount float64 `json:"amount" validate:"min=100" v:"提现金额"`
}

type WithdrawalListInfo struct {
	Id         uint64  `json:"id"`
	Uid        uint64  `json:"uid"`
	AliName    string  `json:"ali_name"`
	AliAccount string  `json:"ali_account"`
	Telephone  string  `json:"telephone"`
	Amount     float64 `json:"amount"`
	Status     uint64  `json:"status"`
	BackNote   string  `json:"back_note"`
	CreateTime string  `json:"create_time"`
	HandleTime string  `json:"handle_time"`
}

type WithdrawalListReq struct {
	Paging
	AliName    string `form:"ali_name,optional"`
	AliAccount string `form:"ali_account,optional"`
	Telephone  string `form:"telephone,optional"`
	Status     int64  `form:"status,optional"`
	StartTime  string `form:"start_time,optional"`
	EndTime    string `form:"end_time,optional"`
}

type WithdrawalListResp struct {
	List  []WithdrawalListInfo `json:"list"`
	Total int64                `json:"total"`
}

type WithdrawalReviewReq struct {
	IdJU
	Status   int64  `json:"status,options=[2,4]" validate:"required" v:"状态"`
	BackNote string `json:"back_note,optional" v:"驳回原因"`
}
