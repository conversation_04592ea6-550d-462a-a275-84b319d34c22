syntax = "v1"

info (
	title:   "WineNotes API"
	desc:    "酒类笔记转换服务"
	author:  "system"
	version: "v1"
)

type (
	// WineNotes转换请求
	WineNotesConvertReq {
		UUID string `json:"uuid" validate:"required"` // WineNotes文章UUID
	}

	// WineNotes转换响应
	WineNotesConvertResp {
		HTML string `json:"html"` // 转换后的HTML内容
	}

	// 商品WineNotes处理请求
	GoodsWineNotesReq {
		GoodsId int64 `json:"goods_id" validate:"required"` // 商品ID
	}

	// 商品WineNotes处理响应
	GoodsWineNotesResp {
		HTML string `json:"html"` // 转换后的HTML内容
	}
)

@server(
	middleware: Global
	group: winenotes
	prefix: /mulandoGreateDestiny/v1
	timeout: 10s
)
service mulandoGreateDestiny {
	@doc "通过UUID转换WineNotes为HTML"
	@handler ConvertWineNotesHandler
	post /winenotes/convert (WineNotesConvertReq) returns (WineNotesConvertResp)

	@doc "处理商品的WineNotes数据"
	@handler ProcessGoodsWineNotesHandler
	post /winenotes/goods (GoodsWineNotesReq) returns (GoodsWineNotesResp)
}

// 后台管理接口
@server(
	middleware: Global,Admin
	group: winenotes
	prefix: /mulandoGreateDestiny/v1/admin
	timeout: 30s
)
service mulandoGreateDestiny {
	@doc "后台通过UUID转换WineNotes为HTML"
	@handler AdminConvertWineNotesHandler
	post /winenotes/convert (WineNotesConvertReq) returns (WineNotesConvertResp)
}
