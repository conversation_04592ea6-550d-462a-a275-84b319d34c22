package user

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetDisabledLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetDisabledLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetDisabledLogic {
	return &SetDisabledLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetDisabledLogic) SetDisabled(req *types.UserSetDisabledReq) error {
	// todo: add your logic here and delete this line

	return nil
}
