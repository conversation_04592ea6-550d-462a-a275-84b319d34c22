package lotteryActivity

import (
	"context"
	"engine/common"
	"engine/common/xerr"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.AdminLotteryActivityListReq) (resp *types.AdminLotteryActivityListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhLotteryActivity
	)

	// 构建查询条件
	where := squirrel.And{}
	// 添加标题搜索条件
	if req.Title != "" {
		where = append(where, squirrel.Like{"title": "%" + req.Title + "%"})
	}

	// 添加状态筛选条件
	if req.Status > 0 {
		where = append(where, squirrel.Eq{"status": req.Status})
	}

	// 获取总数
	wait.Go(func() error {
		ct, er := l.svcCtx.LotteryActivityModel.FindCount(l.ctx, model.CountBuilder("*", l.svcCtx.LotteryActivityModel.TableName()).Where(where))
		if er != nil {
			l.Errorf("ListLogic.LotteryActivityModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		er := l.svcCtx.LotteryActivityModel.FindRows(l.ctx, l.svcCtx.LotteryActivityModel.RowBuilder().
			Where(where).
			OrderBy("create_time DESC").
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)), &list)
		if er != nil {
			l.Errorf("ListLogic.LotteryActivityModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.AdminLotteryActivityListResp)
	resp.Total = total
	resp.List = make([]types.LotteryActivityInfo, 0, len(list))
	for _, ls := range list {
		info := types.LotteryActivityInfo{
			Id:               ls.Id,
			Title:            ls.Title,
			Describe:         ls.Describe,
			Status:           ls.Status,
			StartTime:        common.TimeToString(ls.StartTime),
			GoodsId:          ls.GoodsId,
			GoodsTitle:       ls.GoodsTitle,
			GoodsImg:         l.svcCtx.Config.ITEM.ALIURL + ls.GoodsImg,
			Total:            ls.Total,
			WinnerCount:      ls.WinnerCount,
			ParticipantCount: ls.ParticipantCount,
			CreateTime:       common.TimeToString(ls.CreateTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
