package share

import (
	"engine/api/internal/logic/miniprogram/share"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func ShareBuyListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ShareBuyListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := share.NewShareBuyListLogic(r.Context(), svcCtx)
		resp, err := l.ShareBuyList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
