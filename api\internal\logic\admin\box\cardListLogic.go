package box

import (
	"context"
	"database/sql"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type CardListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCardListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CardListLogic {
	return &CardListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CardListLogic) CardList(req *types.BoxCardListReq) (resp *types.BoxCardListResp, err error) {
	// 构建查询条件
	builder := squirrel.Select("c.card_no, c.status, c.box_id, b.title as box_title, c.main_order_no, c.use_time, c.repeal_time, c.repeal_name, c.repeal_remark, c.created_time").
		From("vh_box_cards c").
		LeftJoin("vh_box b ON c.box_id = b.id").
		Where(squirrel.Eq{"b.delete_time": 0})

	countBuilder := squirrel.Select("COUNT(*)").
		From("vh_box_cards c").
		LeftJoin("vh_box b ON c.box_id = b.id").
		Where(squirrel.Eq{"b.delete_time": 0})

	// 添加筛选条件
	if req.BoxId > 0 {
		builder = builder.Where(squirrel.Eq{"c.box_id": req.BoxId})
		countBuilder = countBuilder.Where(squirrel.Eq{"c.box_id": req.BoxId})
	}

	if req.CardNo != "" {
		builder = builder.Where(squirrel.Like{"c.card_no": "%" + req.CardNo + "%"})
		countBuilder = countBuilder.Where(squirrel.Like{"c.card_no": "%" + req.CardNo + "%"})
	}

	if req.Status > 0 {
		builder = builder.Where(squirrel.Eq{"c.status": req.Status})
		countBuilder = countBuilder.Where(squirrel.Eq{"c.status": req.Status})
	}

	// 获取总数
	total, err := l.svcCtx.BoxCardsModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("CardList FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 添加分页和排序
	builder = builder.OrderBy("c.created_time DESC").
		Limit(uint64(req.Limit)).
		Offset(uint64((req.Page - 1) * req.Limit))

	// 查询数据
	var cards []struct {
		CardNo       string         `db:"card_no"`
		Status       int64          `db:"status"`
		BoxId        int64          `db:"box_id"`
		BoxTitle     sql.NullString `db:"box_title"`
		MainOrderNo  sql.NullString `db:"main_order_no"`
		UseTime      sql.NullTime   `db:"use_time"`
		RepealTime   sql.NullTime   `db:"repeal_time"`
		RepealName   string         `db:"repeal_name"`
		RepealRemark string         `db:"repeal_remark"`
		CreatedTime  sql.NullTime   `db:"created_time"`
	}

	err = l.svcCtx.BoxCardsModel.FindRows(l.ctx, builder, &cards)
	if err != nil {
		l.Logger.Error("CardList FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据格式
	list := make([]types.BoxCardInfo, 0, len(cards))
	for _, card := range cards {
		var useTime, repealTime, createdTime string
		var mainOrderNo, boxTitle string

		if card.UseTime.Valid {
			useTime = function.FormatTime(card.UseTime.Time)
		}
		if card.RepealTime.Valid {
			repealTime = function.FormatTime(card.RepealTime.Time)
		}
		if card.CreatedTime.Valid {
			createdTime = function.FormatTime(card.CreatedTime.Time)
		}
		if card.MainOrderNo.Valid {
			mainOrderNo = card.MainOrderNo.String
		}
		if card.BoxTitle.Valid {
			boxTitle = card.BoxTitle.String
		}

		list = append(list, types.BoxCardInfo{
			CardNo:       card.CardNo,
			Status:       card.Status,
			BoxId:        card.BoxId,
			BoxTitle:     boxTitle,
			MainOrderNo:  mainOrderNo,
			UseTime:      useTime,
			RepealTime:   repealTime,
			RepealName:   card.RepealName,
			RepealRemark: card.RepealRemark,
			CreatedTime:  createdTime,
		})
	}

	return &types.BoxCardListResp{
		List:  list,
		Total: total,
	}, nil
}
