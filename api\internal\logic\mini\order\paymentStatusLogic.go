package order

import (
	"context"
	"encoding/json"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type PaymentStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPaymentStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PaymentStatusLogic {
	return &PaymentStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PaymentStatusLogic) PaymentStatus(req *types.PaymentStatusReq) (resp *types.PaymentStatusResp, err error) {
	l.Logger.Infof("PaymentStatus start: main_order_no=%s", req.MainOrderNo)

	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, req.MainOrderNo)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("订单不存在")
		}
		l.Logger.Error("PaymentStatus OrderMainModel.FindOneByMainOrderNo error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证订单所有权
	if mainOrder.Uid != uid {
		return nil, xerr.NewErrMsg("无权访问此订单")
	}

	// 查询该主订单下的所有子订单
	subOrders, err := l.svcCtx.OrderModel.FindByMainOrderId(l.ctx, mainOrder.Id)
	if err != nil {
		l.Logger.Error("PaymentStatus OrderModel.FindByMainOrderId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 计算返现金额：汇总所有子订单商品的cashback_amount
	cashbackAmount := l.calculateCashbackAmount(subOrders)

	// 格式化支付时间
	paymentTime := ""
	if mainOrder.PaymentTime > 0 {
		paymentTime = function.FormatTimestamp(mainOrder.PaymentTime)
	}

	// 构建响应
	resp = &types.PaymentStatusResp{
		PaymentStatus:    mainOrder.MainOrderStatus,
		MainOrderNo:      mainOrder.MainOrderNo,
		CashAmount:       mainOrder.CashAmount,
		DeductibleAmount: mainOrder.DeductibleAmount,
		PaymentTime:      paymentTime,
		CashbackAmount:   cashbackAmount,
	}



	return resp, nil
}

// calculateCashbackAmount 计算返现金额：汇总所有子订单商品的cashback_amount
func (l *PaymentStatusLogic) calculateCashbackAmount(subOrders []*model.VhOrder) float64 {
	totalCashback := 0.0

	for _, subOrder := range subOrders {
		// 从子订单快照中获取cashback_amount
		if subOrder.Snapshot.Valid && subOrder.Snapshot.String != "" {
			var snapshotData map[string]interface{}
			if err := json.Unmarshal([]byte(subOrder.Snapshot.String), &snapshotData); err == nil {
				if cashbackAmount, ok := snapshotData["cashback_amount"].(float64); ok {
					// 返现金额需要乘以购买数量
					totalCashback += cashbackAmount * float64(subOrder.OrderQty)
				}
			}
		}
	}

	return totalCashback
}
