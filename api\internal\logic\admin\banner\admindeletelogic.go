package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDeleteLogic {
	return &AdminDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDeleteLogic) AdminDelete(req *types.BannerDeleteReq) error {
	// 检查记录是否存在
	_, err := l.svcCtx.BannerModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDeleteLogic BannerModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 删除记录
	err = l.svcCtx.BannerModel.Delete(l.ctx, req.Id)
	if err != nil {
		l.Logger.Error("AdminDeleteLogic BannerModel.Delete error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
