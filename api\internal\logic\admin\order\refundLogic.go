package order

import (
	"context"
	"database/sql"
	"engine/common/wechat"
	"fmt"
	"strings"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/go-pay/gopay"
	"github.com/zeromicro/go-zero/core/logx"
)

type RefundLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRefundLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefundLogic {
	return &RefundLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefundLogic) Refund(req *types.OrderRefundReq) (resp *types.OrderRefundResp, err error) {
	l.Logger.Infof("开始处理订单退款: subOrderNo=%s, reason=%s", req.SubOrderNo, req.Reason)

	// 1. 查询子订单信息
	subOrder, err := l.svcCtx.OrderModel.FindOneBySubOrderNo(l.ctx, req.SubOrderNo)
	if err != nil {
		if err == model.ErrNotFound {
			l.Logger.Errorf("子订单不存在: subOrderNo=%s", req.SubOrderNo)
			return &types.OrderRefundResp{
				ErrorCode: 1001,
				ErrorMsg:  "订单不存在",
				Data:      false,
			}, nil
		}
		l.Logger.Errorf("查询子订单失败: %v", err)
		return &types.OrderRefundResp{
			ErrorCode: 1002,
			ErrorMsg:  "系统错误",
			Data:      false,
		}, nil
	}

	// 2. 检查订单状态是否可以退款
	if !l.canRefund(subOrder) {
		l.Logger.Errorf("订单状态不允许退款: subOrderNo=%s, status=%d", req.SubOrderNo, subOrder.SubOrderStatus)
		return &types.OrderRefundResp{
			ErrorCode: 1003,
			ErrorMsg:  "订单状态不允许退款",
			Data:      false,
		}, nil
	}

	// 3. 查询主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(l.ctx, subOrder.MainOrderId)
	if err != nil {
		l.Logger.Errorf("查询主订单失败: %v", err)
		return &types.OrderRefundResp{
			ErrorCode: 1004,
			ErrorMsg:  "查询主订单失败",
			Data:      false,
		}, nil
	}

	// 4. 执行退款流程
	err = l.processRefund(subOrder, mainOrder, req.Reason)
	if err != nil {
		l.Logger.Errorf("退款处理失败: %v", err)
		return &types.OrderRefundResp{
			ErrorCode: 1005,
			ErrorMsg:  "退款处理失败: " + err.Error(),
			Data:      false,
		}, nil
	}

	l.Logger.Infof("订单退款处理成功: subOrderNo=%s", req.SubOrderNo)
	return &types.OrderRefundResp{
		ErrorCode: 0,
		ErrorMsg:  "success",
		Data:      true,
	}, nil
}

// canRefund 检查订单是否可以退款
func (l *RefundLogic) canRefund(subOrder *model.VhOrder) bool {
	// 已支付(1)和已发货(2)的订单可以退款
	// 已完成(3)的订单不可以退款，因为确认收货时可能会给用户发放礼金
	return subOrder.SubOrderStatus == 1 || subOrder.SubOrderStatus == 2
}

// processRefund 处理退款流程
func (l *RefundLogic) processRefund(subOrder *model.VhOrder, mainOrder *model.VhOrderMain, reason string) error {
	var needClearUserCache bool
	var user *model.VhUser

	// 开启事务
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 1. 更新订单状态（先更新状态，避免退款成功但状态更新失败）
		err := l.updateOrderStatus(ctx, tx, subOrder)
		if err != nil {
			return fmt.Errorf("更新订单状态失败: %v", err)
		}

		// 2. 恢复库存
		err = l.restoreStock(ctx, tx, subOrder)
		if err != nil {
			return fmt.Errorf("恢复库存失败: %v", err)
		}

		// 3. 处理礼金退还
		var userInfo *model.VhUser
		userInfo, err = l.processGiftBalanceRefund(ctx, tx, subOrder, mainOrder)
		if err != nil {
			return fmt.Errorf("礼金退还失败: %v", err)
		}
		if userInfo != nil {
			needClearUserCache = true
			user = userInfo
		}

		// 4. 执行微信退款（最后执行，避免退款成功但其他操作失败）
		err = l.processWechatRefund(ctx, tx, subOrder, mainOrder, reason)
		if err != nil {
			return fmt.Errorf("微信退款失败: %v", err)
		}

		return nil
	})

	// 事务提交成功后的后续处理
	if err == nil {
		// 清除用户缓存
		if needClearUserCache && user != nil {
			clearErr := l.svcCtx.UserModel.ClearCache(l.ctx, user)
			if clearErr != nil {
				l.Logger.Errorf("清除用户缓存失败: uid=%d, error=%v", user.Id, clearErr)
				// 缓存清除失败不影响业务流程，只记录日志
			}
		}

		// 异步推送中台（事务提交后执行，确保数据一致性）
		go l.asyncPushToMiddlePlatform(mainOrder.MainOrderNo, subOrder.SubOrderNo)
	}

	return err
}

// processWechatRefund 处理微信退款
func (l *RefundLogic) processWechatRefund(ctx context.Context, tx *sql.Tx, subOrder *model.VhOrder, mainOrder *model.VhOrderMain, reason string) error {
	// 如果没有现金支付，跳过微信退款
	if mainOrder.CashAmount <= 0 {
		l.Logger.Infof("订单无现金支付，跳过微信退款: subOrderNo=%s", subOrder.SubOrderNo)
		return nil
	}

	// 生成退款单号
	refundNo := fmt.Sprintf("RF%s%06d", time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)

	// 构建退款请求参数
	refundAmount := int64(subOrder.CashAmount * 100) // 转换为分
	totalAmount := int64(mainOrder.CashAmount * 100) // 主订单总金额

	refundReq := make(gopay.BodyMap)
	refundReq.Set("out_trade_no", mainOrder.MainOrderNo)
	refundReq.Set("out_refund_no", refundNo)
	refundReq.Set("reason", reason)
	refundReq.Set("notify_url", l.svcCtx.Config.WePay.NotifyUrl)

	// V3版本金额信息
	amountMap := make(gopay.BodyMap)
	amountMap.Set("refund", refundAmount)
	amountMap.Set("total", totalAmount)
	amountMap.Set("currency", "CNY")
	refundReq.Set("amount", amountMap)

	// 根据配置判断是否为服务商模式
	if l.svcCtx.Config.WePay.SubMiniAppId != "" && l.svcCtx.Config.WePay.SubMchId != "" {
		refundReq.Set("sub_mchid", l.svcCtx.Config.WePay.SubMchId)
	}

	l.Logger.Infof("发起微信退款: subOrderNo=%s, refundNo=%s, amount=%.2f", subOrder.SubOrderNo, refundNo, subOrder.CashAmount)

	// 调用微信V3退款API
	refundResp, err := l.svcCtx.WePay.V3Refund(ctx, refundReq)
	if err != nil {
		return fmt.Errorf("微信退款API调用失败: %v", err)
	}

	// 检查退款结果
	if refundResp.Code != 0 {
		// 特殊处理：如果订单已全额退款，不视为错误
		if l.isOrderAlreadyRefunded(refundResp.Error) {
			l.Logger.Infof("订单已全额退款，跳过微信退款: subOrderNo=%s, response=%s", subOrder.SubOrderNo, refundResp.Error)
		} else {
			return fmt.Errorf("微信退款失败: %s", refundResp.Error)
		}
	}

	// 更新子订单退款信息
	updateSubOrderQuery := `UPDATE vh_order SET
		refund_status = 2,
		refund_time = ?,
		refund_money = ?,
		remarks = CONCAT(IFNULL(remarks, ''), '后台退款单号:', ?)
		WHERE id = ?`

	_, err = tx.ExecContext(ctx, updateSubOrderQuery,
		time.Now().Unix(), subOrder.CashAmount, refundNo, subOrder.Id)
	if err != nil {
		return fmt.Errorf("更新退款信息失败: %v", err)
	}

	l.Logger.Infof("微信退款成功: subOrderNo=%s, refundNo=%s, refundId=%s",
		subOrder.SubOrderNo, refundNo, refundResp.Response.RefundId)
	return nil
}

// updateOrderStatus 更新订单状态
func (l *RefundLogic) updateOrderStatus(ctx context.Context, tx *sql.Tx, subOrder *model.VhOrder) error {
	// 根据当前状态决定退款后的状态
	var newStatus int64
	if subOrder.SubOrderStatus == 1 { // 已支付 -> 已取消
		newStatus = 4
	} else if subOrder.SubOrderStatus == 2 { // 已发货 -> 已完成
		newStatus = 3
	} else {
		return fmt.Errorf("订单状态异常，无法退款: status=%d", subOrder.SubOrderStatus)
	}

	// 更新子订单状态（update_time由数据库自动维护）
	updateSubOrderQuery := `UPDATE vh_order SET sub_order_status = ? WHERE id = ?`
	_, err := tx.ExecContext(ctx, updateSubOrderQuery, newStatus, subOrder.Id)
	if err != nil {
		return fmt.Errorf("更新子订单状态失败: %v", err)
	}

	// 检查主订单下的所有子订单状态，如果都是取消或完成状态，则更新主订单状态
	err = l.updateMainOrderStatusIfNeeded(ctx, tx, subOrder.MainOrderId)
	if err != nil {
		return fmt.Errorf("更新主订单状态失败: %v", err)
	}

	l.Logger.Infof("订单状态更新成功: subOrderNo=%s, oldStatus=%d, newStatus=%d",
		subOrder.SubOrderNo, subOrder.SubOrderStatus, newStatus)
	return nil
}

// updateMainOrderStatusIfNeeded 检查并更新主订单状态
func (l *RefundLogic) updateMainOrderStatusIfNeeded(ctx context.Context, tx *sql.Tx, mainOrderId int64) error {
	// 查询主订单下所有子订单的状态
	statusQuery := `SELECT sub_order_status as status, COUNT(*) as count
		FROM vh_order
		WHERE main_order_id = ?
		GROUP BY sub_order_status`

	rows, err := tx.QueryContext(ctx, statusQuery, mainOrderId)
	if err != nil {
		return fmt.Errorf("查询子订单状态失败: %v", err)
	}
	defer rows.Close()

	var statusCounts []struct {
		Status int64 `db:"status"`
		Count  int64 `db:"count"`
	}

	for rows.Next() {
		var sc struct {
			Status int64 `db:"status"`
			Count  int64 `db:"count"`
		}
		if err := rows.Scan(&sc.Status, &sc.Count); err != nil {
			return fmt.Errorf("扫描子订单状态失败: %v", err)
		}
		statusCounts = append(statusCounts, sc)
	}

	// 分析状态分布
	var totalCount, cancelledCount, completedCount int64
	for _, sc := range statusCounts {
		totalCount += sc.Count
		if sc.Status == 4 { // 已取消
			cancelledCount += sc.Count
		} else if sc.Status == 3 { // 已完成
			completedCount += sc.Count
		}
	}

	// 决定主订单状态
	var newMainStatus int64
	if cancelledCount == totalCount {
		newMainStatus = 4 // 全部取消
	} else if (cancelledCount + completedCount) == totalCount {
		newMainStatus = 3 // 全部完成（包含取消和完成）
	} else {
		// 还有其他状态的子订单，不更新主订单状态
		return nil
	}

	// 更新主订单状态（update_time由数据库自动维护）
	updateMainOrderQuery := `UPDATE vh_order_main SET main_order_status = ? WHERE id = ?`
	_, err = tx.ExecContext(ctx, updateMainOrderQuery, newMainStatus, mainOrderId)
	if err != nil {
		return fmt.Errorf("更新主订单状态失败: %v", err)
	}

	l.Logger.Infof("主订单状态更新成功: mainOrderId=%d, newStatus=%d", mainOrderId, newMainStatus)
	return nil
}

// restoreStock 恢复库存
func (l *RefundLogic) restoreStock(ctx context.Context, tx *sql.Tx, subOrder *model.VhOrder) error {
	// 查询商品信息
	goods, err := l.svcCtx.GoodsModel.FindOne(ctx, uint64(subOrder.GoodsId))
	if err != nil {
		return fmt.Errorf("查询商品信息失败: %v", err)
	}

	// 恢复库存（update_time由数据库自动维护）
	updateStockQuery := `UPDATE vh_goods SET inventory = inventory + ? WHERE id = ?`
	_, err = tx.ExecContext(ctx, updateStockQuery, subOrder.OrderQty, subOrder.GoodsId)
	if err != nil {
		return fmt.Errorf("恢复库存失败: %v", err)
	}

	l.Logger.Infof("库存恢复成功: goodsId=%d, goodsName=%s, restoreQty=%d",
		subOrder.GoodsId, goods.Title, subOrder.OrderQty)
	return nil
}

// processGiftBalanceRefund 处理礼金退还
func (l *RefundLogic) processGiftBalanceRefund(ctx context.Context, tx *sql.Tx, subOrder *model.VhOrder, mainOrder *model.VhOrderMain) (*model.VhUser, error) {
	// 如果没有使用礼金，跳过
	if subOrder.DeductibleAmount <= 0 {
		l.Logger.Infof("订单未使用礼金，跳过礼金退还: subOrderNo=%s", subOrder.SubOrderNo)
		return nil, nil
	}

	// 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(ctx, mainOrder.Uid)
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 退还礼金到用户余额（update_time由数据库自动维护）
	newBalance := user.Balance + subOrder.DeductibleAmount
	updateUserQuery := `UPDATE vh_user SET balance = ? WHERE id = ?`
	_, err = tx.ExecContext(ctx, updateUserQuery, newBalance, mainOrder.Uid)
	if err != nil {
		return nil, fmt.Errorf("更新用户余额失败: %v", err)
	}

	// 添加余额变动记录（忽略ID、创建时间、更新时间，由数据库自动维护）
	// 生成唯一编号，避免重复操作
	uniqueCode := fmt.Sprintf("REFUND_%s_%d", subOrder.SubOrderNo, time.Now().UnixNano())
	insertBalanceHistoryQuery := `INSERT INTO vh_balance_history
		(unique_code, uid, type, amount, after_amount, operation_type, operation_name)
		VALUES (?, ?, ?, ?, ?, ?, ?)`

	_, err = tx.ExecContext(ctx, insertBalanceHistoryQuery,
		uniqueCode, mainOrder.Uid, 1, subOrder.DeductibleAmount, newBalance, 4, "系统退款")
	if err != nil {
		return nil, fmt.Errorf("添加余额记录失败: %v", err)
	}

	l.Logger.Infof("礼金退还成功: uid=%d, amount=%.2f, newBalance=%.2f",
		mainOrder.Uid, subOrder.DeductibleAmount, newBalance)

	// 返回用户信息，用于事务提交后清除缓存
	return user, nil
}

// isOrderAlreadyRefunded 判断是否为订单已全额退款的错误
func (l *RefundLogic) isOrderAlreadyRefunded(errorMsg string) bool {
	// 检查常见的已退款错误信息
	refundedMessages := []string{
		"订单已全额退款",
		//"INVALID_REQUEST",
		//"订单已退款",
		//"already refunded",
		//"full refund",
		"已全额退款",
	}

	for _, msg := range refundedMessages {
		if strings.Contains(errorMsg, msg) {
			return true
		}
	}

	return false
}

// asyncPushToMiddlePlatform 异步推送退款订单到中台
func (l *RefundLogic) asyncPushToMiddlePlatform(mainOrderNo, subOrderNo string) {
	// 创建独立的context，避免HTTP请求结束后context被取消
	ctx := context.Background()

	l.Logger.Infof("开始异步推送退款订单到中台: mainOrderNo=%s, subOrderNo=%s", mainOrderNo, subOrderNo)

	// 使用现有的订单推送服务推送到中台
	pushService := service.NewOrderPushService(ctx, l.svcCtx)
	err := pushService.PushOrderToMiddlePlatform(mainOrderNo)
	if err != nil {
		l.Logger.Errorf("退款订单推送中台失败: mainOrderNo=%s, subOrderNo=%s, error=%v",
			mainOrderNo, subOrderNo, err)
	} else {
		l.Logger.Infof("退款订单推送中台成功: mainOrderNo=%s, subOrderNo=%s", mainOrderNo, subOrderNo)
	}
}

// pushToMiddlePlatform 推送中台
func (l *RefundLogic) pushToMiddlePlatform(ctx context.Context, tx *sql.Tx, subOrder *model.VhOrder) error {
	l.Logger.Infof("开始推送退款订单到中台: subOrderNo=%s", subOrder.SubOrderNo)

	// 获取主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(ctx, subOrder.MainOrderId)
	if err != nil {
		l.Logger.Errorf("获取主订单失败: %v", err)
		return fmt.Errorf("获取主订单失败: %v", err)
	}

	// 使用现有的订单推送服务推送到中台
	// 注意：这里需要在事务外执行，因为推送服务可能需要查询最新的订单状态
	// 所以我们在这里只记录需要推送，实际推送在事务提交后异步执行
	l.Logger.Infof("退款订单将在事务提交后推送中台: mainOrderNo=%s, subOrderNo=%s",
		mainOrder.MainOrderNo, subOrder.SubOrderNo)

	return nil
}

// getAccessToken 获取微信access_token
func (l *RefundLogic) getAccessToken() (string, error) {
	// 使用系统统一的微信服务获取AccessToken
	weChatService := wechat.NewWeChatService(l.svcCtx.Config)

	res, err := weChatService.GetAccessToken()
	if err != nil {
		l.Logger.Errorf("GetAccessToken failed: %v", err)
		return "", fmt.Errorf("获取access_token失败: %v", err)
	}

	if res.AccessToken == "" {
		l.Logger.Errorf("AccessToken is empty: code=%d, msg=%s", res.Code, res.Msg)
		return "", fmt.Errorf("access_token为空: %s", res.Msg)
	}

	return res.AccessToken, nil
}
