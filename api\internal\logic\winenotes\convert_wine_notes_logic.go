package winenotes

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConvertWineNotesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 通过UUID转换WineNotes为HTML
func NewConvertWineNotesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConvertWineNotesLogic {
	return &ConvertWineNotesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConvertWineNotesLogic) ConvertWineNotes(req *types.WineNotesConvertReq) (resp *types.WineNotesConvertResp, err error) {
	// todo: add your logic here and delete this line

	return
}
