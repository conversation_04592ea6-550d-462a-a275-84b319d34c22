package decrypt

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShortUrlRedirectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShortUrlRedirectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShortUrlRedirectLogic {
	return &ShortUrlRedirectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShortUrlRedirectLogic) ShortUrlRedirect(req *types.ShortUrlRedirectReq) (resp *types.ShortUrlRedirectResp, err error) {
	// todo: add your logic here and delete this line

	return
}
