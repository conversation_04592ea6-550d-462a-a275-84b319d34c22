package box

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type RedeemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRedeemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RedeemLogic {
	return &RedeemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RedeemLogic) Redeem(req *types.BoxRedeemReq) (resp *types.BoxRedeemResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	// 1. 解密RSA获取卡号
	decryptService := service.NewDecryptService(l.svcCtx.Config)
	cardNo, err := decryptService.DecryptRSA(req.EncryptedCardNo)
	if err != nil {
		l.Logger.Errorf("DecryptRSA error: %v", err)
		return nil, xerr.NewErrMsg("解密失败")
	}

	// 2. 查询礼品卡信息
	card, err := l.svcCtx.BoxCardsModel.FindOne(l.ctx, cardNo)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("礼品卡不存在")
		}
		l.Logger.Errorf("BoxCardsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 3. 验证礼品卡状态
	if card.Status != 1 {
		return nil, xerr.NewErrMsg("礼品卡已使用或已作废")
	}

	// 4. 验证密码（salt）
	if card.Salt != req.Password {
		return nil, xerr.NewErrMsg("密码错误")
	}

	// 5. 查询盲盒信息
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, card.BoxId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("盲盒不存在")
		}
		l.Logger.Errorf("BoxModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 6. 验证盲盒状态
	if box.DeleteTime > 0 {
		return nil, xerr.NewErrMsg("盲盒已删除")
	}
	if box.OnsaleStatus != 2 {
		return nil, xerr.NewErrMsg("盲盒未在售")
	}

	// 7. 查询盲盒详情
	boxItems, err := l.svcCtx.BoxItemsModel.FindByBoxId(l.ctx, int64(card.BoxId))
	if err != nil {
		l.Logger.Errorf("BoxItemsModel.FindByBoxId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if len(boxItems) == 0 {
		return nil, xerr.NewErrMsg("盲盒商品信息为空")
	}

	// 8. 生成主订单号
	mainOrderNo := l.generateMainOrderNo()

	// 9. 开始事务处理
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		return l.processRedeemTransaction(ctx, tx, uid, mainOrderNo, card, box, boxItems)
	})

	if err != nil {
		l.Logger.Errorf("Transaction error: %v", err)
		return nil, xerr.NewErrMsg("兑换失败")
	}

	return &types.BoxRedeemResp{
		MainOrderNo: mainOrderNo,
	}, nil
}

// generateMainOrderNo 生成主订单号，确保唯一性
func (l *RedeemLogic) generateMainOrderNo() string {
	// 最多尝试10次生成唯一订单号
	for i := 0; i < 10; i++ {
		// 使用通用函数生成订单号
		mainOrderNo := function.GenerateMainOrderNo()

		// 检查订单号是否已存在
		_, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, mainOrderNo)
		if err != nil && err == model.ErrNotFound {
			// 订单号不存在，可以使用
			return mainOrderNo
		}
		// 如果存在或查询出错，继续下一次循环
		time.Sleep(time.Millisecond) // 短暂等待确保时间戳不同
	}

	// 如果10次都失败，使用时间戳确保唯一性
	now := time.Now()
	timestamp := now.UnixNano() / 1000000 // 毫秒时间戳
	return fmt.Sprintf("MLDM%s%d", now.Format("060102"), timestamp%1000000)
}

// processRedeemTransaction 处理兑换事务
func (l *RedeemLogic) processRedeemTransaction(ctx context.Context, tx *sql.Tx, uid int64, mainOrderNo string, card *model.VhBoxCards, box *model.VhBox, boxItems []*model.VhBoxItems) error {
	now := time.Now()
	nowTimestamp := now.Unix()

	// 获取用户信息
	user, err := l.svcCtx.UserModel.FindOne(ctx, uid)
	if err != nil {
		l.Logger.Errorf("UserModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 获取购买人角色（盲盒兑换没有分享人）
	buyerLevel := user.Type
	sharerLevel := int64(0) // 盲盒兑换没有分享人

	// 1. 创建盲盒快照
	snapshot, err := l.createBoxSnapshot(box, boxItems)
	if err != nil {
		return err
	}

	// 2. 创建主订单（使用事务，添加 buyer_level 和 sharer_level 字段）
	mainOrderQuery := `INSERT INTO vh_order_main (uid, main_order_no, main_order_status, payment_amount, payment_time, payment_method, deductible_amount, cash_amount, snapshot, share_id, buyer_level, sharer_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	_, err = tx.ExecContext(ctx, mainOrderQuery,
		uid, mainOrderNo, 1, box.Price, nowTimestamp, 0, 0.00, 0.00, snapshot, 0, buyerLevel, sharerLevel)
	if err != nil {
		l.Logger.Errorf("Insert main order error: %v", err)
		return err
	}

	// 3. 生成盲盒订单详情
	orderMainBoxes, err := l.generateOrderMainBoxes(uid, mainOrderNo, box, boxItems, now)
	if err != nil {
		return err
	}

	// 4. 批量插入盲盒订单详情
	// 直接使用SQL执行批量插入
	err = l.batchInsertOrderMainBoxes(ctx, tx, orderMainBoxes)
	if err != nil {
		l.Logger.Errorf("OrderMainBoxModel.BatchInsert error: %v", err)
		return err
	}

	// 5. 更新礼品卡状态（使用事务）
	updateCardQuery := `UPDATE vh_box_cards SET status = ?, use_time = ?, main_order_no = ? WHERE card_no = ?`
	_, err = tx.ExecContext(ctx, updateCardQuery, 2, now, mainOrderNo, card.CardNo)
	if err != nil {
		l.Logger.Errorf("Update box card error: %v", err)
		return err
	}

	// 6. 更新盲盒激活数量（使用事务）
	updateBoxQuery := `UPDATE vh_box SET active_num = active_num + 1 WHERE id = ?`
	_, err = tx.ExecContext(ctx, updateBoxQuery, box.Id)
	if err != nil {
		l.Logger.Errorf("Update box active_num error: %v", err)
		return err
	}

	// 7. 检查并升级用户类型（使用事务）
	err = l.upgradeUserTypeIfNeeded(ctx, tx, uid)
	if err != nil {
		l.Logger.Errorf("Upgrade user type error: %v", err)
		return err
	}

	return nil
}

// createBoxSnapshot 创建盲盒快照
func (l *RedeemLogic) createBoxSnapshot(box *model.VhBox, boxItems []*model.VhBoxItems) (string, error) {
	type BoxItemSnapshot struct {
		Id        uint64 `json:"id"`
		Type      int64  `json:"type"`
		ItemsInfo string `json:"items_info"`
	}

	type BoxSnapshot struct {
		BoxId         uint64            `json:"box_id"`
		Title         string            `json:"title"`
		AvatarImage   string            `json:"avatar_image"`
		ValidTimeUnit string            `json:"valid_time_unit"`
		ValidTimeNum  uint64            `json:"valid_time_num"`
		Price         float64           `json:"price"`
		Items         []BoxItemSnapshot `json:"items"`
		SnapshotTime  string            `json:"snapshot_time"`
	}

	var items []BoxItemSnapshot
	for _, item := range boxItems {
		items = append(items, BoxItemSnapshot{
			Id:        item.Id,
			Type:      item.Type,
			ItemsInfo: item.ItemsInfo,
		})
	}

	snapshot := BoxSnapshot{
		BoxId:         box.Id,
		Title:         box.Title,
		AvatarImage:   function.NormalizeImagePath(box.AvatarImage), // 统一存储半路径
		ValidTimeUnit: box.ValidTimeUnit,
		ValidTimeNum:  box.ValidTimeNum,
		Price:         box.Price,
		Items:         items,
		SnapshotTime:  time.Now().Format("2006-01-02 15:04:05"),
	}

	snapshotBytes, err := json.Marshal(snapshot)
	if err != nil {
		l.Logger.Errorf("json.Marshal snapshot error: %v", err)
		return "", err
	}

	return string(snapshotBytes), nil
}

// generateOrderMainBoxes 生成盲盒订单详情
func (l *RedeemLogic) generateOrderMainBoxes(uid int64, mainOrderNo string, box *model.VhBox, boxItems []*model.VhBoxItems, activateTime time.Time) ([]*model.VhOrderMainBox, error) {
	var orderMainBoxes []*model.VhOrderMainBox
	var randomItems []string // 存储所有随机类型的商品信息

	// 收集所有随机类型的商品信息
	for _, item := range boxItems {
		if item.Type == 1 { // 随机类型
			randomItems = append(randomItems, item.ItemsInfo)
		}
	}

	// 打乱随机商品信息
	if len(randomItems) > 0 {
		rand.Seed(time.Now().UnixNano())
		rand.Shuffle(len(randomItems), func(i, j int) {
			randomItems[i], randomItems[j] = randomItems[j], randomItems[i]
		})
	}

	randomIndex := 0
	for i, item := range boxItems {
		var itemsInfo string
		if item.Type == 2 { // 固定类型
			itemsInfo = item.ItemsInfo
		} else { // 随机类型
			if randomIndex < len(randomItems) {
				itemsInfo = randomItems[randomIndex]
				randomIndex++
			} else {
				itemsInfo = item.ItemsInfo // 如果随机商品不够，使用原始信息
			}
		}

		// 计算领取时间
		beginTime, endTime := l.calculateReceiveTime(activateTime, box.ValidTimeUnit, box.ValidTimeNum, i+1)

		orderMainBox := &model.VhOrderMainBox{
			Uid:         uid,
			MainOrderNo: mainOrderNo,
			ItemsInfo:   itemsInfo,
			BoxId:       int64(box.Id),
			BoxItemId:   int64(item.Id),
			BoxType:     sql.NullInt64{Int64: 1, Valid: true}, // 待领取
			BeginTime:   sql.NullTime{Time: beginTime, Valid: true},
			EndTime:     sql.NullTime{Time: endTime, Valid: true},
		}

		orderMainBoxes = append(orderMainBoxes, orderMainBox)
	}

	return orderMainBoxes, nil
}

// calculateReceiveTime 计算领取时间
func (l *RedeemLogic) calculateReceiveTime(activateTime time.Time, unit string, num uint64, period int) (time.Time, time.Time) {
	var duration time.Duration

	// 根据 valid_time_unit 设置固定的间隔时间，不使用 valid_time_num 的数量
	switch unit {
	case "hour":
		duration = 1 * time.Hour
	case "day":
		duration = 1 * 24 * time.Hour
	case "week":
		duration = 1 * 7 * 24 * time.Hour
	case "month":
		duration = 1 * 30 * 24 * time.Hour
	case "year":
		duration = 1 * 365 * 24 * time.Hour
	default:
		duration = 1 * 24 * time.Hour // 默认按天计算
	}

	// 第period期的开始时间 = 激活时间 + (period-1) * 间隔时间
	beginTime := activateTime.Add(time.Duration(period-1) * duration)
	// 第period期的结束时间 = 激活时间 + period * 间隔时间
	endTime := activateTime.Add(time.Duration(period) * duration)

	return beginTime, endTime
}

// batchInsertOrderMainBoxes 批量插入盲盒订单详情
func (l *RedeemLogic) batchInsertOrderMainBoxes(ctx context.Context, tx *sql.Tx, boxes []*model.VhOrderMainBox) error {
	if len(boxes) == 0 {
		return nil
	}

	// 构建批量插入SQL
	query := "insert into vh_order_main_box (uid, main_order_no, items_info, box_id, box_item_id, box_type, order_no, begin_time, end_time, get_time) values "

	var values []interface{}
	for i, box := range boxes {
		if i > 0 {
			query += ", "
		}
		query += "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
		values = append(values, box.Uid, box.MainOrderNo, box.ItemsInfo, box.BoxId, box.BoxItemId,
			box.BoxType, box.OrderNo, box.BeginTime, box.EndTime, box.GetTime)
	}

	_, err := tx.ExecContext(ctx, query, values...)
	return err
}

// upgradeUserTypeIfNeeded 检查并升级用户类型
func (l *RedeemLogic) upgradeUserTypeIfNeeded(ctx context.Context, tx *sql.Tx, uid int64) error {
	// 查询用户当前类型
	var userType int64
	getUserTypeQuery := `SELECT type FROM vh_user WHERE id = ?`
	err := tx.QueryRowContext(ctx, getUserTypeQuery, uid).Scan(&userType)
	if err != nil {
		l.Logger.Errorf("Query user type error: %v", err)
		return err
	}

	// 如果用户类型是1（普通用户），升级为2（会员）
	if userType == 1 {
		updateUserTypeQuery := `UPDATE vh_user SET type = 2 WHERE id = ? AND type = 1`
		result, err := tx.ExecContext(ctx, updateUserTypeQuery, uid)
		if err != nil {
			l.Logger.Errorf("Update user type error: %v", err)
			return err
		}

		affected, _ := result.RowsAffected()
		if affected > 0 {
			l.Logger.Infof("User type upgraded from 1 to 2 for uid: %d", uid)

			// 清除用户缓存
			user, err := l.svcCtx.UserModel.FindOne(ctx, uid)
			if err == nil {
				clearErr := l.svcCtx.UserModel.ClearCache(ctx, user)
				if clearErr != nil {
					l.Logger.Errorf("Clear user cache error: %v", clearErr)
				}
			}
		}
	}

	return nil
}
