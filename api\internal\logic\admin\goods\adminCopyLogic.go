package goods

import (
	"context"
	"database/sql"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCopyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCopyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCopyLogic {
	return &AdminCopyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCopyLogic) AdminCopy(req *types.GoodsCopyReq) error {
	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 查找原商品
	originalGoods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminCopy GoodsModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if originalGoods.DeleteTime > 0 {
		return xerr.NewErrMsg("商品已被删除")
	}

	// 创建新商品（复制原商品信息）
	newGoods := &model.VhGoods{
		Title:            fmt.Sprintf("%s-复制", originalGoods.Title),
		Brief:            originalGoods.Brief, // 复制副标题
		Type:             originalGoods.Type,
		ItemsInfo:        originalGoods.ItemsInfo,
		Price:            originalGoods.Price,
		Inventory:        originalGoods.Inventory,
		ErpAmount:        originalGoods.ErpAmount,
		CashbackAmount:   originalGoods.CashbackAmount,
		DeductibleAmount: originalGoods.DeductibleAmount,
		ProductImg:       originalGoods.ProductImg,
		AvatarImage:      originalGoods.AvatarImage,
		Detail:           originalGoods.Detail,
		OnsaleStatus:     3, // 默认下架状态
		WarehouseCode:    originalGoods.WarehouseCode,
		OnsaleTime:       0,
		SoldOutTime:      0,
		Purchased:        0,
		SalesUserNum:     0,
		Pv:               0,
		DeleteTime:       0,
		Sort:             originalGoods.Sort,
		VhUid:            adminUid,
		VhVosName:        sql.NullString{String: adminVosName, Valid: adminVosName != ""},
		// CreatedTime 和 UpdateTime 由数据库自动维护，不需要手动设置
	}

	result, err := l.svcCtx.GoodsModel.Insert(l.ctx, newGoods)
	if err != nil {
		l.Logger.Error("AdminCopy GoodsModel.Insert error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 获取新商品ID
	newGoodsId, _ := result.LastInsertId()

	// 复制商品标签
	originalLabels, err := l.svcCtx.GoodsLabelModel.FindByPeriodId(l.ctx, req.Id)
	if err == nil && len(originalLabels) > 0 {
		for _, label := range originalLabels {
			newLabel := &model.VhGoodsLabel{
				PeriodId: sql.NullInt64{Int64: newGoodsId, Valid: true},
				LabelId:  label.LabelId,
				Name:     label.Name,
			}
			_, err = l.svcCtx.GoodsLabelModel.Insert(l.ctx, newLabel)
			if err != nil {
				l.Logger.Error("AdminCopy GoodsLabelModel.Insert error: %v", err)
				// 继续处理其他标签，不中断
			}
		}
	}

	return nil
}
