package box

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type CardExportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCardExportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CardExportLogic {
	return &CardExportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CardExportLogic) CardExport(req *types.BoxCardExportReq, w http.ResponseWriter) error {
	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 构建查询条件
	builder := squirrel.Select("id, card_no, salt, qr_code, print_count").
		From("vh_box_cards").
		Where(squirrel.NotEq{"qr_code": ""}).
		Where(squirrel.NotEq{"qr_code": nil})

	// 添加筛选条件
	if req.BoxId > 0 {
		builder = builder.Where(squirrel.Eq{"box_id": req.BoxId})
	}

	if req.Status > 0 {
		builder = builder.Where(squirrel.Eq{"status": req.Status})
	}

	if req.PrintCount >= 0 {
		builder = builder.Where(squirrel.Eq{"print_count": req.PrintCount})
	}

	// 查询数据
	var cards []struct {
		Id         int64  `db:"id"`
		CardNo     string `db:"card_no"`
		Salt       string `db:"salt"`
		QrCode     string `db:"qr_code"`
		PrintCount int64  `db:"print_count"`
	}

	err := l.svcCtx.BoxCardsModel.FindRows(l.ctx, builder, &cards)
	if err != nil {
		l.Logger.Error("CardExport FindRows error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if len(cards) == 0 {
		return xerr.NewErrMsg("没有符合条件的数据")
	}

	// 记录导出日志
	l.Logger.Infof("管理员导出礼品卡Excel - UID: %d, 姓名: %s, 导出数量: %d",
		adminUid, adminVosName, len(cards))

	// 记录导出的数据ID
	var exportIds []int64
	for _, card := range cards {
		exportIds = append(exportIds, card.Id)
	}

	// 准备Excel导出数据
	var excelData []service.ExcelDataItem
	for _, card := range cards {
		excelData = append(excelData, service.ExcelDataItem{
			QRCode:     card.QrCode,
			CardNumber: card.Salt,
		})
	}

	// 调用Excel导出服务
	qrcodeService := service.NewQRCodeService(&l.svcCtx.Config)
	excelBytes, err := qrcodeService.ExportExcel(excelData)
	if err != nil {
		l.Logger.Errorf("CardExport ExportExcel error: %v", err)
		return xerr.NewErrMsg("Excel导出失败")
	}

	// 更新打印次数
	err = l.updatePrintCount(exportIds)
	if err != nil {
		l.Logger.Errorf("CardExport updatePrintCount error: %v", err)
		// 不返回错误，因为Excel已经生成成功
	}

	// 设置响应头
	filename := fmt.Sprintf("qrcodes_and_cards_%s.xlsx", time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(excelBytes)))

	// 写入Excel数据
	_, err = w.Write(excelBytes)
	if err != nil {
		l.Logger.Errorf("CardExport write response error: %v", err)
		return xerr.NewErrMsg("写入响应失败")
	}

	return nil
}

// updatePrintCount 更新打印次数
func (l *CardExportLogic) updatePrintCount(ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 批量更新打印次数
	updateBuilder := squirrel.Update("vh_box_cards").
		Set("print_count", squirrel.Expr("print_count + 1")).
		Where(squirrel.Eq{"id": ids})

	_, err := l.svcCtx.BoxCardsModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		return err
	}

	return nil
}
