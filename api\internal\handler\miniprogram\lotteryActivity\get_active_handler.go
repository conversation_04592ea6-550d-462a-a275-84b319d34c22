package lotteryActivity

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/lotteryActivity"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetActiveHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := lotteryActivity.NewGetActiveLogic(r.Context(), svcCtx)
		resp, err := l.GetActive()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
