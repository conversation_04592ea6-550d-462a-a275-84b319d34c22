package goods

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"strings"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type MiniDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniDetailLogic {
	return &MiniDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniDetailLogic) MiniDetail(req *types.MiniGoodsDetailReq) (resp *types.MiniGoodsDetailResp, err error) {
	// 查询商品详情，只返回未删除的数据
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("MiniDetail GoodsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if goods.DeleteTime > 0 {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 增加浏览量 PV
	updateBuilder := squirrel.Update(l.svcCtx.GoodsModel.TableName()).
		Set("pv", goods.Pv+1).
		Where(squirrel.Eq{"id": req.Id})

	_, err = l.svcCtx.GoodsModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Error("MiniDetail UpdateCustom pv error: %v", err)
		// PV更新失败不影响主流程，继续执行
	}

	// 构建响应
	resp = &types.MiniGoodsDetailResp{
		Id:               int64(goods.Id),
		Title:            goods.Title,
		Brief:            goods.Brief,
		Type:             goods.Type,
		ItemsInfo:        goods.ItemsInfo.String,
		Price:            goods.Price,
		Inventory:        int64(goods.Inventory),
		ErpAmount:        goods.ErpAmount,
		CashbackAmount:   goods.CashbackAmount,
		DeductibleAmount: goods.DeductibleAmount,
		ProductImg:       l.processImages(goods.ProductImg),
		AvatarImage:      l.processImage(goods.AvatarImage),
		Detail:           goods.Detail.String,
		OnsaleStatus:     goods.OnsaleStatus,
		Purchased:        int64(goods.Purchased),
		SalesUserNum:     int64(goods.SalesUserNum),
		Pv:               int64(goods.Pv + 1), // 返回更新后的PV值
	}

	return
}

// processImage 处理单个图片
func (l *MiniDetailLogic) processImage(image string) string {
	if image == "" {
		return ""
	}
	return l.svcCtx.Config.ITEM.ALIURL + image
}

// processImages 处理多个图片
func (l *MiniDetailLogic) processImages(images string) string {
	if images == "" {
		return ""
	}

	imageList := strings.Split(images, ",")
	var processedImages []string

	for _, img := range imageList {
		if strings.TrimSpace(img) != "" {
			processedImages = append(processedImages, l.svcCtx.Config.ITEM.ALIURL+strings.TrimSpace(img))
		}
	}

	return strings.Join(processedImages, ",")
}
