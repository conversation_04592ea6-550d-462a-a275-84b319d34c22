package label

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDetailLogic {
	return &AdminDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDetailLogic) AdminDetail(req *types.LabelDetailReq) (resp *types.LabelInfo, err error) {
	label, err := l.svcCtx.LabelModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDetailLogic LabelModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = &types.LabelInfo{
		Id:          label.Id,
		Name:        label.Name,
		Sort:        label.Sort,
		Status:      label.Status,
		CreatedTime: function.FormatTime(label.CreatedTime),
		UpdateTime:  function.FormatTime(label.UpdateTime),
	}

	return
}
