syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    //创建分享
    ShareCreateReq {
        GoodsId uint64 `json:"goods_id" validate:"required" v:"商品id"`
        Channel uint64 `json:"channel" validate:"min=1,max=4" v:"分享渠道"`
        Title string `json:"title,optional" validate:"max=800" v:"推荐词"`
        Notes string `json:"notes,optional" validate:"max=50" v:"分享备注"`
    }
    ShareCreateResp {
        ShareId int64 `json:"share_id"`
    }

    MyShareResp {
        ShareCt int64 `json:"share_ct"`
        BuyCt int64 `json:"buy_ct"`
        CashbackAmount float64 `json:"cashback_amount"`
    }

    MyShareListReq {
        Paging
        IsBuy bool `form:"is_buy,optional"`
    }
    MyShareListResp {
        List []MyShareInfo `json:"list"`
        Total int64 `json:"total"`
    }
    MyShareInfo {
        Id uint64 `json:"id"`
        GoodsId uint64 `json:"goods_id"`
        GoodsTitle string `json:"goods_title"`
        GoodsImg string `json:"goods_img"`
        Channel uint64 `json:"channel"`
        Title string `json:"title"`
        Notes string `json:"notes"`
        CreateTime string `json:"create_time"`
        BuyCount uint64 `json:"buy_count"`
        BuyMoney float64 `json:"buy_money"`
    }
    ShareBuyListReq {
        Paging
        IdFU
    }
    ShareBuyListResp {
        List []ShareBuyInfo `json:"list"`
        Total int64 `json:"total"`
    }
    ShareBuyInfo {
        Nickname string `json:"nickname"`
        AvatarImage string `json:"avatar_image"`
        Money float64 `json:"money"`
        CreateTime string `json:"create_time"`
    }

    DetailReq {
        IdFU
    }
    DetailResp {
        Title string `json:"title"`
        Notes string `json:"notes"`
        Nickname string `json:"nickname"`
        AvatarImage string `json:"avatar_image"`
    }

    GenerateTitleReq {
        GoodsId uint64 `json:"goods_id" validate:"required" v:"商品id"`
        Object string `json:"object,optional" validate:"max=10" v:"对象"`
        Scene string `json:"scene,optional" validate:"max=10" v:"场景"`
        Vacation string `json:"vacation,optional" validate:"max=10" v:"节假日"`
        UseGoodsDetail bool `json:"use_goods_detail,optional" v:"是否使用商品详情"`
    }
    GenerateTitleResp {
        Title string `json:"title"`
    }

    RefineTitleReq {
        Title string `json:"title" validate:"required,max=800" v:"推荐语"`
    }
    RefineTitleResp {
        Title string `json:"title"`
    }
)

@server(
    middleware: Global,ExistAuth
    group: miniprogram/share
    prefix: /mulandoGreateDestiny/v1/miniprogram/share
    timeout: 3s
)

service mulandoGreateDestiny {
    //分享详情
    @handler Detail
    get /detail (DetailReq) returns (DetailResp)
}

// 小程序抽奖活动接口,必须登录
@server(
    middleware: Global,Auth
    group: miniprogram/share
    prefix: /mulandoGreateDestiny/v1/miniprogram/share
    timeout: 3s
)

service mulandoGreateDestiny {
    //创建分享
    @handler Create
    post /create (ShareCreateReq) returns (ShareCreateResp)

    //我的分享
    @handler MyShare
    get /myShare returns (MyShareResp)

    //我的分享列表
    @handler MyShareList
    get /myShareList (MyShareListReq) returns (MyShareListResp)

    //分享购买列表
    @handler ShareBuyList
    get /shareBuyList (ShareBuyListReq) returns (ShareBuyListResp)
}

@server(
    middleware: Global,Auth
    group: miniprogram/share
    prefix: /mulandoGreateDestiny/v1/miniprogram/share
    timeout: 30s
)

service mulandoGreateDestiny {
    //生成推荐语
    @handler GenerateTitle
    post /generateTitle (GenerateTitleReq) returns (GenerateTitleResp)

    //润色推荐语
    @handler RefineTitle
    post /refineTitle (RefineTitleReq) returns (RefineTitleResp)
}
