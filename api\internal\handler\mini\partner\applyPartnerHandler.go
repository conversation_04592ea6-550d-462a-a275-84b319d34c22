package partner

import (
	"net/http"

	"engine/api/internal/logic/mini/partner"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ApplyPartnerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ApplyPartnerReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := partner.NewApplyPartnerLogic(r.Context(), svcCtx)
		resp, err := l.ApplyPartner(&req)
		result.HttpResult(r, w, resp, err)
	}
}
