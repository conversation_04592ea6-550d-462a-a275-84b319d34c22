package winenotes

import (
	"net/http"

	"engine/api/internal/logic/winenotes"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 后台通过UUID转换WineNotes为HTML
func AdminConvertWineNotesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WineNotesConvertReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := winenotes.NewAdminConvertWineNotesLogic(r.Context(), svcCtx)
		resp, err := l.AdminConvertWineNotes(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
