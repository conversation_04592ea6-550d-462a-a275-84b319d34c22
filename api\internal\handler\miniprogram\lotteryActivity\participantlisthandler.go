package lotteryActivity

import (
	"engine/api/internal/logic/miniprogram/lotteryActivity"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func ParticipantListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ActiveLotteryActivityParticipantListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := lotteryActivity.NewParticipantListLogic(r.Context(), svcCtx)
		resp, err := l.ParticipantList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
