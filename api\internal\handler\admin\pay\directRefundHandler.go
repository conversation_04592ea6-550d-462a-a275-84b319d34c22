package pay

import (
	"net/http"

	"engine/api/internal/logic/admin/pay"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 直接退款,处理测试环境中未退款单已清理数据的订单
func DirectRefundHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DirectRefundReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := pay.NewDirectRefundLogic(r.Context(), svcCtx)
		resp, err := l.DirectRefund(&req)
		result.HttpResult(r, w, resp, err)
	}
}
