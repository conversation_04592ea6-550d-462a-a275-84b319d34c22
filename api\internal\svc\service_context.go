package svc

import (
	"engine/api/internal/config"
	"engine/api/internal/middleware"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config    config.Config
	Global    rest.Middleware
	Admin     rest.Middleware
	Auth      rest.Middleware
	ExistAuth rest.Middleware
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:    c,
		Global:    middleware.NewGlobalMiddleware().Handle,
		Admin:     middleware.NewAdminMiddleware().Handle,
		Auth:      middleware.NewAuthMiddleware().<PERSON><PERSON>,
		ExistAuth: middleware.NewExistAuthMiddleware().<PERSON><PERSON>,
	}
}
