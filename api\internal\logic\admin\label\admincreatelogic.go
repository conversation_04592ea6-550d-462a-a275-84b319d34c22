package label

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCreateLogic {
	return &AdminCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCreateLogic) AdminCreate(req *types.LabelCreateReq) (*types.LabelCreateResp, error) {
	// 检查名称是否已存在
	existLabel, err := l.svcCtx.LabelModel.FindOneByName(l.ctx, req.Name)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("AdminCreateLogic LabelModel.FindOneByName error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if existLabel != nil {
		// 返回指定字段
		return &types.LabelCreateResp{
			Id:     existLabel.Id,
			Name:   existLabel.Name,
			Status: existLabel.Status,
		}, nil
	}

	// 创建新标签，使用默认值
	label := &model.Label{
		Name:   req.Name,
		Sort:   0, // 默认排序为0
		Status: 1, // 默认状态为启用
	}

	result, err := l.svcCtx.LabelModel.Insert(l.ctx, label)
	if err != nil {
		l.Logger.Error("AdminCreateLogic LabelModel.Insert error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取插入的ID
	labelId, _ := result.LastInsertId()

	// 返回指定字段
	resp := &types.LabelCreateResp{
		Id:     labelId,
		Name:   label.Name,
		Status: label.Status,
	}

	return resp, nil
}
