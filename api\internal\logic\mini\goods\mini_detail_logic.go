package goods

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniDetailLogic {
	return &MiniDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniDetailLogic) MiniDetail(req *types.MiniGoodsDetailReq) (resp *types.MiniGoodsDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
