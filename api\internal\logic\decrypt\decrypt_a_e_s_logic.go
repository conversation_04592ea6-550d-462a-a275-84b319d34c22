package decrypt

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptAESLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptAESLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptAESLogic {
	return &DecryptAESLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptAESLogic) DecryptAES(req *types.AESDecryptReq) (resp *types.AESDecryptResp, err error) {
	// todo: add your logic here and delete this line

	return
}
