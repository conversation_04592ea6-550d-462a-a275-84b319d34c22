package banner

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniProgramListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniProgramListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniProgramListLogic {
	return &MiniProgramListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniProgramListLogic) MiniProgramList() (resp *types.MiniProgramBannerListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
