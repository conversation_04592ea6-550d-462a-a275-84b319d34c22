package order

import (
	"context"
	"database/sql"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/core/logx"
)

type DetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DetailLogic {
	return &DetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DetailLogic) Detail(req *types.OrderDetailReq) (resp *types.OrderDetailResp, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 确定查询的订单号
	var subOrderNo string
	var mainOrderNo string

	// 优先级：SubOrderNo > MainOrderNo > Id
	if req.SubOrderNo != "" {
		subOrderNo = req.SubOrderNo
	} else if req.MainOrderNo != "" {
		mainOrderNo = req.MainOrderNo
	} else if req.Id != "" {
		// 兼容微信跳转，Id可能是主订单号或子订单号
		// 先尝试作为主订单号查询
		mainOrderNo = req.Id
	} else {
		return nil, xerr.NewErrMsg("请提供订单号")
	}

	var subOrder *model.VhOrder

	// 如果有子订单号，直接查询子订单
	if subOrderNo != "" {
		subOrder, err = l.svcCtx.OrderModel.FindOneBySubOrderNo(l.ctx, subOrderNo)
		if err != nil {
			if err == model.ErrNotFound {
				return nil, xerr.NewErrMsg("订单不存在")
			}
			l.Logger.Error("Detail OrderModel.FindOneBySubOrderNo error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}
	} else if mainOrderNo != "" {
		// 通过主订单号查询子订单
		subOrders, err := l.svcCtx.OrderModel.FindByMainOrderNo(l.ctx, mainOrderNo)
		if err != nil || len(subOrders) == 0 {
			// 如果通过主订单号没找到，尝试作为子订单号查询（兼容性处理）
			subOrder, err = l.svcCtx.OrderModel.FindOneBySubOrderNo(l.ctx, mainOrderNo)
			if err != nil {
				if err == model.ErrNotFound {
					return nil, xerr.NewErrMsg("订单不存在")
				}
				l.Logger.Error("Detail OrderModel.FindOneBySubOrderNo error: %v", err)
				return nil, xerr.NewErrCode(xerr.DbError)
			}
		} else {
			// 取第一个子订单（通常一个主订单对应一个子订单）
			subOrder = subOrders[0]
		}
	}

	// 验证订单所有权
	if subOrder.Uid != uid {
		return nil, xerr.NewErrMsg("无权访问此订单")
	}

	// 查询主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOne(l.ctx, subOrder.MainOrderId)
	if err != nil {
		l.Logger.Error("Detail OrderMainModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询商品信息
	goodsInfo, err := l.getGoodsInfo(subOrder.GoodsId, subOrder.OrderQty, subOrder.Snapshot)
	if err != nil {
		l.Logger.Error("Detail getGoodsInfo error: %v", err)
		return nil, err
	}

	// 根据订单类型获取商品快照信息
	goodsTitle, goodsImage, goodsPrice := l.getGoodsSnapshotInfo(subOrder.Type, subOrder.Snapshot, mainOrder.Snapshot)

	// 构建响应
	resp = &types.OrderDetailResp{
		SubOrderNo:       subOrder.SubOrderNo,
		MainOrderNo:      mainOrder.MainOrderNo,
		OrderType:        subOrder.Type,
		OrderStatus:      subOrder.SubOrderStatus,
		OrderStatusText:  l.getOrderStatusText(subOrder.SubOrderStatus),
		OrderQty:         subOrder.OrderQty,
		GoodsTitle:       goodsTitle,
		GoodsImage:       goodsImage,
		GoodsPrice:       goodsPrice,
		GoodsInfo:        *goodsInfo,
		CreatedTime:      function.FormatTime(subOrder.CreatedTime),
		PaymentTime:      function.FormatTimestamp(subOrder.PaymentTime),
		DeliveryTime:     l.formatNullTimestamp(subOrder.DeliveryTime),
		CompletedTime:    function.FormatTimestamp(subOrder.GoodsReceiptTime),
		CancelTime:       l.formatNullTimestamp(subOrder.CancelTime),
		GoodsAmount:      goodsInfo.Price * float64(goodsInfo.Quantity),
		DeductibleAmount: subOrder.DeductibleAmount,
		PaymentAmount:    subOrder.PaymentAmount,
		CashAmount:       subOrder.CashAmount,
		ExpressType:      subOrder.ExpressType,
		ExpressNumber:    subOrder.ExpressNumber.String,
		ExpressName:      l.getExpressName(subOrder.ExpressType),
		ConsigneeName:    subOrder.Consignee.String,
		ConsigneePhone:   subOrder.ConsigneePhone.String,
		Province:         subOrder.Province.String,
		City:             subOrder.City.String,
		District:         subOrder.District.String,
		Address:          subOrder.Address.String,
		Remarks:          subOrder.Remarks.String,
		RefundStatus:     subOrder.RefundStatus.Int64,
		RefundAmount:     subOrder.RefundMoney,
	}

	// 设置操作按钮状态
	l.setActionButtons(resp, subOrder.SubOrderStatus)

	return resp, nil
}

// getGoodsInfo 获取商品信息
func (l *DetailLogic) getGoodsInfo(goodsId, quantity int64, snapshot sql.NullString) (*types.OrderGoodsInfo, error) {
	goodsInfo := &types.OrderGoodsInfo{
		GoodsId:  goodsId,
		Quantity: quantity,
	}

	// 优先从快照解析商品信息
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			if title, ok := snapshotData["title"].(string); ok {
				goodsInfo.Title = title
			}
			if avatarImage, ok := snapshotData["avatar_image"].(string); ok {
				goodsInfo.AvatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, avatarImage)
			}
			if price, ok := snapshotData["price"].(float64); ok {
				goodsInfo.Price = price
			}
			return goodsInfo, nil
		}
	}

	// 快照解析失败，从数据库查询
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(goodsId))
	if err != nil {
		if err == model.ErrNotFound {
			goodsInfo.Title = "商品已下架"
			goodsInfo.AvatarImage = ""
			goodsInfo.Price = 0
			return goodsInfo, nil
		}
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	goodsInfo.Title = goods.Title
	goodsInfo.AvatarImage = l.processImage(goods.AvatarImage)
	goodsInfo.Price = goods.Price

	return goodsInfo, nil
}

// processImage 处理图片URL
func (l *DetailLogic) processImage(image string) string {
	return function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, image)
}

// formatNullTimestamp 格式化可空的时间戳
func (l *DetailLogic) formatNullTimestamp(timestamp sql.NullInt64) string {
	if timestamp.Valid && timestamp.Int64 > 0 {
		return function.FormatTimestamp(timestamp.Int64)
	}
	return ""
}

// getOrderStatusText 获取订单状态文本
func (l *DetailLogic) getOrderStatusText(status int64) string {
	switch status {
	case 0:
		return "等待支付"
	case 1:
		return "支付成功"
	case 2:
		return "问题反馈"
	case 3:
		return "再次购买"
	case 4:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getExpressName 获取快递公司名称
func (l *DetailLogic) getExpressName(expressType int64) string {
	switch expressType {
	case 1:
		return "中通快递"
	case 2:
		return "顺丰快递"
	case 3:
		return "顺丰冷链"
	case 5:
		return "京东快递"
	case 6:
		return "客户仓库自提"
	case 10:
		return "京东快运"
	case 21:
		return "欣运物流自提"
	case 22:
		return "欣运物流上门"
	case 23:
		return "京东TC"
	default:
		return ""
	}
}

// setActionButtons 设置操作按钮状态
func (l *DetailLogic) setActionButtons(resp *types.OrderDetailResp, status int64) {
	switch status {
	case 0: // 待支付
		resp.CanPay = true
		resp.CanCancel = true
	case 1: // 已支付
		resp.CanConfirm = false
	case 2: // 已发货
		resp.CanConfirm = true
	case 3: // 已完成
		resp.CanRepurchase = true
	case 4: // 已取消
		resp.CanRepurchase = true
	}
}

// getGoodsSnapshotInfo 根据订单类型从不同快照获取商品信息
func (l *DetailLogic) getGoodsSnapshotInfo(orderType int64, subOrderSnapshot, mainOrderSnapshot sql.NullString) (title, image string, price float64) {
	var snapshotData map[string]interface{}
	var err error

	// 根据订单类型选择快照来源
	if orderType == 3 { // 盲盒订单从主订单快照获取
		if mainOrderSnapshot.Valid && mainOrderSnapshot.String != "" {
			err = json.Unmarshal([]byte(mainOrderSnapshot.String), &snapshotData)
		}
	} else { // 普通商品和抽奖商品从子订单快照获取
		if subOrderSnapshot.Valid && subOrderSnapshot.String != "" {
			err = json.Unmarshal([]byte(subOrderSnapshot.String), &snapshotData)
		}
	}

	// 解析快照失败时返回默认值
	if err != nil {
		return "", "", 0.0
	}

	// 提取商品信息
	if titleVal, ok := snapshotData["title"].(string); ok {
		title = titleVal
	}

	if imageVal, ok := snapshotData["avatar_image"].(string); ok {
		image = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, imageVal)
	}

	if priceVal, ok := snapshotData["price"].(float64); ok {
		price = priceVal
	}

	return title, image, price
}
