package pay

import (
	"net/http"

	"engine/api/internal/logic/pay"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func WechatCallbackHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := pay.NewWechatCallbackLogic(r.Context(), svcCtx)
		err := l.WechatCallback()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.Ok(w)
		}
	}
}
