package balance

import (
	"context"
	"database/sql"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawalCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWithdrawalCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawalCreateLogic {
	return &WithdrawalCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawalCreateLogic) WithdrawalCreate(req *types.WithdrawalCreateReq) error {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	info, er := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if er != nil && !errors.Is(er, model.ErrNotFound) {
		l.<PERSON>("WithdrawalCreateLogic UserModel.FindOne error: %v", er)
		return xerr.NewErrCode(xerr.DbError)
	}
	if errors.Is(er, model.ErrNotFound) {
		return xerr.NewErrCode(xerr.UserNotExist)
	}

	if info.AliName == "" || info.AliAccount == "" {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "请先完善提现账号和姓名")
	}

	if info.Balance < req.Amount {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "余额不足")
	}

	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 创建提现记录
		status := uint64(2) //提现中，定时任务去自动提现
		if req.Amount >= 500 {
			//大于500需要审核
			status = 1
		}
		withdrawal := &model.VhWithdrawal{
			Uid:    cast.ToUint64(uid),
			Amount: req.Amount,
			Status: status,
		}
		result, er := l.svcCtx.WithdrawalModel.InsertTx(ctx, tx, withdrawal)
		if er != nil {
			l.Errorf("WithdrawalCreateLogic Trans.WithdrawalModel.InsertTx error: %v", er)
			return er
		}
		lastId, er := result.LastInsertId()
		if er != nil {
			l.Errorf("WithdrawalCreateLogic Trans.WithdrawalModel.InsertTx.LastInsertId error: %v", er)
			return er
		}

		//插入余额历史记录
		_, er = l.svcCtx.BalanceHistory.InsertTx(ctx, tx, &model.VhBalanceHistory{
			UniqueCode:    logic.GenerateWithdrawalNo(uid, lastId),
			Uid:           cast.ToUint64(uid),
			Type:          2,
			Amount:        req.Amount,
			AfterAmount:   info.Balance - req.Amount,
			OperationType: 5,
			OperationName: "系统",
		})
		if er != nil {
			return er
		}

		//更新用户余额
		result, err := l.svcCtx.UserModel.UpdateCustomTx(ctx, tx, squirrel.Update(l.svcCtx.UserModel.TableName()).
			Where(squirrel.Eq{"id": uid, "balance": info.Balance}).
			Set("balance", info.Balance-req.Amount))
		if err != nil {
			logx.Errorf("WithdrawalCreateLogic Trans.UserModel.UpdateCustomTx err: %v", err)
			return err
		}

		affected, err := result.RowsAffected()
		if err != nil {
			logx.Errorf("WithdrawalCreateLogic Trans.UserModel.RowsAffected err: %v", err)
			return err
		}
		if affected == 0 {
			return errors.New("用户余额发送变动 请重试")
		}

		_ = l.svcCtx.UserModel.ClearCache(l.ctx, info)
		return nil
	})

	if err != nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "服务器繁忙，请重试!")
	}

	return nil
}
