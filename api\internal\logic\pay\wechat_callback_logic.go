package pay

import (
	"context"

	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/core/logx"
)

type WechatCallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWechatCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WechatCallbackLogic {
	return &WechatCallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WechatCallbackLogic) WechatCallback() error {
	// todo: add your logic here and delete this line

	return nil
}
