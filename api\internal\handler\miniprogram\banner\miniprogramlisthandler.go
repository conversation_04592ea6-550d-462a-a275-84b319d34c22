package banner

import (
	"engine/api/internal/logic/miniprogram/banner"
	"engine/api/internal/svc"
	"engine/common/result"
	"net/http"
)

func MiniProgramListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := banner.NewMiniProgramListLogic(r.Context(), svcCtx)
		resp, err := l.MiniProgramList()
		result.HttpResult(r, w, resp, err)
	}
}
