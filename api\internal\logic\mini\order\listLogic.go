package order

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/golang-jwt/jwt/v4"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// mapApiStatusToDbStatus 将接口状态映射为数据库状态
// 接口状态: 0=全部, 1=待支付, 2=待发货, 3=已发货, 4=已完成
// 数据库状态: 0=待支付, 1=已支付, 2=已发货, 3=已完成, 4=已取消
func (l *ListLogic) mapApiStatusToDbStatus(apiStatus int64) int64 {
	switch apiStatus {
	case 0:
		return 0 // 全部订单，返回0表示不筛选
	case 1:
		return 0 // 待支付 -> 数据库的0
	case 2:
		return 1 // 待发货 -> 数据库的1(已支付)
	case 3:
		return 2 // 已发货 -> 数据库的2
	case 4:
		return 3 // 已完成 -> 数据库的3
	default:
		return 0 // 默认不筛选
	}
}

func (l *ListLogic) List(req *types.OrderListReq) (resp *types.OrderListResp, err error) {
	// 获取用户信息
	jwtUserValue := l.ctx.Value("jwt_user")
	if jwtUserValue == nil {
		l.Logger.Error("jwt_user not found in context")
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	jwtUser, ok := jwtUserValue.(jwt.MapClaims)
	if !ok {
		l.Logger.Error("jwt_user type assertion failed, value: %v", jwtUserValue)
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	uid := cast.ToInt64(jwtUser["uid"])

	// 优化：并发获取状态统计和订单数据
	var statusCount *types.OrderStatusCount
	var allOrders []types.OrderItem
	var totalCount int64

	// 使用channel进行并发查询
	statusChan := make(chan *types.OrderStatusCount, 1)
	statusErrChan := make(chan error, 1)
	ordersChan := make(chan []types.OrderItem, 1)
	ordersErrChan := make(chan error, 1)
	countChan := make(chan int64, 1)

	// 并发查询状态统计
	go func() {
		count, err := l.getOrderStatusCountOptimized(uid, req.Status)
		if err != nil {
			statusErrChan <- err
			return
		}
		statusChan <- count
	}()

	// 并发查询订单数据
	go func() {
		orders, total, err := l.getOrdersOptimized(uid, req)
		if err != nil {
			ordersErrChan <- err
			return
		}
		ordersChan <- orders
		countChan <- total
	}()

	// 等待并发查询结果
	select {
	case statusCount = <-statusChan:
	case err = <-statusErrChan:
		return nil, err
	}

	select {
	case allOrders = <-ordersChan:
		totalCount = <-countChan
	case err = <-ordersErrChan:
		return nil, err
	}

	// 初始化响应
	resp = &types.OrderListResp{
		List:        allOrders,
		Total:       totalCount,
		StatusCount: *statusCount,
	}

	return resp, nil
}

// getNormalOrders 获取普通订单和抽奖订单
func (l *ListLogic) getNormalOrders(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	// 构建查询条件
	builder := squirrel.Select(
		"o.id", "o.sub_order_no", "o.sub_order_status", "o.payment_amount",
		"o.cash_amount", "o.deductible_amount", "o.created_time", "o.payment_time",
		"o.goods_id", "o.order_qty", "o.type", "o.snapshot",
		"m.main_order_no",
	).From("vh_order o").
		LeftJoin("vh_order_main m ON o.main_order_id = m.id").
		Where(squirrel.Eq{"o.uid": uid}).
		Where("o.type IN (1, 2, 4)").
		OrderBy("o.created_time DESC")

	// 状态筛选 - 普通/抽奖订单按 sub_order_status 筛选
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		builder = builder.Where(squirrel.Eq{"o.sub_order_status": dbStatus})
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From("vh_order o").
		Where(squirrel.Eq{"o.uid": uid}).
		Where("o.type IN (1, 2, 4)")
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		countBuilder = countBuilder.Where(squirrel.Eq{"o.sub_order_status": dbStatus})
	}

	total, err := l.svcCtx.OrderModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("getNormalOrders FindCount error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 查询数据
	var orders []struct {
		Id               int64          `db:"id"`
		SubOrderNo       string         `db:"sub_order_no"`
		SubOrderStatus   int64          `db:"sub_order_status"`
		PaymentAmount    float64        `db:"payment_amount"`
		CashAmount       float64        `db:"cash_amount"`
		DeductibleAmount float64        `db:"deductible_amount"`
		CreatedTime      time.Time      `db:"created_time"`
		PaymentTime      int64          `db:"payment_time"`
		GoodsId          int64          `db:"goods_id"`
		OrderQty         int64          `db:"order_qty"`
		Type             int64          `db:"type"`
		Snapshot         sql.NullString `db:"snapshot"`
		MainOrderNo      string         `db:"main_order_no"`
	}

	err = l.svcCtx.OrderModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("getNormalOrders FindRows error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据格式
	result := make([]types.OrderItem, 0, len(orders))
	for _, order := range orders {
		orderItem := types.OrderItem{
			Uid:           uid,
			OrderType:     order.Type,
			MainOrderNo:   order.MainOrderNo,
			SubOrderNo:    order.SubOrderNo, // 普通/抽奖订单有子订单号
			OrderQty:      order.OrderQty,
			OrderStatus:   order.SubOrderStatus,
			PaymentAmount: order.PaymentAmount,
			CashAmount:    order.CashAmount,
			CreatedTime:   function.FormatTime(order.CreatedTime),
			PaymentTime:   function.FormatTimestamp(order.PaymentTime),
			ValidTimeUnit: "",                                  // 普通订单周期单位为空
			ValidTimeNum:  0,                                   // 普通订单周期数量为0
			BoxItems:      make([]types.OrderBoxItemDetail, 0), // 普通订单盲盒项目为空
		}

		// 解析商品信息获取标题和图片
		title, avatarImage, err := l.parseGoodsBasicInfo(order.GoodsId, order.Snapshot)
		if err != nil {
			l.Logger.Error("parseGoodsBasicInfo error: %v", err)
			continue
		}
		orderItem.Title = title
		orderItem.AvatarImage = avatarImage

		result = append(result, orderItem)
	}

	return result, total, nil
}

// getBoxOrders 获取盲盒订单
func (l *ListLogic) getBoxOrders(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	// 构建查询条件
	builder := squirrel.Select(
		"m.id", "m.main_order_no", "m.main_order_status", "m.payment_amount",
		"m.cash_amount", "m.deductible_amount", "m.created_time", "m.payment_time",
		"m.snapshot",
	).From("vh_order_main m").
		Where(squirrel.Eq{"m.uid": uid}).
		Where("EXISTS (SELECT 1 FROM vh_order_main_box b WHERE b.main_order_no = m.main_order_no)").
		OrderBy("m.created_time DESC")

	// 状态筛选 - 盲盒订单按 main_order_status 筛选
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		builder = builder.Where(squirrel.Eq{"m.main_order_status": dbStatus})
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From("vh_order_main m").
		Where(squirrel.Eq{"m.uid": uid}).
		Where("EXISTS (SELECT 1 FROM vh_order_main_box b WHERE b.main_order_no = m.main_order_no)")
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		countBuilder = countBuilder.Where(squirrel.Eq{"m.main_order_status": dbStatus})
	}

	total, err := l.svcCtx.OrderMainModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("getBoxOrders FindCount error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 查询数据
	var orders []struct {
		Id               int64          `db:"id"`
		MainOrderNo      string         `db:"main_order_no"`
		MainOrderStatus  int64          `db:"main_order_status"`
		PaymentAmount    float64        `db:"payment_amount"`
		CashAmount       float64        `db:"cash_amount"`
		DeductibleAmount float64        `db:"deductible_amount"`
		CreatedTime      time.Time      `db:"created_time"`
		PaymentTime      int64          `db:"payment_time"`
		Snapshot         sql.NullString `db:"snapshot"`
	}

	err = l.svcCtx.OrderMainModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("getBoxOrders FindRows error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据格式
	result := make([]types.OrderItem, 0, len(orders))
	for _, order := range orders {
		orderItem := types.OrderItem{
			Uid:           uid,
			OrderType:     3, // 盲盒订单
			MainOrderNo:   order.MainOrderNo,
			SubOrderNo:    "", // 盲盒订单没有子订单号，返回空字符串
			OrderQty:      1,  // 盲盒订单数量固定为1
			OrderStatus:   order.MainOrderStatus,
			PaymentAmount: order.PaymentAmount,
			CashAmount:    order.CashAmount,
			CreatedTime:   function.FormatTime(order.CreatedTime),
			PaymentTime:   function.FormatTimestamp(order.PaymentTime),
		}

		// 解析盲盒信息
		title, avatarImage, validTimeUnit, validTimeNum, boxItems, _, err := l.parseBoxInfoForList(order.MainOrderNo, order.Snapshot)
		if err != nil {
			l.Logger.Error("parseBoxInfoForList error: %v", err)
			continue
		}
		orderItem.Title = title
		orderItem.AvatarImage = avatarImage
		orderItem.ValidTimeUnit = validTimeUnit
		orderItem.ValidTimeNum = validTimeNum
		orderItem.BoxItems = boxItems

		result = append(result, orderItem)
	}

	return result, total, nil
}

// parseGoodsBasicInfo 解析商品基本信息(标题和图片)
func (l *ListLogic) parseGoodsBasicInfo(goodsId int64, snapshot sql.NullString) (string, string, error) {
	// 优先从快照解析
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			title := ""
			avatarImage := ""
			if t, ok := snapshotData["title"].(string); ok {
				title = t
			}
			if img, ok := snapshotData["avatar_image"].(string); ok {
				avatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, img)
			}
			return title, avatarImage, nil
		}
	}

	// 快照解析失败，从数据库查询
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(goodsId))
	if err != nil {
		if err == model.ErrNotFound {
			return "商品已下架", "", nil
		}
		return "", "", err
	}

	return goods.Title, l.processImage(goods.AvatarImage), nil
}

// parseGoodsInfo 解析商品信息
func (l *ListLogic) parseGoodsInfo(goodsId, quantity int64, snapshot sql.NullString) (*types.OrderGoodsInfo, error) {
	goodsInfo := &types.OrderGoodsInfo{
		GoodsId:  goodsId,
		Quantity: quantity,
	}

	// 优先从快照解析
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			if title, ok := snapshotData["title"].(string); ok {
				goodsInfo.Title = title
			}
			if avatarImage, ok := snapshotData["avatar_image"].(string); ok {
				goodsInfo.AvatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, avatarImage)
			}
			if price, ok := snapshotData["price"].(float64); ok {
				goodsInfo.Price = price
			}
			return goodsInfo, nil
		}
	}

	// 快照解析失败，从数据库查询
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(goodsId))
	if err != nil {
		if err == model.ErrNotFound {
			goodsInfo.Title = "商品已下架"
			return goodsInfo, nil
		}
		return nil, err
	}

	goodsInfo.Title = goods.Title
	goodsInfo.AvatarImage = l.processImage(goods.AvatarImage)
	goodsInfo.Price = goods.Price

	return goodsInfo, nil
}

// parseBoxInfoForList 解析盲盒信息用于列表显示
func (l *ListLogic) parseBoxInfoForList(mainOrderNo string, snapshot sql.NullString) (string, string, string, int64, []types.OrderBoxItemDetail, bool, error) {
	// 查询盲盒订单详情
	boxOrders, err := l.svcCtx.OrderMainBoxModel.FindByMainOrderNo(l.ctx, mainOrderNo)
	if err != nil || len(boxOrders) == 0 {
		return "", "", "", 0, nil, false, fmt.Errorf("盲盒订单详情不存在")
	}

	// 使用第一个记录获取基本信息
	firstBoxOrder := boxOrders[0]

	// 从快照中解析周期信息
	validTimeUnit := ""
	validTimeNum := int64(0)
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			if unit, ok := snapshotData["valid_time_unit"].(string); ok {
				validTimeUnit = unit
			}
			if num, ok := snapshotData["valid_time_num"].(float64); ok {
				validTimeNum = int64(num)
			}
		}
	}

	// 收集所有盲盒项目信息
	hasUnclaimedItems := false
	boxItems := make([]types.OrderBoxItemDetail, 0)

	for _, order := range boxOrders {
		// 检查未领取状态
		if order.BoxType.Valid && order.BoxType.Int64 == 1 { // 待领取
			hasUnclaimedItems = true
		}

		// 构建盲盒项目详情
		boxItem := types.OrderBoxItemDetail{
			Id:           order.Id,  // vh_order_main_box.id
			BoxType:      0,
			MonthLabel:   l.generateMonthLabelSimple(order.BeginTime),
			CanClaim:     false,
			CanTransfer:  false,
			BeginTime:    "",
			EndTime:      "",
			GetTime:      "",
			OrderNo:      "",
			ClaimedPhone: "",
		}

		// 设置盲盒类型和时间
		if order.BoxType.Valid {
			boxItem.BoxType = order.BoxType.Int64
		}
		if order.BeginTime.Valid {
			boxItem.BeginTime = function.FormatTime(order.BeginTime.Time)
		}
		if order.EndTime.Valid {
			boxItem.EndTime = function.FormatTime(order.EndTime.Time)
		}
		if order.GetTime.Valid {
			boxItem.GetTime = function.FormatTime(order.GetTime.Time)
		}

		// 设置子订单号
		if order.OrderNo.Valid {
			boxItem.OrderNo = order.OrderNo.String
		}

		// 判断是否可以领取和转赠
		now := time.Now()
		if order.BoxType.Valid && order.BoxType.Int64 == 1 { // 待领取
			if order.BeginTime.Valid && order.EndTime.Valid {
				if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
					boxItem.CanClaim = true
					boxItem.CanTransfer = true
				}
			}
		} else if order.BoxType.Valid && order.BoxType.Int64 == 4 { // 赠送中
			// 赠送中状态可以重新转赠，但要判断有效期
			if order.BeginTime.Valid && order.EndTime.Valid {
				if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
					boxItem.CanTransfer = true
				}
			}
		} else if order.BoxType.Valid && (order.BoxType.Int64 == 2 || order.BoxType.Int64 == 3) { // 已领取或已转赠
			// 根据子订单号获取加密手机号
			if order.OrderNo.Valid && order.OrderNo.String != "" {
				phone, err := l.getClaimedPhoneByOrderNo(order.OrderNo.String)
				if err != nil {
					l.Logger.Error("getClaimedPhoneByOrderNo error: %v", err)
				} else {
					boxItem.ClaimedPhone = l.maskPhone(phone)
				}
			}
		}

		boxItems = append(boxItems, boxItem)
	}

	// 解析盲盒标题和图片
	title := ""
	avatarImage := ""

	// 优先从快照解析盲盒基本信息
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			if t, ok := snapshotData["title"].(string); ok {
				title = t
			}
			if img, ok := snapshotData["avatar_image"].(string); ok {
				avatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, img)
			}
			return title, avatarImage, validTimeUnit, validTimeNum, boxItems, hasUnclaimedItems, nil
		}
	}

	// 快照解析失败，从数据库查询
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(firstBoxOrder.BoxId))
	if err != nil {
		if err == model.ErrNotFound {
			title = "盲盒已下架"
			return title, avatarImage, validTimeUnit, validTimeNum, boxItems, hasUnclaimedItems, nil
		}
		return "", "", "", 0, nil, false, err
	}

	title = box.Title
	avatarImage = l.processImage(box.AvatarImage)

	return title, avatarImage, validTimeUnit, validTimeNum, boxItems, hasUnclaimedItems, nil
}

// parseBoxInfoWithStatus 解析盲盒信息并返回是否有未领取项目
func (l *ListLogic) parseBoxInfoWithStatus(mainOrderNo string, snapshot sql.NullString) (*types.OrderBoxInfo, bool, error) {
	// 查询盲盒订单详情
	boxOrders, err := l.svcCtx.OrderMainBoxModel.FindByMainOrderNo(l.ctx, mainOrderNo)
	if err != nil || len(boxOrders) == 0 {
		return nil, false, fmt.Errorf("盲盒订单详情不存在")
	}

	// 使用第一个记录获取基本信息
	firstBoxOrder := boxOrders[0]
	boxInfo := &types.OrderBoxInfo{
		BoxId:    firstBoxOrder.BoxId,
		BoxItems: make([]types.OrderBoxItemDetail, 0),
	}

	// 收集所有盲盒项目信息
	hasUnclaimedItems := false
	for _, order := range boxOrders {
		// 检查未领取状态
		if order.BoxType.Valid && order.BoxType.Int64 == 1 { // 待领取
			hasUnclaimedItems = true
		}

		// 构建盲盒项目详情
		boxItem := types.OrderBoxItemDetail{
			Id:           order.Id, // vh_order_main_box.id
			BoxType:      0,
			CanClaim:     false,
			CanTransfer:  false,
			BeginTime:    "",
			EndTime:      "",
			GetTime:      "",
			OrderNo:      "",
			ClaimedPhone: "",
		}

		// 设置盲盒类型和时间
		if order.BoxType.Valid {
			boxItem.BoxType = order.BoxType.Int64
		}
		if order.BeginTime.Valid {
			boxItem.BeginTime = function.FormatTime(order.BeginTime.Time)
		}
		if order.EndTime.Valid {
			boxItem.EndTime = function.FormatTime(order.EndTime.Time)
		}
		if order.GetTime.Valid {
			boxItem.GetTime = function.FormatTime(order.GetTime.Time)
		}

		// 判断是否可以领取和转赠
		now := time.Now()
		if order.BoxType.Valid && order.BoxType.Int64 == 1 { // 待领取
			if order.BeginTime.Valid && order.EndTime.Valid {
				if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
					boxItem.CanClaim = true
					boxItem.CanTransfer = true
				}
			}
		} else if order.BoxType.Valid && order.BoxType.Int64 == 4 { // 赠送中
			// 赠送中状态可以重新转赠，但要判断有效期
			if order.BeginTime.Valid && order.EndTime.Valid {
				if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
					boxItem.CanTransfer = true
				}
			}
		} else if order.BoxType.Valid && (order.BoxType.Int64 == 2 || order.BoxType.Int64 == 3) { // 已领取或已转赠
			// 查询关联的子订单获取手机号
			if order.OrderNo.Valid && order.OrderNo.String != "" {
				phone, err := l.getClaimedPhoneByOrderNo(order.OrderNo.String)
				if err != nil {
					l.Logger.Error("getClaimedPhoneByOrderNo error: %v", err)
				} else {
					boxItem.ClaimedPhone = l.maskPhone(phone)
				}
			}
		}

		// 设置子订单号
		if order.OrderNo.Valid {
			boxItem.OrderNo = order.OrderNo.String
		}

		boxInfo.BoxItems = append(boxInfo.BoxItems, boxItem)
	}

	// 优先从快照解析盲盒基本信息
	if snapshot.Valid && snapshot.String != "" {
		var snapshotData map[string]interface{}
		if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
			if title, ok := snapshotData["title"].(string); ok {
				boxInfo.Title = title
			}
			if avatarImage, ok := snapshotData["avatar_image"].(string); ok {
				boxInfo.AvatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, avatarImage)
			}
			return boxInfo, hasUnclaimedItems, nil
		}
	}

	// 快照解析失败，从数据库查询
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(firstBoxOrder.BoxId))
	if err != nil {
		if err == model.ErrNotFound {
			boxInfo.Title = "盲盒已下架"
			return boxInfo, hasUnclaimedItems, nil
		}
		return nil, false, err
	}

	boxInfo.Title = box.Title
	boxInfo.AvatarImage = l.processImage(box.AvatarImage)

	return boxInfo, hasUnclaimedItems, nil
}

// sortOrdersByPriority 按优先级排序：盲盒类型且main_order_status=1的订单置顶，其他按创建时间倒序
func (l *ListLogic) sortOrdersByPriority(orders []types.OrderItem) {
	// 使用冒泡排序，按优先级排序
	for i := 0; i < len(orders)-1; i++ {
		for j := 0; j < len(orders)-1-i; j++ {
			if l.shouldSwap(orders[j], orders[j+1]) {
				orders[j], orders[j+1] = orders[j+1], orders[j]
			}
		}
	}
}

// shouldSwap 判断是否需要交换两个订单的位置
func (l *ListLogic) shouldSwap(order1, order2 types.OrderItem) bool {
	// 1. 盲盒类型且main_order_status=1的订单优先级最高
	isOrder1Priority := order1.OrderType == 3 && order1.OrderStatus == 1
	isOrder2Priority := order2.OrderType == 3 && order2.OrderStatus == 1

	if isOrder1Priority != isOrder2Priority {
		return !isOrder1Priority && isOrder2Priority
	}

	// 2. 同等优先级下，按创建时间倒序排序
	time1, err1 := time.Parse("2006-01-02 15:04:05", order1.CreatedTime)
	time2, err2 := time.Parse("2006-01-02 15:04:05", order2.CreatedTime)

	if err1 == nil && err2 == nil {
		return time1.Before(time2)
	}

	return false
}

// getClaimedPhoneByOrderNo 根据子订单号查询领取人手机号
func (l *ListLogic) getClaimedPhoneByOrderNo(orderNo string) (string, error) {
	// 查询子订单
	subOrder, err := l.svcCtx.OrderModel.FindOneBySubOrderNo(l.ctx, orderNo)
	if err != nil {
		if err == model.ErrNotFound {
			return "", fmt.Errorf("子订单不存在")
		}
		return "", err
	}

	// 查询用户信息获取手机号
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, subOrder.Uid)
	if err != nil {
		if err == model.ErrNotFound {
			return "", fmt.Errorf("用户不存在")
		}
		return "", err
	}

	return user.Telephone, nil
}

// maskPhone 手机号脱敏处理，格式：177****7777
func (l *ListLogic) maskPhone(phone string) string {
	if len(phone) != 11 {
		return phone // 如果不是11位手机号，直接返回
	}

	// 保留前3位和后4位，中间用****替换
	return phone[:3] + "****" + phone[7:]
}

// generateMonthLabelSimple 生成简单的月份标识，只显示年月
func (l *ListLogic) generateMonthLabelSimple(beginTime sql.NullTime) string {
	if !beginTime.Valid {
		return ""
	}

	return beginTime.Time.Format("2006年01月")
}

// generateMonthLabelWithPeriod 生成月份标识，结合周期信息
func (l *ListLogic) generateMonthLabelWithPeriod(beginTime sql.NullTime, validTimeUnit string, validTimeNum int64) string {
	if !beginTime.Valid {
		return ""
	}

	// 默认月份格式
	monthStr := beginTime.Time.Format("2006年01月")

	// 如果有周期信息，添加到月份标识中
	if validTimeUnit == "month" && validTimeNum > 0 {
		return fmt.Sprintf("%s (%d个月份)", monthStr, validTimeNum)
	}

	return monthStr
}

// processImage 处理图片URL
func (l *ListLogic) processImage(image string) string {
	if image == "" {
		return ""
	}
	if strings.HasPrefix(image, "http") {
		return image
	}
	return l.svcCtx.Config.ITEM.ALIURL + image
}

// getOrderStatusCountOptimized 优化的状态统计查询 - 使用UNION ALL一次查询
func (l *ListLogic) getOrderStatusCountOptimized(uid int64, filterStatus int64) (*types.OrderStatusCount, error) {
	statusCount := &types.OrderStatusCount{
		PendingPayment:  0,
		PendingShipment: 0,
		Shipped:         0,
		Completed:       0,
	}

	var results []struct {
		OrderType string `db:"order_type"`
		Status    int64  `db:"status"`
		Count     int64  `db:"count"`
	}

	if filterStatus == 0 {
		// 查询全部订单时，分别查询普通订单和盲盒订单的状态统计
		// 1. 查询普通订单状态统计
		normalBuilder := squirrel.Select("'normal' as order_type", "sub_order_status as status", "COUNT(*) as count").
			From("vh_order").
			Where(squirrel.Eq{"uid": uid}).
			Where("type IN (1, 2, 4)").
			GroupBy("sub_order_status")

		var normalResults []struct {
			OrderType string `db:"order_type"`
			Status    int64  `db:"status"`
			Count     int64  `db:"count"`
		}

		err := l.svcCtx.OrderModel.FindRows(l.ctx, normalBuilder, &normalResults)
		if err != nil {
			l.Logger.Error("getOrderStatusCountOptimized normal query error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		// 2. 查询盲盒订单状态统计
		boxBuilder := squirrel.Select("'box' as order_type", "main_order_status as status", "COUNT(*) as count").
			From("vh_order_main m").
			Where(squirrel.Eq{"uid": uid}).
			Where("EXISTS (SELECT 1 FROM vh_order_main_box b WHERE b.main_order_no = m.main_order_no)").
			GroupBy("main_order_status")

		var boxResults []struct {
			OrderType string `db:"order_type"`
			Status    int64  `db:"status"`
			Count     int64  `db:"count"`
		}

		err = l.svcCtx.OrderMainModel.FindRows(l.ctx, boxBuilder, &boxResults)
		if err != nil {
			l.Logger.Error("getOrderStatusCountOptimized box query error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		// 合并结果
		results = append(results, normalResults...)
		results = append(results, boxResults...)
	} else {
		// 按状态筛选时，只统计普通订单和抽奖订单
		builder := squirrel.Select("'normal' as order_type", "sub_order_status as status", "COUNT(*) as count").
			From("vh_order").
			Where(squirrel.Eq{"uid": uid}).
			Where("type IN (1, 2, 4)").
			GroupBy("sub_order_status")

		err := l.svcCtx.OrderModel.FindRows(l.ctx, builder, &results)
		if err != nil {
			l.Logger.Error("getOrderStatusCountOptimized query error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}
	}

	// 统计结果
	for _, result := range results {
		switch result.Status {
		case 0: // 待支付
			statusCount.PendingPayment += result.Count
		case 1: // 待发货
			statusCount.PendingShipment += result.Count
		case 2: // 已发货
			statusCount.Shipped += result.Count
		case 3: // 已完成
			statusCount.Completed += result.Count
		}
	}

	l.Logger.Infof("getOrderStatusCountOptimized result (filterStatus=%d): pending_payment=%d, pending_shipment=%d, shipped=%d, completed=%d",
		filterStatus, statusCount.PendingPayment, statusCount.PendingShipment, statusCount.Shipped, statusCount.Completed)

	return statusCount, nil
}

// getOrderStatusCount 获取订单状态统计 - 保留原方法作为备用
func (l *ListLogic) getOrderStatusCount(uid int64, filterStatus int64) (*types.OrderStatusCount, error) {
	return l.getOrderStatusCountOptimized(uid, filterStatus)
}

// getOrdersOptimized 优化的订单查询 - 统一处理排序和分页
func (l *ListLogic) getOrdersOptimized(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	if req.Status == 0 {
		// 查询全部订单时，需要合并普通订单和盲盒订单，并进行特殊排序
		return l.getAllOrdersWithPriority(uid, req)
	} else {
		// 按状态筛选时，只查询普通订单，可以直接在数据库层分页
		return l.getNormalOrdersOptimized(uid, req)
	}
}

// getAllOrdersWithPriority 获取全部订单并按优先级排序
func (l *ListLogic) getAllOrdersWithPriority(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	var allOrders []types.OrderItem
	var totalCount int64

	// 并发查询普通订单和盲盒订单
	normalChan := make(chan []types.OrderItem, 1)
	normalCountChan := make(chan int64, 1)
	normalErrChan := make(chan error, 1)

	boxChan := make(chan []types.OrderItem, 1)
	boxCountChan := make(chan int64, 1)
	boxErrChan := make(chan error, 1)

	// 并发查询普通订单
	go func() {
		orders, count, err := l.getNormalOrders(uid, req)
		if err != nil {
			normalErrChan <- err
			return
		}
		normalChan <- orders
		normalCountChan <- count
	}()

	// 并发查询盲盒订单
	go func() {
		orders, count, err := l.getBoxOrdersOptimized(uid, req)
		if err != nil {
			boxErrChan <- err
			return
		}
		boxChan <- orders
		boxCountChan <- count
	}()

	// 等待查询结果
	var normalOrders, boxOrders []types.OrderItem
	var normalCount, boxCount int64

	select {
	case normalOrders = <-normalChan:
		normalCount = <-normalCountChan
	case err := <-normalErrChan:
		return nil, 0, err
	}

	select {
	case boxOrders = <-boxChan:
		boxCount = <-boxCountChan
	case err := <-boxErrChan:
		return nil, 0, err
	}

	// 合并订单
	allOrders = append(allOrders, normalOrders...)
	allOrders = append(allOrders, boxOrders...)
	totalCount = normalCount + boxCount

	// 按优先级排序：盲盒类型且main_order_status=1的订单置顶
	l.sortOrdersByPriority(allOrders)

	// 分页处理
	offset := (req.Page - 1) * req.Limit
	if offset >= int64(len(allOrders)) {
		return []types.OrderItem{}, totalCount, nil
	}

	end := offset + req.Limit
	if end > int64(len(allOrders)) {
		end = int64(len(allOrders))
	}

	return allOrders[offset:end], totalCount, nil
}

// getNormalOrdersOptimized 优化的普通订单查询 - 数据库层分页
func (l *ListLogic) getNormalOrdersOptimized(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	// 先查询总数
	countBuilder := squirrel.Select("COUNT(*)").From("vh_order o").
		Where(squirrel.Eq{"o.uid": uid}).
		Where("o.type IN (1, 2, 4)")
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		countBuilder = countBuilder.Where(squirrel.Eq{"o.sub_order_status": dbStatus})
	}

	total, err := l.svcCtx.OrderModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("getNormalOrdersOptimized FindCount error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 如果没有数据，直接返回
	if total == 0 {
		return []types.OrderItem{}, 0, nil
	}

	// 构建分页查询
	offset := (req.Page - 1) * req.Limit
	builder := squirrel.Select(
		"o.id", "o.sub_order_no", "o.sub_order_status", "o.payment_amount",
		"o.cash_amount", "o.deductible_amount", "o.created_time", "o.payment_time",
		"o.goods_id", "o.order_qty", "o.type", "o.snapshot",
		"m.main_order_no",
	).From("vh_order o").
		LeftJoin("vh_order_main m ON o.main_order_id = m.id").
		Where(squirrel.Eq{"o.uid": uid}).
		Where("o.type IN (1, 2, 4)").
		OrderBy("o.created_time DESC").
		Limit(uint64(req.Limit)).
		Offset(uint64(offset))

	// 状态筛选
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		builder = builder.Where(squirrel.Eq{"o.sub_order_status": dbStatus})
	}

	// 查询数据
	var orders []struct {
		Id               int64          `db:"id"`
		SubOrderNo       string         `db:"sub_order_no"`
		SubOrderStatus   int64          `db:"sub_order_status"`
		PaymentAmount    float64        `db:"payment_amount"`
		CashAmount       float64        `db:"cash_amount"`
		DeductibleAmount float64        `db:"deductible_amount"`
		CreatedTime      time.Time      `db:"created_time"`
		PaymentTime      int64          `db:"payment_time"`
		GoodsId          int64          `db:"goods_id"`
		OrderQty         int64          `db:"order_qty"`
		Type             int64          `db:"type"`
		Snapshot         sql.NullString `db:"snapshot"`
		MainOrderNo      string         `db:"main_order_no"`
	}

	err = l.svcCtx.OrderModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("getNormalOrdersOptimized FindRows error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 批量获取商品信息
	goodsInfoMap, err := l.batchGetGoodsInfo(orders)
	if err != nil {
		l.Logger.Error("batchGetGoodsInfo error: %v", err)
		// 不返回错误，继续处理，只是商品信息可能不完整
	}

	// 转换数据格式
	result := make([]types.OrderItem, 0, len(orders))
	for _, order := range orders {
		orderItem := types.OrderItem{
			Uid:           uid,
			OrderType:     order.Type,
			MainOrderNo:   order.MainOrderNo,
			SubOrderNo:    order.SubOrderNo,
			OrderQty:      order.OrderQty,
			OrderStatus:   order.SubOrderStatus,
			PaymentAmount: order.PaymentAmount,
			CashAmount:    order.CashAmount,
			CreatedTime:   function.FormatTime(order.CreatedTime),
			PaymentTime:   function.FormatTimestamp(order.PaymentTime),
			ValidTimeUnit: "",
			ValidTimeNum:  0,
			BoxItems:      make([]types.OrderBoxItemDetail, 0),
		}

		// 从批量查询结果中获取商品信息
		if goodsInfo, exists := goodsInfoMap[order.GoodsId]; exists {
			orderItem.Title = goodsInfo.Title
			orderItem.AvatarImage = goodsInfo.AvatarImage
		} else {
			// 如果批量查询失败，回退到单个查询
			title, avatarImage, err := l.parseGoodsBasicInfo(order.GoodsId, order.Snapshot)
			if err != nil {
				l.Logger.Error("parseGoodsBasicInfo error: %v", err)
				continue
			}
			orderItem.Title = title
			orderItem.AvatarImage = avatarImage
		}

		result = append(result, orderItem)
	}

	return result, total, nil
}

// batchGetGoodsInfo 批量获取商品信息
func (l *ListLogic) batchGetGoodsInfo(orders []struct {
	Id               int64          `db:"id"`
	SubOrderNo       string         `db:"sub_order_no"`
	SubOrderStatus   int64          `db:"sub_order_status"`
	PaymentAmount    float64        `db:"payment_amount"`
	CashAmount       float64        `db:"cash_amount"`
	DeductibleAmount float64        `db:"deductible_amount"`
	CreatedTime      time.Time      `db:"created_time"`
	PaymentTime      int64          `db:"payment_time"`
	GoodsId          int64          `db:"goods_id"`
	OrderQty         int64          `db:"order_qty"`
	Type             int64          `db:"type"`
	Snapshot         sql.NullString `db:"snapshot"`
	MainOrderNo      string         `db:"main_order_no"`
}) (map[int64]*GoodsBasicInfo, error) {
	goodsInfoMap := make(map[int64]*GoodsBasicInfo)
	goodsIds := make([]int64, 0)
	snapshotMap := make(map[int64]sql.NullString)

	// 收集需要查询的商品ID和快照信息
	for _, order := range orders {
		if _, exists := goodsInfoMap[order.GoodsId]; !exists {
			goodsIds = append(goodsIds, order.GoodsId)
			snapshotMap[order.GoodsId] = order.Snapshot
		}
	}

	if len(goodsIds) == 0 {
		return goodsInfoMap, nil
	}

	// 优先从快照解析商品信息
	for goodsId, snapshot := range snapshotMap {
		if snapshot.Valid && snapshot.String != "" {
			var snapshotData map[string]interface{}
			if err := json.Unmarshal([]byte(snapshot.String), &snapshotData); err == nil {
				goodsInfo := &GoodsBasicInfo{}
				if title, ok := snapshotData["title"].(string); ok {
					goodsInfo.Title = title
				}
				if img, ok := snapshotData["avatar_image"].(string); ok {
					goodsInfo.AvatarImage = function.ProcessSnapshotImage(l.svcCtx.Config.ITEM.ALIURL, img)
				}
				if goodsInfo.Title != "" {
					goodsInfoMap[goodsId] = goodsInfo
				}
			}
		}
	}

	// 批量查询数据库中的商品信息（只查询快照解析失败的）
	needDbQueryIds := make([]uint64, 0)
	for _, goodsId := range goodsIds {
		if _, exists := goodsInfoMap[goodsId]; !exists {
			needDbQueryIds = append(needDbQueryIds, uint64(goodsId))
		}
	}

	if len(needDbQueryIds) > 0 {
		goodsList, err := l.svcCtx.GoodsModel.FindByIds(l.ctx, needDbQueryIds)
		if err != nil {
			l.Logger.Error("batchGetGoodsInfo FindByIds error: %v", err)
			return goodsInfoMap, err
		}

		for _, goods := range goodsList {
			goodsInfoMap[int64(goods.Id)] = &GoodsBasicInfo{
				Title:       goods.Title,
				AvatarImage: l.processImage(goods.AvatarImage),
			}
		}
	}

	// 对于仍然没有找到的商品，设置默认值
	for _, goodsId := range goodsIds {
		if _, exists := goodsInfoMap[goodsId]; !exists {
			goodsInfoMap[goodsId] = &GoodsBasicInfo{
				Title:       "商品已下架",
				AvatarImage: "",
			}
		}
	}

	return goodsInfoMap, nil
}

// GoodsBasicInfo 商品基本信息
type GoodsBasicInfo struct {
	Title       string
	AvatarImage string
}

// getBoxOrdersOptimized 优化的盲盒订单查询 - 使用JOIN替代EXISTS，批量查询详情
func (l *ListLogic) getBoxOrdersOptimized(uid int64, req *types.OrderListReq) ([]types.OrderItem, int64, error) {
	// 使用JOIN替代EXISTS提高查询效率
	builder := squirrel.Select(
		"DISTINCT m.id", "m.main_order_no", "m.main_order_status", "m.payment_amount",
		"m.cash_amount", "m.deductible_amount", "m.created_time", "m.payment_time",
		"m.snapshot",
	).From("vh_order_main m").
		InnerJoin("vh_order_main_box b ON m.main_order_no = b.main_order_no").
		Where(squirrel.Eq{"m.uid": uid}).
		OrderBy("m.created_time DESC")

	// 状态筛选
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		builder = builder.Where(squirrel.Eq{"m.main_order_status": dbStatus})
	}

	// 查询总数
	countBuilder := squirrel.Select("COUNT(DISTINCT m.id)").From("vh_order_main m").
		InnerJoin("vh_order_main_box b ON m.main_order_no = b.main_order_no").
		Where(squirrel.Eq{"m.uid": uid})
	if req.Status > 0 {
		dbStatus := l.mapApiStatusToDbStatus(req.Status)
		countBuilder = countBuilder.Where(squirrel.Eq{"m.main_order_status": dbStatus})
	}

	total, err := l.svcCtx.OrderMainModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("getBoxOrdersOptimized FindCount error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	// 查询数据
	var orders []struct {
		Id               int64          `db:"id"`
		MainOrderNo      string         `db:"main_order_no"`
		MainOrderStatus  int64          `db:"main_order_status"`
		PaymentAmount    float64        `db:"payment_amount"`
		CashAmount       float64        `db:"cash_amount"`
		DeductibleAmount float64        `db:"deductible_amount"`
		CreatedTime      time.Time      `db:"created_time"`
		PaymentTime      int64          `db:"payment_time"`
		Snapshot         sql.NullString `db:"snapshot"`
	}

	err = l.svcCtx.OrderMainModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("getBoxOrdersOptimized FindRows error: %v", err)
		return nil, 0, xerr.NewErrCode(xerr.DbError)
	}

	if len(orders) == 0 {
		return []types.OrderItem{}, total, nil
	}

	// 批量查询盲盒详情
	mainOrderNos := make([]string, len(orders))
	for i, order := range orders {
		mainOrderNos[i] = order.MainOrderNo
	}

	boxDetailsMap, err := l.batchGetBoxDetails(mainOrderNos)
	if err != nil {
		l.Logger.Error("batchGetBoxDetails error: %v", err)
		// 不返回错误，继续处理，只是盲盒详情可能不完整
	}

	// 转换数据格式
	result := make([]types.OrderItem, 0, len(orders))
	for _, order := range orders {
		orderItem := types.OrderItem{
			Uid:           uid,
			OrderType:     3, // 盲盒订单
			MainOrderNo:   order.MainOrderNo,
			SubOrderNo:    "", // 盲盒订单没有子订单号
			OrderQty:      1,  // 盲盒订单数量固定为1
			OrderStatus:   order.MainOrderStatus,
			PaymentAmount: order.PaymentAmount,
			CashAmount:    order.CashAmount,
			CreatedTime:   function.FormatTime(order.CreatedTime),
			PaymentTime:   function.FormatTimestamp(order.PaymentTime),
		}

		// 从批量查询结果中获取盲盒信息
		if boxDetail, exists := boxDetailsMap[order.MainOrderNo]; exists {
			orderItem.Title = boxDetail.Title
			orderItem.AvatarImage = boxDetail.AvatarImage
			orderItem.ValidTimeUnit = boxDetail.ValidTimeUnit
			orderItem.ValidTimeNum = boxDetail.ValidTimeNum
			orderItem.BoxItems = boxDetail.BoxItems
		} else {
			// 如果批量查询失败，回退到单个查询
			title, avatarImage, validTimeUnit, validTimeNum, boxItems, _, err := l.parseBoxInfoForList(order.MainOrderNo, order.Snapshot)
			if err != nil {
				l.Logger.Error("parseBoxInfoForList error: %v", err)
				continue
			}
			orderItem.Title = title
			orderItem.AvatarImage = avatarImage
			orderItem.ValidTimeUnit = validTimeUnit
			orderItem.ValidTimeNum = validTimeNum
			orderItem.BoxItems = boxItems
		}

		result = append(result, orderItem)
	}

	return result, total, nil
}

// BoxDetailInfo 盲盒详情信息
type BoxDetailInfo struct {
	Title         string
	AvatarImage   string
	ValidTimeUnit string
	ValidTimeNum  int64
	BoxItems      []types.OrderBoxItemDetail
}

// batchGetBoxDetails 批量获取盲盒详情
func (l *ListLogic) batchGetBoxDetails(mainOrderNos []string) (map[string]*BoxDetailInfo, error) {
	if len(mainOrderNos) == 0 {
		return make(map[string]*BoxDetailInfo), nil
	}

	// 批量查询盲盒订单详情
	builder := squirrel.Select("*").From("vh_order_main_box").
		Where(squirrel.Eq{"main_order_no": mainOrderNos}).
		OrderBy("main_order_no, id")

	var boxOrders []*model.VhOrderMainBox
	err := l.svcCtx.OrderMainBoxModel.FindRows(l.ctx, builder, &boxOrders)
	if err != nil {
		l.Logger.Error("batchGetBoxDetails FindRows error: %v", err)
		return nil, err
	}

	// 按主订单号分组
	boxOrdersMap := make(map[string][]*model.VhOrderMainBox)
	boxIdSet := make(map[int64]bool)
	for _, boxOrder := range boxOrders {
		boxOrdersMap[boxOrder.MainOrderNo] = append(boxOrdersMap[boxOrder.MainOrderNo], boxOrder)
		boxIdSet[boxOrder.BoxId] = true
	}

	// 批量查询盲盒基本信息
	boxIds := make([]uint64, 0, len(boxIdSet))
	for boxId := range boxIdSet {
		boxIds = append(boxIds, uint64(boxId))
	}

	boxInfoMap := make(map[int64]*model.VhBox)
	if len(boxIds) > 0 {
		boxes, err := l.svcCtx.BoxModel.FindByIds(l.ctx, boxIds)
		if err != nil {
			l.Logger.Error("batchGetBoxDetails FindByIds error: %v", err)
		} else {
			for _, box := range boxes {
				boxInfoMap[int64(box.Id)] = box
			}
		}
	}

	// 批量查询需要手机号的子订单
	needPhoneOrderNos := make([]string, 0)
	for _, orders := range boxOrdersMap {
		for _, order := range orders {
			if order.BoxType.Valid && (order.BoxType.Int64 == 2 || order.BoxType.Int64 == 3) && order.OrderNo.Valid {
				needPhoneOrderNos = append(needPhoneOrderNos, order.OrderNo.String)
			}
		}
	}

	phoneMap, err := l.batchGetClaimedPhones(needPhoneOrderNos)
	if err != nil {
		l.Logger.Error("batchGetClaimedPhones error: %v", err)
		phoneMap = make(map[string]string) // 继续处理，只是手机号可能为空
	}

	// 构建结果
	result := make(map[string]*BoxDetailInfo)
	for mainOrderNo, orders := range boxOrdersMap {
		if len(orders) == 0 {
			continue
		}

		firstOrder := orders[0]
		boxDetail := &BoxDetailInfo{
			BoxItems: make([]types.OrderBoxItemDetail, 0, len(orders)),
		}

		// 获取盲盒基本信息
		if boxInfo, exists := boxInfoMap[firstOrder.BoxId]; exists {
			boxDetail.Title = boxInfo.Title
			boxDetail.AvatarImage = l.processImage(boxInfo.AvatarImage)
		} else {
			boxDetail.Title = "盲盒已下架"
		}

		// 处理每个盲盒项目
		for _, order := range orders {
			boxItem := types.OrderBoxItemDetail{
				Id:           order.Id,
				BoxType:      0,
				MonthLabel:   l.generateMonthLabelSimple(order.BeginTime),
				CanClaim:     false,
				CanTransfer:  false,
				BeginTime:    "",
				EndTime:      "",
				GetTime:      "",
				OrderNo:      "",
				ClaimedPhone: "",
			}

			// 设置盲盒类型和时间
			if order.BoxType.Valid {
				boxItem.BoxType = order.BoxType.Int64
			}
			if order.BeginTime.Valid {
				boxItem.BeginTime = function.FormatTime(order.BeginTime.Time)
			}
			if order.EndTime.Valid {
				boxItem.EndTime = function.FormatTime(order.EndTime.Time)
			}
			if order.GetTime.Valid {
				boxItem.GetTime = function.FormatTime(order.GetTime.Time)
			}
			if order.OrderNo.Valid {
				boxItem.OrderNo = order.OrderNo.String
			}

			// 判断是否可以领取和转赠
			now := time.Now()
			if order.BoxType.Valid && order.BoxType.Int64 == 1 { // 待领取
				if order.BeginTime.Valid && order.EndTime.Valid {
					if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
						boxItem.CanClaim = true
						boxItem.CanTransfer = true
					}
				}
			} else if order.BoxType.Valid && order.BoxType.Int64 == 4 { // 赠送中
				// 赠送中状态可以重新转赠，但要判断有效期
				if order.BeginTime.Valid && order.EndTime.Valid {
					if now.After(order.BeginTime.Time) && now.Before(order.EndTime.Time) {
						boxItem.CanTransfer = true
					}
				}
			} else if order.BoxType.Valid && (order.BoxType.Int64 == 2 || order.BoxType.Int64 == 3) { // 已领取或已转赠
				if order.OrderNo.Valid {
					if phone, exists := phoneMap[order.OrderNo.String]; exists {
						boxItem.ClaimedPhone = l.maskPhone(phone)
					}
				}
			}

			boxDetail.BoxItems = append(boxDetail.BoxItems, boxItem)
		}

		result[mainOrderNo] = boxDetail
	}

	return result, nil
}

// batchGetClaimedPhones 批量获取领取人手机号
func (l *ListLogic) batchGetClaimedPhones(orderNos []string) (map[string]string, error) {
	if len(orderNos) == 0 {
		return make(map[string]string), nil
	}

	// 批量查询子订单
	builder := squirrel.Select("sub_order_no", "uid").From("vh_order").
		Where(squirrel.Eq{"sub_order_no": orderNos})

	var orders []struct {
		SubOrderNo string `db:"sub_order_no"`
		Uid        int64  `db:"uid"`
	}

	err := l.svcCtx.OrderModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("batchGetClaimedPhones FindRows error: %v", err)
		return nil, err
	}

	if len(orders) == 0 {
		return make(map[string]string), nil
	}

	// 收集用户ID
	uidSet := make(map[int64]bool)
	orderUidMap := make(map[string]int64)
	for _, order := range orders {
		uidSet[order.Uid] = true
		orderUidMap[order.SubOrderNo] = order.Uid
	}

	uids := make([]int64, 0, len(uidSet))
	for uid := range uidSet {
		uids = append(uids, uid)
	}

	// 批量查询用户信息
	userBuilder := squirrel.Select("id", "telephone").From("vh_user").
		Where(squirrel.Eq{"id": uids})

	var users []struct {
		Id        int64  `db:"id"`
		Telephone string `db:"telephone"`
	}

	err = l.svcCtx.UserModel.FindRows(l.ctx, userBuilder, &users)
	if err != nil {
		l.Logger.Error("batchGetClaimedPhones user FindRows error: %v", err)
		return nil, err
	}

	// 构建用户ID到手机号的映射
	uidPhoneMap := make(map[int64]string)
	for _, user := range users {
		uidPhoneMap[user.Id] = user.Telephone
	}

	// 构建订单号到手机号的映射
	result := make(map[string]string)
	for orderNo, uid := range orderUidMap {
		if phone, exists := uidPhoneMap[uid]; exists {
			result[orderNo] = phone
		}
	}

	return result, nil
}

// getNormalOrderStatusCount 获取普通订单和抽奖订单的状态统计
func (l *ListLogic) getNormalOrderStatusCount(uid int64) (*types.OrderStatusCount, error) {
	statusCount := &types.OrderStatusCount{}

	var results []struct {
		Status int64 `db:"sub_order_status"`
		Count  int64 `db:"count"`
	}

	err := l.svcCtx.OrderModel.FindRows(l.ctx, squirrel.Select("sub_order_status, COUNT(*) as count").
		From(l.svcCtx.OrderModel.TableName()).
		Where(squirrel.Eq{"uid": uid}).
		Where(squirrel.Or{squirrel.Eq{"type": 1}, squirrel.Eq{"type": 2}, squirrel.Eq{"type": 4}}).
		GroupBy("sub_order_status"), &results)
	if err != nil {
		l.Logger.Error("getNormalOrderStatusCount query error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	for _, result := range results {
		switch result.Status {
		case 0: // 待支付
			statusCount.PendingPayment += result.Count
		case 1: // 待发货
			statusCount.PendingShipment += result.Count
		case 2: // 已发货
			statusCount.Shipped += result.Count
		case 3: // 已完成
			statusCount.Completed += result.Count
		}
	}

	return statusCount, nil
}

// getBoxOrderStatusCount 获取盲盒订单的状态统计
func (l *ListLogic) getBoxOrderStatusCount(uid int64) (*types.OrderStatusCount, error) {
	statusCount := &types.OrderStatusCount{}

	var results []struct {
		Status int64 `db:"main_order_status"`
		Count  int64 `db:"count"`
	}

	err := l.svcCtx.OrderMainModel.FindRows(l.ctx, squirrel.Select("main_order_status, COUNT(*) as count").
		From(l.svcCtx.OrderMainModel.TableName()).
		Where(squirrel.Eq{"uid": uid}).
		Where("EXISTS (SELECT 1 FROM vh_order_main_box b WHERE b.main_order_no = vh_order_main.main_order_no)").
		GroupBy("main_order_status"), &results)
	if err != nil {
		l.Logger.Error("getBoxOrderStatusCount query error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	for _, result := range results {
		switch result.Status {
		case 0: // 待支付
			statusCount.PendingPayment += result.Count
		case 1: // 已支付（对应待发货，因为盲盒是虚拟商品）
			statusCount.PendingShipment += result.Count
		case 2: // 已发货（盲盒不适用，但保留逻辑）
			statusCount.Shipped += result.Count
		case 3: // 已完成
			statusCount.Completed += result.Count
		}
	}

	return statusCount, nil
}
