package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftDetailLogic {
	return &GiftDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftDetailLogic) GiftDetail(req *types.BoxGiftDetailReq) (resp *types.BoxGiftDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
