package balance

import (
	"engine/api/internal/logic/admin/balance"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func WithdrawalReviewHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WithdrawalReviewReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := balance.NewWithdrawalReviewLogic(r.Context(), svcCtx)
		err := l.WithdrawalReview(&req)
		result.HttpResult(r, w, result.<PERSON>ull<PERSON><PERSON>{}, err)
	}
}
