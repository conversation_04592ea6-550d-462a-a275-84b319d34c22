package partner

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PartnerTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPartnerTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PartnerTasksLogic {
	return &PartnerTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PartnerTasksLogic) PartnerTasks(req *types.PartnerTasksReq) (resp *types.PartnerTasksResp, err error) {
	// todo: add your logic here and delete this line

	return
}
