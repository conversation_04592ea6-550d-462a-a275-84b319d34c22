package wechat

import (
	"engine/api/internal/logic/wechat"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func AppletOneclickLoginHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WeChatAppletOneclickLoginReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := wechat.NewAppletOneclickLoginLogic(r.Context(), svcCtx)
		resp, err := l.AppletOneclickLogin(&req)
		result.HttpResult(r, w, resp, err)
	}
}
