package box

import (
	"net/http"

	"engine/api/internal/logic/admin/box"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func BindGoodsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BoxBindGoodsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := box.NewBindGoodsLogic(r.Context(), svcCtx)
		err := l.BindGoods(&req)
		result.HttpResult(r, w, nil, err)
	}
}
