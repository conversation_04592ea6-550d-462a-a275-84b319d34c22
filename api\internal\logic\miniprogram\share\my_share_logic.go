package share

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MyShareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMyShareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MyShareLogic {
	return &MyShareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MyShareLogic) MyShare() (resp *types.MyShareResp, err error) {
	// todo: add your logic here and delete this line

	return
}
