package box

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDeleteLogic {
	return &AdminDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDeleteLogic) AdminDelete(req *types.BoxDeleteReq) error {
	// 查询盲盒是否存在
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDelete BoxModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已删除
	if box.DeleteTime > 0 {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 软删除盲盒
	deleteTime := time.Now().Unix()
	err = l.svcCtx.BoxModel.SoftDelete(l.ctx, uint64(req.Id), deleteTime)
	if err != nil {
		l.Logger.Error("AdminDelete BoxModel.SoftDelete error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
