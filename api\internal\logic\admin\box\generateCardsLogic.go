package box

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	mathrand "math/rand"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateCardsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateCardsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateCardsLogic {
	return &GenerateCardsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateCardsLogic) GenerateCards(req *types.BoxGenerateCardsReq) (resp *types.BoxGenerateCardsResp, err error) {
	// 查询盲盒是否存在
	box, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(req.BoxId))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("GenerateCards BoxModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已删除
	if box.DeleteTime > 0 {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 验证盲盒的items_info不能为空
	boxItems, err := l.svcCtx.BoxItemsModel.FindByBoxId(l.ctx, req.BoxId)
	if err != nil {
		l.Logger.Error("GenerateCards BoxItemsModel.FindByBoxId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if len(boxItems) == 0 {
		return nil, xerr.NewErrMsg("盲盒还未配置商品，无法生成礼品卡")
	}

	// 检查每个盲盒项目的items_info是否为空
	for _, item := range boxItems {
		if item.ItemsInfo == "" || item.ItemsInfo == "[]" {
			return nil, xerr.NewErrMsg("盲盒还未配置商品，无法生成礼品卡")
		}

		// 解析JSON检查是否有商品
		var itemsInfo []types.BoxItemInfo
		if err := json.Unmarshal([]byte(item.ItemsInfo), &itemsInfo); err != nil {
			l.Logger.Error("GenerateCards json.Unmarshal items_info error: %v", err)
			return nil, xerr.NewErrMsg("盲盒商品配置格式错误")
		}

		if len(itemsInfo) == 0 {
			return nil, xerr.NewErrMsg("盲盒还未配置商品，无法生成礼品卡")
		}
	}

	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 生成礼品卡数据
	cards := make([]*model.VhBoxCards, 0, req.Num)
	for i := int64(0); i < req.Num; i++ {
		cardNo := l.generateCardNo()
		salt := l.generateSalt()

		card := &model.VhBoxCards{
			CardNo:       cardNo,
			Salt:         salt,
			Status:       1, // 1=待使用
			BoxId:        uint64(req.BoxId),
			RepealName:   "",
			RepealRemark: "",
			QrCode:       "", // 二维码地址暂时为空字符串
			PrintCount:   0,  // 打印次数初始化为0
			// CreatedTime由数据库自动维护，不需要设置
		}
		cards = append(cards, card)
	}

	// 开启事务批量插入
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 批量插入礼品卡
		err := l.svcCtx.BoxCardsModel.BatchInsert(ctx, cards)
		if err != nil {
			l.Logger.Error("GenerateCards BatchInsert error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 更新盲盒的创建数量
		updateQuery := `UPDATE vh_box SET create_num = create_num + ?, vh_uid = ?, vh_vos_name = ? WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateQuery, req.Num, adminUid, adminVosName, req.BoxId)
		if err != nil {
			l.Logger.Error("GenerateCards update box create_num error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 异步生成二维码
	go func() {
		qrcodeGenerateService := service.NewQRCodeGenerateService(l.svcCtx)
		qrcodeGenerateService.GenerateQRCodesForCards(context.Background(), req.BoxId)
	}()

	return &types.BoxGenerateCardsResp{
		GeneratedNum: req.Num,
	}, nil
}

// generateCardNo 生成卡号 (32位)
func (l *GenerateCardsLogic) generateCardNo() string {
	// 使用时间戳 + 随机数生成唯一卡号
	timestamp := time.Now().UnixNano()
	randomBytes := make([]byte, 8)
	rand.Read(randomBytes)

	cardNo := fmt.Sprintf("%d%s", timestamp, hex.EncodeToString(randomBytes))
	if len(cardNo) > 32 {
		cardNo = cardNo[:32]
	}

	// 如果长度不足32位，用0补齐
	for len(cardNo) < 32 {
		cardNo = "0" + cardNo
	}

	return cardNo
}

// generateSalt 生成6位盐值（数字+大写字母组合，必须都包含）
func (l *GenerateCardsLogic) generateSalt() string {
	const digits = "23456789"
	const upperLetters = "ABCDEFGHJKLMNPQRSTUVWXYZ"
	const charset = digits + upperLetters

	for {
		salt := make([]byte, 6)
		for i := range salt {
			salt[i] = charset[mathrand.Intn(len(charset))]
		}

		saltStr := string(salt)

		// 检查是否包含数字
		hasDigit := false
		for _, char := range saltStr {
			if char >= '0' && char <= '9' {
				hasDigit = true
				break
			}
		}

		// 检查是否包含大写字母
		hasUpper := false
		for _, char := range saltStr {
			if char >= 'A' && char <= 'Z' {
				hasUpper = true
				break
			}
		}

		// 如果既包含数字又包含大写字母，返回结果
		if hasDigit && hasUpper {
			return saltStr
		}
		// 否则重新生成
	}
}
