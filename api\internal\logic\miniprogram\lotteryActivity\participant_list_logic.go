package lotteryActivity

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantListLogic {
	return &ParticipantListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantListLogic) ParticipantList(req *types.ActiveLotteryActivityParticipantListReq) (resp *types.ActiveLotteryActivityParticipantListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
