package share

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateTitleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateTitleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateTitleLogic {
	return &GenerateTitleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateTitleLogic) GenerateTitle(req *types.GenerateTitleReq) (resp *types.GenerateTitleResp, err error) {
	// todo: add your logic here and delete this line

	return
}
