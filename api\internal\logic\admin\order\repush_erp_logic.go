package order

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RepushErpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRepushErpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RepushErpLogic {
	return &RepushErpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RepushErpLogic) RepushErp(req *types.OrderRepushErpReq) (resp *types.OrderRepushErpResp, err error) {
	// todo: add your logic here and delete this line

	return
}
