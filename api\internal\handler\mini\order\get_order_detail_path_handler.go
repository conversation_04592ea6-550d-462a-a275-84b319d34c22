package order

import (
	"net/http"

	"engine/api/internal/logic/mini/order"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetOrderDetailPathHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := order.NewGetOrderDetailPathLogic(r.Context(), svcCtx)
		resp, err := l.GetOrderDetailPath()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
