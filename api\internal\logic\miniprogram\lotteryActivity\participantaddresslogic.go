package lotteryActivity

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantAddressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantAddressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantAddressLogic {
	return &ParticipantAddressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantAddressLogic) ParticipantAddress(req *types.ActiveLotteryActivityParticipantAddressReq) (resp *types.ActiveLotteryActivityParticipantAddressResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	participant, err := l.svcCtx.LotteryParticipantModel.FindOneByQuery(l.ctx, l.svcCtx.LotteryParticipantModel.RowBuilder().Where(squirrel.Eq{"activity_id": req.Id, "uid": uid}))
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("ParticipantAddressLogic.LotteryParticipantModel.FindOneByQuery err: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if errors.Is(err, model.ErrNotFound) {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)
	}

	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, cast.ToInt64(participant.AddressId))
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf("ParticipantAddressLogic.UserAddressModel.FindOne err: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if errors.Is(err, model.ErrNotFound) {
		return nil, xerr.NewErrCode(xerr.DataNoExistError)

	}

	return &types.ActiveLotteryActivityParticipantAddressResp{
		UserAddressInfo: types.UserAddressInfo{
			Id:             address.Id,
			Uid:            address.Uid,
			ProvinceId:     address.ProvinceId,
			CityId:         address.CityId,
			TownId:         address.TownId,
			Address:        address.Address,
			IsDefault:      address.IsDefault,
			Label:          address.Label,
			Code:           address.Code,
			Consignee:      address.Consignee,
			ConsigneePhone: address.ConsigneePhone,
			ProvinceName:   address.ProvinceName,
			CityName:       address.CityName,
			TownName:       address.TownName,
			CreatedTime:    common.TimeToString(address.CreatedTime),
			UpdateTime:     common.TimeToString(address.UpdateTime),
		},
	}, nil
}
