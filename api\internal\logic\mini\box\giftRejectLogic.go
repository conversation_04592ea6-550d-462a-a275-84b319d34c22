package box

import (
	"context"
	"database/sql"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftRejectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftRejectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftRejectLogic {
	return &GiftRejectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftRejectLogic) GiftReject(req *types.BoxGiftRejectReq) error {
	// 获取用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		l.Logger.Error("uid not found in context")
		return xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询转赠分享记录
	giftShare, err := l.svcCtx.BoxGiftShareModel.FindOne(l.ctx, req.ShareId)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrMsg("转赠记录不存在")
		}
		l.Logger.Errorf("查询转赠记录失败: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 验证不能拒绝自己的转赠
	if giftShare.SenderUid == uid {
		return xerr.NewErrMsg("不能拒绝自己的转赠")
	}

	// 验证转赠状态
	if giftShare.Status != 1 {
		return xerr.NewErrMsg("转赠已失效")
	}

	// 验证是否过期
	if time.Now().After(giftShare.ExpireTime) {
		return xerr.NewErrMsg("转赠已过期")
	}

	// 开启事务
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		now := time.Now()

		// 1. 更新转赠记录状态为已拒绝
		updateGiftQuery := `UPDATE vh_box_gift_share SET status = 3, reject_time = ?, update_time = ? WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateGiftQuery, now, now, req.ShareId)
		if err != nil {
			l.Logger.Errorf("更新转赠记录失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 2. 恢复盲盒状态为待领取
		updateBoxQuery := `UPDATE vh_order_main_box SET box_type = 1 WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateBoxQuery, giftShare.BoxMainOrderId)
		if err != nil {
			l.Logger.Errorf("恢复盲盒状态失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
