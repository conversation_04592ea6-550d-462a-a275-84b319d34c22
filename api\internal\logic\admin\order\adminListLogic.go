package order

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AdminList 后台订单列表
func (l *AdminListLogic) AdminList(req *types.AdminOrderListReq) (resp *types.AdminOrderListResp, err error) {
	// 构建基础查询
	builder := squirrel.Select(
		// 子订单字段
		"o.id", "o.sub_order_no", "o.sub_order_status", "o.goods_id", "o.order_qty",
		"o.payment_amount", "o.cash_amount", "o.erp_amount", "o.deductible_amount",
		"o.refund_money", "o.refund_status", "o.express_type", "o.express_number",
		"o.payment_time", "o.delivery_time", "o.cancel_time", "o.created_time",
		"o.consignee", "o.consignee_phone", "o.province", "o.city", "o.district", "o.address",
		"o.push_t_status", "o.push_wms_status", "o.push_zt_status", "o.remarks",
		"o.warehouse_code", "o.snapshot as order_snapshot", "o.type",
		// 主订单字段
		"m.main_order_no", "m.main_order_status", "m.snapshot as main_snapshot", "m.payment_method", "m.tradeno",
	).From("vh_order o").
		LeftJoin("vh_order_main m ON o.main_order_id = m.id").
		OrderBy("o.created_time DESC")

	// 添加筛选条件
	builder = l.addFilters(builder, req)

	// 计算总数
	countBuilder := squirrel.Select("COUNT(*)").From("vh_order o").
		LeftJoin("vh_order_main m ON o.main_order_id = m.id")
	countBuilder = l.addFilters(countBuilder, req)

	total, err := l.svcCtx.OrderModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminList count error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	builder = builder.Limit(uint64(req.Limit)).Offset(uint64(offset))

	// 执行查询
	var orders []OrderQueryResult
	err = l.svcCtx.OrderModel.FindRows(l.ctx, builder, &orders)
	if err != nil {
		l.Logger.Error("AdminList query error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据格式
	list := make([]types.AdminOrderItem, 0, len(orders))
	for _, order := range orders {
		item := l.convertToAdminOrderItem(order)
		list = append(list, item)
	}

	return &types.AdminOrderListResp{
		List:  list,
		Total: total,
	}, nil
}

// addFilters 添加筛选条件
func (l *AdminListLogic) addFilters(builder squirrel.SelectBuilder, req *types.AdminOrderListReq) squirrel.SelectBuilder {
	// 订单号筛选（可同时搜索子订单号和主订单号）
	if req.SubOrderNo != "" {
		builder = builder.Where(squirrel.Or{
			squirrel.Like{"o.sub_order_no": "%" + req.SubOrderNo + "%"},
			squirrel.Like{"m.main_order_no": "%" + req.SubOrderNo + "%"},
		})
	}

	// 商品名称筛选（盲盒订单从主订单快照，非盲盒订单从子订单快照）
	if req.GoodsName != "" {
		builder = builder.Where(squirrel.Or{
			// 盲盒订单：从主订单快照筛选
			squirrel.And{
				squirrel.Eq{"o.type": 3},
				squirrel.Like{"m.snapshot": "%" + req.GoodsName + "%"},
			},
			// 非盲盒订单：从子订单快照筛选
			squirrel.And{
				squirrel.NotEq{"o.type": 3},
				squirrel.Like{"o.snapshot": "%" + req.GoodsName + "%"},
			},
		})
	}

	// 收货人姓名筛选
	if req.ConsigneeName != "" {
		builder = builder.Where(squirrel.Like{"o.consignee": "%" + req.ConsigneeName + "%"})
	}

	// 收货人手机筛选
	if req.ConsigneePhone != "" {
		builder = builder.Where(squirrel.Like{"o.consignee_phone": "%" + req.ConsigneePhone + "%"})
	}

	// 子订单状态筛选
	if req.SubOrderStatus > 0 {
		builder = builder.Where(squirrel.Eq{"o.sub_order_status": req.SubOrderStatus})
	}

	// 主订单状态筛选
	if req.MainOrderStatus > 0 {
		builder = builder.Where(squirrel.Eq{"m.main_order_status": req.MainOrderStatus})
	}

	// ERP推送状态筛选
	if req.ErpPushStatus > 0 {
		builder = builder.Where(squirrel.Eq{"o.push_t_status": req.ErpPushStatus})
	}

	// 推送中台状态筛选
	if req.MiddlePushStatus > 0 {
		builder = builder.Where(squirrel.Eq{"o.push_zt_status": req.MiddlePushStatus})
	}

	// 中台推送状态筛选
	if req.PushZtStatus > 0 {
		builder = builder.Where(squirrel.Eq{"o.push_zt_status": req.PushZtStatus})
	}

	// T+推送状态筛选
	if req.PushTStatus > 0 {
		builder = builder.Where(squirrel.Eq{"o.push_t_status": req.PushTStatus})
	}

	// 商品类型筛选
	if req.GoodsType > 0 {
		builder = builder.Where(squirrel.Eq{"o.type": req.GoodsType})
	}

	// 盲盒ID筛选（从主订单快照中查询）
	if req.BoxId > 0 {
		builder = builder.Where(squirrel.Like{"m.snapshot": fmt.Sprintf(`%%"box_id":%d%%`, req.BoxId)})
	}

	// 支付时间范围筛选
	if req.PaymentTimeStart != "" {
		startTime, err := time.Parse("2006-01-02", req.PaymentTimeStart)
		if err == nil {
			builder = builder.Where(squirrel.GtOrEq{"o.payment_time": startTime.Unix()})
		}
	}

	if req.PaymentTimeEnd != "" {
		endTime, err := time.Parse("2006-01-02", req.PaymentTimeEnd)
		if err == nil {
			// 结束时间设为当天23:59:59
			endTime = endTime.Add(24*time.Hour - time.Second)
			builder = builder.Where(squirrel.LtOrEq{"o.payment_time": endTime.Unix()})
		}
	}

	return builder
}

// convertToAdminOrderItem 转换为管理后台订单项
func (l *AdminListLogic) convertToAdminOrderItem(order OrderQueryResult) types.AdminOrderItem {
	// 获取商品名称（根据订单类型从不同快照获取）
	goodsName := l.getGoodsNameFromSnapshot(order.Type, order.OrderSnapshot, order.MainSnapshot)

	item := types.AdminOrderItem{
		Id:               order.Id,
		SubOrderNo:       order.SubOrderNo,
		MainOrderNo:      l.getNullString(order.MainOrderNo),
		SubOrderStatus:   order.SubOrderStatus,
		MainOrderStatus:  l.getNullInt64(order.MainOrderStatus),
		GoodsId:          order.GoodsId,
		GoodsName:        goodsName,
		GoodsType:        order.Type,
		OrderQty:         order.OrderQty,
		PaymentAmount:    order.PaymentAmount,
		CashAmount:       order.CashAmount,
		DeductibleAmount: order.DeductibleAmount,
		ErpAmount:        order.ErpAmount,
		RefundAmount:     order.RefundMoney,
		RefundStatus:     l.getNullInt64(order.RefundStatus),
		CreatedTime:      function.FormatTime(order.CreatedTime),
		PaymentTime:      function.FormatTimestamp(order.PaymentTime),
		DeliveryTime:     l.formatNullTimestamp(order.DeliveryTime),
		CancelTime:       l.formatNullTimestamp(order.CancelTime),
		ConsigneeName:     l.getNullString(order.Consignee),
		ConsigneePhone:    l.getNullString(order.ConsigneePhone),
		ConsigneeNameMask: l.maskName(l.getNullString(order.Consignee)),
		ConsigneePhoneMask: l.maskPhone(l.getNullString(order.ConsigneePhone)),
		Province:         l.getNullString(order.Province),
		City:             l.getNullString(order.City),
		District:         l.getNullString(order.District),
		Address:          l.getNullString(order.Address),
		ExpressType:      order.ExpressType,
		ExpressNumber:    l.getNullString(order.ExpressNumber),
		PushTStatus:         order.PushTStatus,
		PushWmsStatus:       order.PushWmsStatus,
		PushZtStatus:        order.PushZtStatus,
		PaymentMethod:       l.getNullInt64(order.PaymentMethod),
		Tradeno:             l.getNullString(order.Tradeno),
		Remarks:             l.getNullString(order.Remarks),
		WarehouseCode:       l.getNullString(order.WarehouseCode),
	}

	return item
}



// getSubOrderStatusText 获取子订单状态文本
func (l *AdminListLogic) getSubOrderStatusText(status int64) string {
	switch status {
	case 0:
		return "待支付"
	case 1:
		return "已支付"
	case 2:
		return "已发货"
	case 3:
		return "已完成"
	case 4:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getMainOrderStatusText 获取主订单状态文本
func (l *AdminListLogic) getMainOrderStatusText(status int64) string {
	switch status {
	case 0:
		return "待支付"
	case 1:
		return "已支付"
	case 2:
		return "已发货"
	case 3:
		return "已完成"
	case 4:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getGoodsTypeText 获取商品类型文本
func (l *AdminListLogic) getGoodsTypeText(goodsType int64) string {
	switch goodsType {
	case 1:
		return "普通商品"
	case 2:
		return "抽奖商品"
	case 3:
		return "盲盒商品"
	default:
		return "未知类型"
	}
}

// getNullString 获取NullString的值
func (l *AdminListLogic) getNullString(ns sql.NullString) string {
	if ns.Valid {
		return ns.String
	}
	return ""
}

// getNullInt64 获取NullInt64的值
func (l *AdminListLogic) getNullInt64(ni sql.NullInt64) int64 {
	if ni.Valid {
		return ni.Int64
	}
	return 0
}

// formatNullTimestamp 格式化可空的时间戳
func (l *AdminListLogic) formatNullTimestamp(timestamp sql.NullInt64) string {
	if timestamp.Valid && timestamp.Int64 > 0 {
		return function.FormatTimestamp(timestamp.Int64)
	}
	return ""
}

// OrderQueryResult 订单查询结果结构体
type OrderQueryResult struct {
	// 子订单字段
	Id               int64                  `db:"id"`
	SubOrderNo       string                 `db:"sub_order_no"`
	SubOrderStatus   int64                  `db:"sub_order_status"`
	GoodsId          int64                  `db:"goods_id"`
	OrderQty         int64                  `db:"order_qty"`
	PaymentAmount    float64                `db:"payment_amount"`
	CashAmount       float64                `db:"cash_amount"`
	ErpAmount        float64                `db:"erp_amount"`
	DeductibleAmount float64                `db:"deductible_amount"`
	RefundMoney      float64                `db:"refund_money"`
	RefundStatus     sql.NullInt64          `db:"refund_status"`
	ExpressType      int64                  `db:"express_type"`
	ExpressNumber    sql.NullString         `db:"express_number"`
	PaymentTime      int64                  `db:"payment_time"`
	DeliveryTime     sql.NullInt64          `db:"delivery_time"`
	CancelTime       sql.NullInt64          `db:"cancel_time"`
	CreatedTime      time.Time              `db:"created_time"`
	Consignee        sql.NullString         `db:"consignee"`
	ConsigneePhone   sql.NullString         `db:"consignee_phone"`
	Province         sql.NullString         `db:"province"`
	City             sql.NullString         `db:"city"`
	District         sql.NullString         `db:"district"`
	Address          sql.NullString         `db:"address"`
	PushTStatus      int64                  `db:"push_t_status"`
	PushWmsStatus    int64                  `db:"push_wms_status"`
	PushZtStatus     int64                  `db:"push_zt_status"`
	Remarks          sql.NullString         `db:"remarks"`
	WarehouseCode    sql.NullString         `db:"warehouse_code"`
	OrderSnapshot    sql.NullString         `db:"order_snapshot"`
	Type             int64                  `db:"type"`
	// 主订单字段
	MainOrderNo      sql.NullString         `db:"main_order_no"`
	MainOrderStatus  sql.NullInt64          `db:"main_order_status"`
	MainSnapshot     sql.NullString         `db:"main_snapshot"`
	PaymentMethod    sql.NullInt64          `db:"payment_method"`
	Tradeno          sql.NullString         `db:"tradeno"`
}

// getGoodsNameFromSnapshot 根据订单类型从快照获取商品名称
func (l *AdminListLogic) getGoodsNameFromSnapshot(orderType int64, subSnapshot, mainSnapshot sql.NullString) string {
	var snapshotData map[string]interface{}
	var err error

	// 根据订单类型选择快照来源
	if orderType == 3 { // 盲盒订单从主订单快照获取
		if mainSnapshot.Valid && mainSnapshot.String != "" {
			err = json.Unmarshal([]byte(mainSnapshot.String), &snapshotData)
		}
	} else { // 普通商品和抽奖商品从子订单快照获取
		if subSnapshot.Valid && subSnapshot.String != "" {
			err = json.Unmarshal([]byte(subSnapshot.String), &snapshotData)
		}
	}

	// 解析失败时返回默认值
	if err != nil {
		return "商品名称"
	}

	// 提取商品名称
	if title, ok := snapshotData["title"].(string); ok {
		return title
	}

	return "商品名称"
}

// maskName 脱敏姓名
func (l *AdminListLogic) maskName(name string) string {
	if name == "" {
		return ""
	}

	runes := []rune(name)
	if len(runes) <= 1 {
		return name
	}

	// 保留第一个字符，其他用*替换
	result := string(runes[0])
	for i := 1; i < len(runes); i++ {
		result += "*"
	}
	return result
}

// maskPhone 脱敏手机号
func (l *AdminListLogic) maskPhone(phone string) string {
	if phone == "" {
		return ""
	}

	if len(phone) != 11 {
		return phone
	}

	// 保留前3位和后4位，中间4位用*替换
	return phone[:3] + "****" + phone[7:]
}
