package label

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDeleteLogic {
	return &AdminDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDeleteLogic) AdminDelete(req *types.LabelDeleteReq) error {
	// 检查记录是否存在
	_, err := l.svcCtx.LabelModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDeleteLogic LabelModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 删除记录
	err = l.svcCtx.LabelModel.Delete(l.ctx, req.Id)
	if err != nil {
		l.Logger.Error("AdminDeleteLogic LabelModel.Delete error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
