package goods

import (
	"net/http"

	"engine/api/internal/logic/mini/goods"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func MiniListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MiniGoodsListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := goods.NewMiniListLogic(r.Context(), svcCtx)
		resp, err := l.MiniList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
