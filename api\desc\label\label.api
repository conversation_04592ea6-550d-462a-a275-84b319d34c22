syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    // Label信息
    LabelInfo {
        Id int64 `json:"id"`
        Name string `json:"name"`
        Sort int64 `json:"sort"`
        Status int64 `json:"status"`
        CreatedTime string `json:"created_time"`
        UpdateTime string `json:"update_time"`
    }

    // 后台Label列表请求
    AdminLabelListReq {
        Paging
        Status int64 `form:"status,optional"` // 状态筛选，可选
    }

    // 后台Label列表响应
    AdminLabelListResp {
        List []LabelInfo `json:"list"`
        Total int64 `json:"total"`
    }

    // Label创建请求
    LabelCreateReq {
        Name string `json:"name" validate:"required" v:"分类名称"`
    }

    // Label创建响应
    LabelCreateResp {
        Id int64 `json:"id"`
        Name string `json:"name"`
        Status int64 `json:"status"`
    }

    // 简单标签列表请求（只返回启用的标签）
    SimpleLabelListReq {
    }

    // 简单标签列表响应
    SimpleLabelListResp {
        List []SimpleLabelInfo `json:"list"`
    }

    // 简单标签信息
    SimpleLabelInfo {
        Id int64 `json:"id"`
        Name string `json:"name"`
    }

    // Label更新请求
    LabelUpdateReq {
        IdJ
        Name string `json:"name" validate:"required" v:"分类名称"`
        Sort int64 `json:"sort" validate:"min=0" v:"排序"`
        Status int64 `json:"status" validate:"min=0,max=1" v:"状态"`
    }

    // Label删除请求
    LabelDeleteReq {
        IdJ
    }

    // Label详情请求
    LabelDetailReq {
        IdF
    }
)

// 后台Label管理接口
@server(
    middleware: Global,Admin
    group: admin/label
    prefix: /mulandoGreateDestiny/v1/admin/label
    timeout: 3s
)
service mulandoGreateDestiny {
    // Label列表
    @handler AdminList
    get /list (AdminLabelListReq) returns (AdminLabelListResp)

    // Label详情
    @handler AdminDetail
    get /detail (LabelDetailReq) returns (LabelInfo)

    // 创建Label
    @handler AdminCreate
    post /create (LabelCreateReq) returns (LabelCreateResp)

    // 更新Label
    @handler AdminUpdate
    post /update (LabelUpdateReq)

    // 删除Label
    @handler AdminDelete
    post /delete (LabelDeleteReq)

    // 简单标签列表（只返回启用的标签）
    @handler SimpleList
    get /simple_list (SimpleLabelListReq) returns (SimpleLabelListResp)
}
