package winenotes

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProcessGoodsWineNotesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 处理商品的WineNotes数据
func NewProcessGoodsWineNotesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessGoodsWineNotesLogic {
	return &ProcessGoodsWineNotesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ProcessGoodsWineNotesLogic) ProcessGoodsWineNotes(req *types.GoodsWineNotesReq) (resp *types.GoodsWineNotesResp, err error) {
	// todo: add your logic here and delete this line

	return
}
