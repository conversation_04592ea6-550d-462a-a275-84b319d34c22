package label

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminLabelListReq) (resp *types.AdminLabelListResp, err error) {
	var labels []*model.Label
	
	// 构建查询条件
	builder := l.svcCtx.LabelModel.RowBuilder().OrderBy("sort DESC, id DESC")
	
	// 状态筛选
	if req.Status != 0 {
		builder = builder.Where(squirrel.Eq{"status": req.Status})
	}
	
	// 分页
	offset := (req.Page - 1) * req.Limit
	builder = builder.Limit(uint64(req.Limit)).Offset(uint64(offset))
	
	// 查询数据
	err = l.svcCtx.LabelModel.FindRows(l.ctx, builder, &labels)
	if err != nil {
		l.Logger.Error("AdminListLogic LabelModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	
	// 查询总数
	countBuilder := squirrel.Select("COUNT(*)").From(l.svcCtx.LabelModel.TableName())
	if req.Status != 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"status": req.Status})
	}
	
	total, err := l.svcCtx.LabelModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminListLogic LabelModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	
	// 构建响应
	resp = &types.AdminLabelListResp{
		List:  make([]types.LabelInfo, 0),
		Total: total,
	}
	
	for _, label := range labels {
		resp.List = append(resp.List, types.LabelInfo{
			Id:          label.Id,
			Name:        label.Name,
			Sort:        label.Sort,
			Status:      label.Status,
			CreatedTime: function.FormatTime(label.CreatedTime),
			UpdateTime:  function.FormatTime(label.UpdateTime),
		})
	}
	
	return
}
