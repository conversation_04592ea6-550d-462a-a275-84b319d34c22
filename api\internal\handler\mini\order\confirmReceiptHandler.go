package order

import (
	"net/http"

	"engine/api/internal/logic/mini/order"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ConfirmReceiptHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ConfirmReceiptReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := order.NewConfirmReceiptLogic(r.Context(), svcCtx)
		resp, err := l.ConfirmReceipt(&req)
		result.HttpResult(r, w, resp, err)
	}
}
