package order

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/wechat"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderDetailPathLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrderDetailPathLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderDetailPathLogic {
	return &GetOrderDetailPathLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrderDetailPathLogic) GetOrderDetailPath() (resp *types.GetOrderDetailPathResp, err error) {
	// 获取access_token
	accessToken, err := l.getAccessToken()
	if err != nil {
		l.Logger.Errorf("获取access_token失败: %v", err)
		return &types.GetOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
			Path:      "",
		}, nil
	}

	// 调用微信API
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/sec/order/get_order_detail_path?access_token=%s", accessToken)

	httpResp, err := http.Post(url, "application/json", nil)
	if err != nil {
		l.Logger.Errorf("调用微信API失败: %v", err)
		return &types.GetOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
			Path:      "",
		}, nil
	}
	defer httpResp.Body.Close()

	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		l.Logger.Errorf("读取微信API响应失败: %v", err)
		return &types.GetOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
			Path:      "",
		}, nil
	}

	var wechatResp types.GetOrderDetailPathResp
	if err := json.Unmarshal(respBody, &wechatResp); err != nil {
		l.Logger.Errorf("解析微信API响应失败: %v, body: %s", err, string(respBody))
		return &types.GetOrderDetailPathResp{
			ErrorCode: 10060012,
			ErrorMsg:  "系统繁忙，此时请开发者稍候再试",
			Path:      "",
		}, nil
	}

	l.Logger.Infof("查询订单详情路径结果: %+v", wechatResp)
	return &wechatResp, nil
}

// getAccessToken 获取微信access_token
func (l *GetOrderDetailPathLogic) getAccessToken() (string, error) {
	// 使用系统统一的微信服务获取AccessToken
	weChatService := wechat.NewWeChatService(l.svcCtx.Config)

	res, err := weChatService.GetAccessToken()
	if err != nil {
		l.Logger.Errorf("GetAccessToken failed: %v", err)
		return "", xerr.NewErrMsg("获取access_token失败: " + err.Error())
	}

	if res.AccessToken == "" {
		l.Logger.Errorf("AccessToken is empty: code=%d, msg=%s", res.Code, res.Msg)
		return "", xerr.NewErrMsg("access_token为空: " + res.Msg)
	}

	return res.AccessToken, nil
}
