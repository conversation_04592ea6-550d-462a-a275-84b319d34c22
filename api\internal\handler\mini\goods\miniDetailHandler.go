package goods

import (
	"net/http"

	"engine/api/internal/logic/mini/goods"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func MiniDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MiniGoodsDetailReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := goods.NewMiniDetailLogic(r.Context(), svcCtx)
		resp, err := l.MiniDetail(&req)
		result.HttpResult(r, w, resp, err)
	}
}
