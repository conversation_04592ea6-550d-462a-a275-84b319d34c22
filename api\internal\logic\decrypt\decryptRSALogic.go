package decrypt

import (
	"context"
	"engine/common/xerr"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptRSALogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptRSALogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptRSALogic {
	return &DecryptRSALogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptRSALogic) DecryptRSA(req *types.RSADecryptReq) (resp *types.RSADecryptResp, err error) {
	// 创建解密服务
	decryptService := service.NewDecryptService(l.svcCtx.Config)

	// 调用RSA解密服务
	decryptedData, err := decryptService.DecryptRSA(req.EncryptedData)
	if err != nil {
		l.Logger.<PERSON>rrorf("DecryptRSA error: %v", err)
		return nil, xerr.NewErrMsg("解密失败")
	}

	return &types.RSADecryptResp{
		DecryptedData: decryptedData,
	}, nil
}
