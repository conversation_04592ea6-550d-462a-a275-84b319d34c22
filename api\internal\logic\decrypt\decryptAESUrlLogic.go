package decrypt

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type DecryptAESUrlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDecryptAESUrlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DecryptAESUrlLogic {
	return &DecryptAESUrlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DecryptAESUrlLogic) DecryptAESUrl(req *types.AESDecryptUrlReq) (resp *types.AESDecryptUrlResp, err error) {
	// 创建解密服务
	decryptService := service.NewDecryptService(l.svcCtx.Config)

	l.Logger.Infof("AES URL 接收到的请求数据: %s", req.Q)

	// 1. 调用AES解密服务获取短链接
	shortUrl, err := decryptService.DecryptAES(req.Q)
	if err != nil {
		// 记录详细错误信息到日志，但不暴露给用户
		l.Logger.Errorf("AES解密失败 - 加密数据: %s, 错误详情: %v", req.Q, err)
		return nil, xerr.NewErrMsg("操作失败")
	}

	l.Logger.Infof("AES解密成功，获得短链接: %s", shortUrl)

	// 2. 访问短链接获取长链接
	finalUrl, err := l.getFinalUrl(shortUrl)
	if err != nil {
		l.Logger.Errorf("获取最终链接失败 - 短链接: %s, 错误详情: %v", shortUrl, err)
		return nil, xerr.NewErrMsg("操作失败")
	}

	l.Logger.Infof("获取最终链接成功: %s", finalUrl)

	return &types.AESDecryptUrlResp{
		Url: finalUrl,
	}, nil
}

// getFinalUrl 访问短链接获取最终的长链接
func (l *DecryptAESUrlLogic) getFinalUrl(shortUrl string) (string, error) {
	// 创建HTTP客户端，不自动跟随重定向
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 不跟随重定向，我们要手动处理
			return http.ErrUseLastResponse
		},
	}

	// 创建请求
	req, err := http.NewRequest("GET", shortUrl, nil)
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查是否是重定向响应
	if resp.StatusCode >= 300 && resp.StatusCode < 400 {
		// 获取重定向的目标URL
		location := resp.Header.Get("Location")
		if location == "" {
			return "", fmt.Errorf("重定向响应中没有Location头")
		}
		l.Logger.Infof("短链接重定向到: %s", location)
		return location, nil
	}

	// 如果不是重定向，读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	l.Logger.Infof("短链接响应状态码: %d, 响应内容: %s", resp.StatusCode, string(body))

	// 如果状态码是200，可能短链接本身就是最终链接
	if resp.StatusCode == 200 {
		return shortUrl, nil
	}

	return "", fmt.Errorf("无法获取最终链接，状态码: %d", resp.StatusCode)
}
