package lotteryActivity

import (
	"engine/api/internal/logic/admin/lotteryActivity"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func UpdateHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LotteryActivityUpdateReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := lotteryActivity.NewUpdateLogic(r.Context(), svcCtx)
		err := l.Update(&req)
		result.HttpResult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
