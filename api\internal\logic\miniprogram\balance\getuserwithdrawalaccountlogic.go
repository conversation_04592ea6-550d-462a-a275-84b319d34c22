package balance

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserWithdrawalAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserWithdrawalAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserWithdrawalAccountLogic {
	return &GetUserWithdrawalAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserWithdrawalAccountLogic) GetUserWithdrawalAccount() (resp *types.GetUserWithdrawalAccountResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	info, er := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if er != nil && !errors.Is(er, model.ErrNotFound) {
		l.<PERSON>("GetUserWithdrawalAccountLogic UserModel.FindOne error: %v", er)
		return nil, xerr.NewErrCode(xerr.DbError)
	}
	if errors.Is(er, model.ErrNotFound) {
		return nil, xerr.NewErrCode(xerr.UserNotExist)
	}

	return &types.GetUserWithdrawalAccountResp{
		WithdrawalAccount: types.WithdrawalAccount{
			AliName:    info.AliName,
			AliAccount: info.AliAccount,
		},
	}, nil

}
