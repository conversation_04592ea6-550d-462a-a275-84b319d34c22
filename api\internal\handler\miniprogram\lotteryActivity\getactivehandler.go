package lotteryActivity

import (
	"engine/api/internal/logic/miniprogram/lotteryActivity"
	"engine/api/internal/svc"
	"engine/common/result"
	"net/http"
)

func GetActiveHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := lotteryActivity.NewGetActiveLogic(r.Context(), svcCtx)
		resp, err := l.GetActive()
		result.HttpResult(r, w, resp, err)
	}
}
