package box

import (
	"context"
	"database/sql"
	"encoding/json"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCreateLogic {
	return &AdminCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCreateLogic) AdminCreate(req *types.BoxCreateReq) error {
	// 获取管理员信息
	adminUid := l.ctx.Value("admin_uid").(int64)
	adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 开启事务
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 创建盲盒 - 不设置ID、CreatedTime、UpdateTime，由数据库自动维护
		box := &model.VhBox{
			Title:         req.Title,
			AvatarImage:   req.AvatarImage,
			ValidTimeUnit: req.ValidTimeUnit,
			ValidTimeNum:  uint64(req.ValidTimeNum),
			Price:         req.Price,
			OnsaleStatus:  req.OnsaleStatus,
			DeleteTime:    0,
			VhUid:         adminUid,
			VhVosName:     adminVosName,
			CreateNum:     0,
			ActiveNum:     0,
			GetNum:        0,
		}

		// 使用原生SQL插入，排除自动维护的字段
		query := `INSERT INTO vh_box (title, avatar_image, valid_time_unit, valid_time_num, price, onsale_status, delete_time, vh_uid, vh_vos_name, create_num, active_num, get_num) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
		result, err := tx.ExecContext(ctx, query,
			box.Title, box.AvatarImage, box.ValidTimeUnit, box.ValidTimeNum,
			box.Price, box.OnsaleStatus, box.DeleteTime, box.VhUid, box.VhVosName,
			box.CreateNum, box.ActiveNum, box.GetNum)
		if err != nil {
			l.Logger.Error("AdminCreate tx.ExecContext error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 获取插入的盲盒ID
		boxId, _ := result.LastInsertId()

		// 根据周期创建对应数量的vh_box_items数据
		// 这里创建空的items，后续通过绑定商品接口来填充
		for i := 1; i <= int(req.ValidTimeNum); i++ {
			// 创建空的商品详情JSON
			emptyItems := []types.BoxItemInfo{}
			itemsJson, _ := json.Marshal(emptyItems)

			// 使用原生SQL插入vh_box_items
			itemQuery := `INSERT INTO vh_box_items (box_id, type, items_info) VALUES (?, ?, ?)`
			_, err = tx.ExecContext(ctx, itemQuery, boxId, 1, string(itemsJson))
			if err != nil {
				l.Logger.Error("AdminCreate vh_box_items insert error: %v", err)
				return xerr.NewErrCode(xerr.DbError)
			}
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
