package user_level

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminUserLevelListReq) (resp *types.AdminUserLevelListResp, err error) {
	// 构建查询条件
	builder := l.svcCtx.UserLevelModel.RowBuilder()
	
	// 添加筛选条件
	if req.Name != "" {
		builder = builder.Where(squirrel.Like{"name": "%" + req.Name + "%"})
	}
	if req.Level > 0 {
		builder = builder.Where(squirrel.Eq{"level": req.Level})
	}

	// 获取总数
	countBuilder := model.CountBuilder("id", l.svcCtx.UserLevelModel.TableName())
	if req.Name != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"name": "%" + req.Name + "%"})
	}
	if req.Level > 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"level": req.Level})
	}

	total, err := l.svcCtx.UserLevelModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminList UserLevelModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 分页查询 - 按等级升序，ID降序排列
	builder = builder.OrderBy("level ASC, id DESC").
		Limit(uint64(req.Limit)).
		Offset(model.GetOffset(req.Page, req.Limit))

	var userLevels []*model.UserLevel
	err = l.svcCtx.UserLevelModel.FindRows(l.ctx, builder, &userLevels)
	if err != nil {
		l.Logger.Error("AdminList UserLevelModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据并获取关联的任务
	list := make([]types.UserLevelInfo, 0, len(userLevels))
	for _, userLevel := range userLevels {
		// 查询该等级的任务 - 按类型升序，ID升序排列
		taskBuilder := l.svcCtx.UserLevelTaskModel.RowBuilder().
			Where(squirrel.Eq{"level": userLevel.Level}).
			OrderBy("type ASC, id ASC")

		var tasks []*model.UserLevelTask
		err = l.svcCtx.UserLevelTaskModel.FindRows(l.ctx, taskBuilder, &tasks)
		if err != nil {
			l.Logger.Error("AdminList UserLevelTaskModel.FindRows error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		// 转换任务数据
		taskInfos := make([]types.UserLevelTaskInfo, 0, len(tasks))
		for _, task := range tasks {
			taskInfo := types.UserLevelTaskInfo{
				Id:    task.Id,
				Level: task.Level,
				Name:  task.Name,
				Type:  task.Type,
				Num:   task.Num,
			}
			taskInfos = append(taskInfos, taskInfo)
		}

		userLevelInfo := types.UserLevelInfo{
			Id:               userLevel.Id,
			Level:            userLevel.Level,
			Name:             userLevel.Name,
			LevelName:        userLevel.LevelName,
			ShareBrokerage:   userLevel.ShareBrokerage,
			PaymentBrokerage: userLevel.PaymentBrokerage,
			CashBrokerage:    userLevel.CashBrokerage,
			CashMinAmount:    userLevel.CashMinAmount,
			Duration:         userLevel.Duration,
			CreatedTime:      common.TimeToString(userLevel.CreatedTime),
			UpdateTime:       common.TimeToString(userLevel.UpdateTime),
			Tasks:            taskInfos,
		}
		list = append(list, userLevelInfo)
	}

	return &types.AdminUserLevelListResp{
		List:  list,
		Total: total,
	}, nil
}
