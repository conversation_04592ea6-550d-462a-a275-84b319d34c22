package share

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/share"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func RefineTitleHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RefineTitleReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := share.NewRefineTitleLogic(r.Context(), svcCtx)
		resp, err := l.RefineTitle(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
