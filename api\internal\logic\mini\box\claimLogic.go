package box

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"engine/api/internal/service"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type ClaimLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClaimLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClaimLogic {
	return &ClaimLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClaimLogic) Claim(req *types.BoxClaimReq) (resp *types.BoxClaimResp, err error) {
	// 获取用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		l.Logger.Error("uid not found in context")
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 生成子订单号（在事务外生成，避免重复生成）
	subOrderNo := l.generateSubOrderNo()

	// 开始事务处理
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		return l.processClaimTransaction(ctx, tx, uid, req, subOrderNo)
	})

	if err != nil {
		l.Logger.Errorf("Transaction error: %v", err)
		return nil, err // 直接返回错误，保持错误码
	}

	// 盲盒领取成功后，推送订单到中台
	err = l.pushBoxOrderToMiddlePlatform(req, subOrderNo)
	if err != nil {
		l.Logger.Errorf("pushBoxOrderToMiddlePlatform error: %v", err)
		// 推送失败不影响领取成功，只记录错误日志
	}

	// 检查并更新主订单状态
	err = l.checkAndUpdateMainOrderStatus(req.Id)
	if err != nil {
		l.Logger.Errorf("checkAndUpdateMainOrderStatus error: %v", err)
		// 状态更新失败不影响领取成功，只记录错误日志
	}

	return &types.BoxClaimResp{
		SubOrderNo: subOrderNo,
		Message:    "领取成功",
	}, nil
}

// processClaimTransaction 处理领取事务
func (l *ClaimLogic) processClaimTransaction(ctx context.Context, tx *sql.Tx, uid int64, req *types.BoxClaimReq, subOrderNo string) error {
	// 1. 查询盲盒订单详情
	l.Logger.Infof("查询盲盒订单详情: id=%d, uid=%d", req.Id, uid)
	orderMainBox, err := l.svcCtx.OrderMainBoxModel.FindOne(ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			l.Logger.Errorf("盲盒订单不存在: id=%d", req.Id)
			return xerr.NewErrMsg("盲盒订单不存在")
		}
		l.Logger.Errorf("OrderMainBoxModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 2. 验证订单归属
	l.Logger.Infof("验证订单归属: order_uid=%d, current_uid=%d", orderMainBox.Uid, uid)
	if orderMainBox.Uid != uid {
		l.Logger.Errorf("订单不属于当前用户: order_uid=%d, current_uid=%d", orderMainBox.Uid, uid)
		return xerr.NewErrMsg("无权限操作此订单")
	}

	// 3. 验证领取状态
	l.Logger.Infof("验证领取状态: box_type_valid=%t, box_type=%d", orderMainBox.BoxType.Valid, orderMainBox.BoxType.Int64)
	if orderMainBox.BoxType.Valid && orderMainBox.BoxType.Int64 != 1 {
		var statusMsg string
		switch orderMainBox.BoxType.Int64 {
		case 2:
			statusMsg = "该盲盒已被领取"
		case 3:
			statusMsg = "该盲盒已被转赠"
		case 4:
			statusMsg = "该盲盒正在转赠中"
		default:
			statusMsg = "该盲盒状态异常"
		}
		l.Logger.Errorf("盲盒状态不允许领取: box_type=%d", orderMainBox.BoxType.Int64)
		return xerr.NewErrMsg(statusMsg)
	}

	// 4. 验证领取时间
	now := time.Now()
	l.Logger.Infof("验证领取时间: now=%s, begin_time_valid=%t, end_time_valid=%t",
		now.Format("2006-01-02 15:04:05"), orderMainBox.BeginTime.Valid, orderMainBox.EndTime.Valid)
	if orderMainBox.BeginTime.Valid && now.Before(orderMainBox.BeginTime.Time) {
		l.Logger.Errorf("还未到领取时间: now=%s, begin_time=%s",
			now.Format("2006-01-02 15:04:05"), orderMainBox.BeginTime.Time.Format("2006-01-02 15:04:05"))
		return xerr.NewErrMsg("还未到领取时间")
	}
	if orderMainBox.EndTime.Valid && now.After(orderMainBox.EndTime.Time) {
		l.Logger.Errorf("领取时间已过期: now=%s, end_time=%s",
			now.Format("2006-01-02 15:04:05"), orderMainBox.EndTime.Time.Format("2006-01-02 15:04:05"))
		return xerr.NewErrMsg("领取时间已过期")
	}

	// 5. 验证收货地址
	l.Logger.Infof("验证收货地址: address_id=%d", req.AddressId)
	address, err := l.svcCtx.UserAddressModel.FindOne(ctx, req.AddressId)
	if err != nil {
		if err == model.ErrNotFound {
			l.Logger.Errorf("收货地址不存在: address_id=%d", req.AddressId)
			return xerr.NewErrMsg("收货地址不存在")
		}
		l.Logger.Errorf("UserAddressModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	l.Logger.Infof("验证地址归属: address_uid=%d, current_uid=%d", address.Uid, uid)
	if address.Uid != uid {
		l.Logger.Errorf("收货地址不属于当前用户: address_uid=%d, current_uid=%d", address.Uid, uid)
		return xerr.NewErrMsg("收货地址不属于当前用户")
	}

	// 6. 检查是否首次领取，更新盲盒领取数量
	err = l.updateBoxGetNum(ctx, tx, orderMainBox.MainOrderNo, orderMainBox.BoxId)
	if err != nil {
		return err
	}

	// 7. 更新盲盒订单状态
	err = l.updateOrderMainBoxStatus(ctx, tx, orderMainBox, req.BoxType, now, subOrderNo)
	if err != nil {
		return err
	}

	// 8. 生成子订单
	err = l.createSubOrder(ctx, tx, uid, orderMainBox, address, req.BoxType, subOrderNo)
	if err != nil {
		return err
	}

	return nil
}

// updateBoxGetNum 更新盲盒领取数量（首次领取时）
func (l *ClaimLogic) updateBoxGetNum(ctx context.Context, tx *sql.Tx, mainOrderNo string, boxId int64) error {
	// 检查该主订单是否首次领取
	checkQuery := `SELECT COUNT(*) FROM vh_order_main_box WHERE main_order_no = ? AND box_type IN (2, 3)`
	var count int
	err := tx.QueryRowContext(ctx, checkQuery, mainOrderNo).Scan(&count)
	if err != nil {
		l.Logger.Errorf("Check first claim error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 如果是首次领取，更新盲盒的 get_num
	if count == 0 {
		updateBoxQuery := `UPDATE vh_box SET get_num = get_num + 1 WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateBoxQuery, boxId)
		if err != nil {
			l.Logger.Errorf("Update box get_num error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}

// updateOrderMainBoxStatus 更新盲盒订单状态
func (l *ClaimLogic) updateOrderMainBoxStatus(ctx context.Context, tx *sql.Tx, orderMainBox *model.VhOrderMainBox, boxType int, now time.Time, subOrderNo string) error {
	updateQuery := `UPDATE vh_order_main_box SET box_type = ?, get_time = ?, order_no = ? WHERE id = ?`
	_, err := tx.ExecContext(ctx, updateQuery, boxType, now, subOrderNo, orderMainBox.Id)
	if err != nil {
		l.Logger.Errorf("Update order main box status error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	return nil
}

// createSubOrder 创建子订单
func (l *ClaimLogic) createSubOrder(ctx context.Context, tx *sql.Tx, uid int64, orderMainBox *model.VhOrderMainBox, address *model.UserAddress, boxType int, subOrderNo string) error {

	// 获取主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(ctx, orderMainBox.MainOrderNo)
	if err != nil {
		l.Logger.Errorf("OrderMainModel.FindOneByMainOrderNo error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 创建盲盒子订单快照
	snapshot, err := l.createBoxSubOrderSnapshot(mainOrder, orderMainBox)
	if err != nil {
		l.Logger.Errorf("createBoxSubOrderSnapshot error: %v", err)
		return err
	}

	// 创建子订单
	insertOrderQuery := `INSERT INTO vh_order (
		snapshot, uid, sub_order_no, sub_order_status, main_order_id, goods_id, order_qty,
		payment_amount, cash_amount, erp_amount, deductible_amount, refund_deductible,
		refund_status, refund_time, refund_money, express_type, express_number,
		payment_time, delivery_time, cancel_time, goods_receipt_time, warehouse_code,
		province_id, province, city_id, city, district_id, district, address,
		consignee, consignee_phone, remarks, push_t_status, push_wms_status, push_zt_status, type
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = tx.ExecContext(ctx, insertOrderQuery,
		snapshot,                                  // snapshot
		uid,                                       // uid
		subOrderNo,                                // sub_order_no
		1,                                         // sub_order_status (已支付)
		mainOrder.Id,                              // main_order_id
		0,                                         // goods_id (盲盒商品ID为0)
		1,                                         // order_qty
		0.00,                                      // payment_amount
		0.00,                                      // cash_amount
		0.00,                                      // erp_amount
		0.00,                                      // deductible_amount
		0.00,                                      // refund_deductible
		0,                                         // refund_status
		0,                                         // refund_time
		0.00,                                      // refund_money
		0,                                         // express_type
		nil,                                       // express_number
		mainOrder.PaymentTime,                     // payment_time
		0,                                         // delivery_time
		nil,                                       // cancel_time
		0,                                         // goods_receipt_time
		nil,                                       // warehouse_code
		address.ProvinceId,                        // province_id
		address.ProvinceName,                      // province
		address.CityId,                            // city_id
		address.CityName,                          // city
		address.TownId,                            // district_id
		address.TownName,                          // district
		address.Address,                           // address
		address.Consignee,                         // consignee
		address.ConsigneePhone,                    // consignee_phone
		fmt.Sprintf("盲盒领取类型:%d", boxType),        // remarks
		0,                                         // push_t_status
		0,                                         // push_wms_status
		3,                                         // push_zt_status (设置为3)
		3,                                         // type (盲盒商品)
	)

	if err != nil {
		l.Logger.Errorf("Create sub order error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}

// pushBoxOrderToMiddlePlatform 推送盲盒订单到中台
func (l *ClaimLogic) pushBoxOrderToMiddlePlatform(req *types.BoxClaimReq, subOrderNo string) error {
	// 获取盲盒订单详情
	orderMainBox, err := l.svcCtx.OrderMainBoxModel.FindOne(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf("OrderMainBoxModel.FindOne error: %v", err)
		return err
	}

	l.Logger.Infof("盲盒商品领取成功，准备推送中台: main_order_no=%s, sub_order_no=%s",
		orderMainBox.MainOrderNo, subOrderNo)

	// 使用订单推送服务推送到中台
	pushService := service.NewOrderPushService(l.ctx, l.svcCtx)
	err = pushService.PushOrderToMiddlePlatform(orderMainBox.MainOrderNo)
	if err != nil {
		l.Logger.Errorf("推送盲盒订单到中台失败: main_order_no=%s, sub_order_no=%s, error=%v",
			orderMainBox.MainOrderNo, subOrderNo, err)
		return err
	}

	l.Logger.Infof("盲盒订单推送中台成功: main_order_no=%s, sub_order_no=%s",
		orderMainBox.MainOrderNo, subOrderNo)
	return nil
}

// checkAndUpdateMainOrderStatus 检查并更新主订单状态
func (l *ClaimLogic) checkAndUpdateMainOrderStatus(orderMainBoxId int64) error {
	// 获取当前盲盒订单信息
	orderMainBox, err := l.svcCtx.OrderMainBoxModel.FindOne(l.ctx, orderMainBoxId)
	if err != nil {
		l.Logger.Errorf("checkAndUpdateMainOrderStatus FindOne error: %v", err)
		return err
	}

	// 查询该主订单下的所有盲盒项目
	boxItems, err := l.svcCtx.OrderMainBoxModel.FindByMainOrderNo(l.ctx, orderMainBox.MainOrderNo)
	if err != nil {
		l.Logger.Errorf("checkAndUpdateMainOrderStatus FindByMainOrderNo error: %v", err)
		return err
	}

	// 检查是否所有项目都已领取完成或已过期
	now := time.Now()
	allCompletedOrExpired := true
	for _, item := range boxItems {
		// box_type: 1=待领取, 2=自己领取, 3=赠送, 4=赠送中
		if item.BoxType.Valid && (item.BoxType.Int64 == 2 || item.BoxType.Int64 == 3) {
			// 已领取，继续检查下一个
			continue
		}

		// 检查是否已过期
		if item.BoxType.Valid && item.BoxType.Int64 == 1 && item.EndTime.Valid {
			if now.After(item.EndTime.Time) {
				// 已过期，视为完成
				l.Logger.Infof("盲盒项目已过期: id=%d, end_time=%s", item.Id, item.EndTime.Time.Format("2006-01-02 15:04:05"))
				continue
			}
		}

		// 还有未领取且未过期的项目
		allCompletedOrExpired = false
		break
	}

	if !allCompletedOrExpired {
		l.Logger.Infof("盲盒订单 %s 还有未领取且未过期的项目", orderMainBox.MainOrderNo)
		return nil
	}

	// 所有项目都已领取完成或过期，更新主订单状态为已完成
	return l.updateMainOrderToCompleted(orderMainBox.MainOrderNo)
}

// updateMainOrderToCompleted 更新主订单状态为已完成
func (l *ClaimLogic) updateMainOrderToCompleted(mainOrderNo string) error {
	// 查询主订单信息
	mainOrder, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, mainOrderNo)
	if err != nil {
		l.Logger.Errorf("updateMainOrderToCompleted FindOneByMainOrderNo error: %v", err)
		return err
	}

	// 如果主订单状态已经是已完成，则不需要更新
	if mainOrder.MainOrderStatus == 3 {
		l.Logger.Infof("主订单 %s 状态已经是已完成", mainOrderNo)
		return nil
	}

	// 更新主订单状态为已完成
	now := time.Now()
	updateBuilder := squirrel.Update(l.svcCtx.OrderMainModel.TableName()).
		Set("main_order_status", 3).
		Set("update_time", now.Format("2006-01-02 15:04:05")).
		Where(squirrel.Eq{"main_order_no": mainOrderNo}).
		Where(squirrel.NotEq{"main_order_status": 3})

	_, err = l.svcCtx.OrderMainModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Errorf("updateMainOrderToCompleted update error: %v", err)
		return err
	}

	l.Logger.Infof("主订单状态更新为已完成 - 订单号: %s", mainOrderNo)
	return nil
}

// generateSubOrderNo 生成子订单号
func (l *ClaimLogic) generateSubOrderNo() string {
	return function.GenerateSubOrderNo()
}

// createBoxSubOrderSnapshot 创建盲盒子订单快照
func (l *ClaimLogic) createBoxSubOrderSnapshot(mainOrder *model.VhOrderMain, orderMainBox *model.VhOrderMainBox) (string, error) {
	// 解析主订单快照获取盲盒信息
	var mainSnapshot map[string]interface{}
	if mainOrder.Snapshot.Valid && mainOrder.Snapshot.String != "" {
		err := json.Unmarshal([]byte(mainOrder.Snapshot.String), &mainSnapshot)
		if err != nil {
			l.Logger.Errorf("Unmarshal main order snapshot error: %v", err)
			return "", xerr.NewErrCode(xerr.ServerCommonError)
		}
	}

	// 解析vh_order_main_box.items_info获取商品信息
	var itemsInfo []map[string]interface{}
	if orderMainBox.ItemsInfo != "" {
		err := json.Unmarshal([]byte(orderMainBox.ItemsInfo), &itemsInfo)
		if err != nil {
			l.Logger.Errorf("Unmarshal items_info error: %v", err)
			return "", xerr.NewErrCode(xerr.ServerCommonError)
		}
	}

	// 构建盲盒子订单快照
	snapshot := map[string]interface{}{
		"avatar_image":      function.NormalizeImagePath(getStringFromSnapshot(mainSnapshot, "avatar_image")), // 统一存储半路径
		"title":             getStringFromSnapshot(mainSnapshot, "title"),
		"box_id":            getIntFromSnapshot(mainSnapshot, "box_id"),
		"valid_time_unit":   getStringFromSnapshot(mainSnapshot, "valid_time_unit"),
		"valid_time_num":    getIntFromSnapshot(mainSnapshot, "valid_time_num"),
		"price":             0,    // 盲盒取0
		"cashback_amount":   0,    // 盲盒取0
		"erp_amount":        0,    // 盲盒取0
		"deductible_amount": 0,    // 盲盒取0
		"product_img":       "",   // 盲盒取空字符串
		"detail":            "",   // 盲盒取空字符串
		"goods_id":          0,    // 盲盒取0
		"inventory":         0,    // 盲盒取0
		"items_info":        itemsInfo, // 盲盒取关联的vh_order_main_box.items_info
		"labels":            "",   // 盲盒取空字符串
		"snapshot_time":     function.FormatTime(time.Now()),
		"type":              3,    // 盲盒类型
		"warehouse_code":    "",   // 盲盒取空字符串
	}

	snapshotJson, err := json.Marshal(snapshot)
	if err != nil {
		l.Logger.Errorf("Marshal snapshot error: %v", err)
		return "", xerr.NewErrCode(xerr.ServerCommonError)
	}

	return string(snapshotJson), nil
}

// getStringFromSnapshot 从快照中获取字符串值
func getStringFromSnapshot(snapshot map[string]interface{}, key string) string {
	if snapshot == nil {
		return ""
	}
	if val, ok := snapshot[key].(string); ok {
		return val
	}
	return ""
}

// getIntFromSnapshot 从快照中获取整数值
func getIntFromSnapshot(snapshot map[string]interface{}, key string) int {
	if snapshot == nil {
		return 0
	}
	if val, ok := snapshot[key].(float64); ok {
		return int(val)
	}
	if val, ok := snapshot[key].(int); ok {
		return val
	}
	return 0
}
