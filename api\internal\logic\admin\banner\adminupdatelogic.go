package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminUpdateLogic {
	return &AdminUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminUpdateLogic) AdminUpdate(req *types.BannerUpdateReq) error {
	// 检查记录是否存在
	_, err := l.svcCtx.BannerModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminUpdateLogic BannerModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 从context中获取管理员信息
	adminUid, err := function.GetAdminUid(l.ctx)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic GetAdminUid error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	adminVosName, err := function.GetAdminVosName(l.ctx)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic GetAdminVosName error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	// 更新数据
	banner := &model.Banner{
		Id:        req.Id,
		Name:      req.Name,
		Picture:   req.Picture,
		JumpType:  req.JumpType,
		JumpValue: req.JumpValue,
		Sort:      req.Sort,
		Status:    req.Status,
		VhUid:     adminUid,     // 使用当前操作用户
		VhVosName: adminVosName, // 使用当前操作用户
	}

	err = l.svcCtx.BannerModel.Update(l.ctx, banner)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic BannerModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
