package order

import (
	"context"
	"database/sql"
	"encoding/json"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

// OrderService 订单服务，提供内部调用方法
type OrderService struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOrderService(ctx context.Context, svcCtx *svc.ServiceContext) *OrderService {
	return &OrderService{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// CreateLotteryOrdersBatch 批量创建抽奖订单（使用外部事务）
func (s *OrderService) CreateLotteryOrdersBatch(ctx context.Context, tx *sql.Tx, orders []types.LotteryOrderItem) ([]types.LotteryOrderResult, error) {
	var results []types.LotteryOrderResult

	for _, orderItem := range orders {
		result, err := s.CreateSingleLotteryOrder(ctx, tx, orderItem)
		if err != nil {
			return nil, err
		}
		results = append(results, *result)
	}

	return results, nil
}

// createSingleLotteryOrder 创建单个抽奖订单（内部方法）
func (s *OrderService) CreateSingleLotteryOrder(ctx context.Context, tx *sql.Tx, orderItem types.LotteryOrderItem) (*types.LotteryOrderResult, error) {
	// 固定数量为1
	quantity := int64(1)

	// 验证商品信息
	goods, err := s.svcCtx.GoodsModel.FindOne(ctx, uint64(orderItem.GoodsId))
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("商品不存在")
		}
		logx.Error("createSingleLotteryOrder GoodsModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证商品状态
	if goods.DeleteTime > 0 {
		return nil, xerr.NewErrMsg("商品不存在")
	}
	if goods.OnsaleStatus != 2 {
		return nil, xerr.NewErrMsg("商品未上架")
	}
	if goods.Type != 2 {
		return nil, xerr.NewErrMsg("该商品不支持抽奖订单")
	}

	// 验证库存
	if goods.Inventory < uint64(quantity) {
		return nil, xerr.NewErrMsg("商品库存不足")
	}

	// 验证收货地址
	address, err := s.svcCtx.UserAddressModel.FindOne(ctx, orderItem.AddressId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("收货地址不存在")
		}
		logx.Error("createSingleLotteryOrder AddressModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证地址是否属于该用户
	if address.Uid != orderItem.Uid {
		return nil, xerr.NewErrMsg("收货地址不属于该用户")
	}

	// 获取用户信息
	user, err := s.svcCtx.UserModel.FindOne(ctx, orderItem.Uid)
	if err != nil {
		logx.Error("createSingleLotteryOrder UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取购买人角色（抽奖订单没有分享人）
	buyerLevel := user.Type
	sharerLevel := int64(0) // 抽奖订单没有分享人

	// 生成订单号
	mainOrderNo := function.GenerateMainOrderNo()
	subOrderNo := function.GenerateSubOrderNo()

	// 创建商品快照
	snapshot, err := s.createGoodsSnapshot(goods)
	if err != nil {
		return nil, err
	}

	// 扣减商品库存
	updateQuery := `UPDATE vh_goods SET inventory = inventory - ?, purchased = purchased + ?, sales_user_num = sales_user_num + 1 WHERE id = ? AND inventory >= ?`
	result, err := tx.ExecContext(ctx, updateQuery, quantity, quantity, orderItem.GoodsId, quantity)
	if err != nil {
		logx.Error("createSingleLotteryOrder update goods inventory error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	affected, _ := result.RowsAffected()
	if affected == 0 {
		return nil, xerr.NewErrMsg("商品库存不足")
	}

	// 创建主订单
	cashAmount := 0.0
	deductibleAmount := 0.0
	// payment_amount = cash_amount + deductible_amount (总共需要支付金额)
	paymentAmount := cashAmount + deductibleAmount

	mainOrder := &model.VhOrderMain{
		Uid:              orderItem.Uid,
		MainOrderNo:      mainOrderNo,
		MainOrderStatus:  1,             // 已支付
		PaymentAmount:    paymentAmount, // 总共需要支付金额 = cash_amount + deductible_amount
		PaymentTime:      time.Now().Unix(),
		PaymentMethod:    sql.NullInt64{Int64: 1, Valid: true}, // 抽奖活动
		DeductibleAmount: deductibleAmount,
		CashAmount:       cashAmount,
		Snapshot:         sql.NullString{}, // 抽奖订单snapshot为空，只有盲盒订单才使用
		ShareId:          0,                // 抽奖订单不使用分享ID
	}

	insertMainOrderQuery := `INSERT INTO vh_order_main (uid, main_order_no, main_order_status, payment_amount, payment_time, payment_method, deductible_amount, cash_amount, snapshot, share_id, buyer_level, sharer_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	mainResult, err := tx.ExecContext(ctx, insertMainOrderQuery, mainOrder.Uid, mainOrder.MainOrderNo, mainOrder.MainOrderStatus, mainOrder.PaymentAmount, mainOrder.PaymentTime, mainOrder.PaymentMethod.Int64, mainOrder.DeductibleAmount, mainOrder.CashAmount, mainOrder.Snapshot.String, mainOrder.ShareId, buyerLevel, sharerLevel)
	if err != nil {
		logx.Error("createSingleLotteryOrder insert main order error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	mainOrderId, _ := mainResult.LastInsertId()

	// 创建子订单
	subOrder := &model.VhOrder{
		Snapshot:         sql.NullString{String: snapshot, Valid: true}, // 子订单保留商品快照
		Uid:              orderItem.Uid,
		SubOrderNo:       subOrderNo,
		SubOrderStatus:   1, // 已支付
		MainOrderId:      mainOrderId,
		GoodsId:          orderItem.GoodsId,
		OrderQty:         quantity,
		PaymentAmount:    paymentAmount, // 总共需要支付金额 = cash_amount + deductible_amount
		CashAmount:       cashAmount,
		ErpAmount:        goods.ErpAmount * float64(quantity),
		DeductibleAmount: deductibleAmount,
		ExpressType:      0,
		PaymentTime:      time.Now().Unix(),
		ProvinceId:       sql.NullInt64{Int64: address.ProvinceId, Valid: true},
		Province:         sql.NullString{String: address.ProvinceName, Valid: true},
		CityId:           sql.NullInt64{Int64: address.CityId, Valid: true},
		City:             sql.NullString{String: address.CityName, Valid: true},
		DistrictId:       sql.NullInt64{Int64: address.TownId, Valid: true},
		District:         sql.NullString{String: address.TownName, Valid: true},
		Address:          sql.NullString{String: address.Address, Valid: true},
		Consignee:        sql.NullString{String: address.Consignee, Valid: true},
		ConsigneePhone:   sql.NullString{String: address.ConsigneePhone, Valid: true},
		PushZtStatus:     0, // 未推送，等待定时任务推送
		Type:             2, // 抽奖商品
	}

	insertSubOrderQuery := `INSERT INTO vh_order (snapshot, uid, sub_order_no, sub_order_status, main_order_id, goods_id, order_qty, payment_amount, cash_amount, erp_amount, deductible_amount, express_type, payment_time, province_id, province, city_id, city, district_id, district, address, consignee, consignee_phone, push_zt_status, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	_, err = tx.ExecContext(ctx, insertSubOrderQuery, subOrder.Snapshot.String, subOrder.Uid, subOrder.SubOrderNo, subOrder.SubOrderStatus, subOrder.MainOrderId, subOrder.GoodsId, subOrder.OrderQty, subOrder.PaymentAmount, subOrder.CashAmount, subOrder.ErpAmount, subOrder.DeductibleAmount, subOrder.ExpressType,
		subOrder.PaymentTime, subOrder.ProvinceId.Int64, subOrder.Province.String,
		subOrder.CityId.Int64, subOrder.City.String, subOrder.DistrictId.Int64, subOrder.District.String,
		subOrder.Address.String, subOrder.Consignee.String, subOrder.ConsigneePhone.String, subOrder.PushZtStatus, subOrder.Type)
	if err != nil {
		logx.Error("createSingleLotteryOrder insert sub order error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	return &types.LotteryOrderResult{
		Uid:         orderItem.Uid,
		MainOrderNo: mainOrderNo,
		SubOrderNo:  subOrderNo,
	}, nil
}

// createGoodsSnapshot 创建商品快照
func (s *OrderService) createGoodsSnapshot(goods *model.VhGoods) (string, error) {
	// 获取商品标签
	labels, err := s.svcCtx.GoodsLabelModel.FindByPeriodId(s.ctx, int64(goods.Id))
	if err != nil {
		logx.Error("createGoodsSnapshot FindByPeriodId error: %v", err)
		// 标签获取失败不影响订单创建，使用空标签
		labels = []*model.VhGoodsLabel{}
	}

	// 构建标签字符串
	var labelNames []string
	for _, label := range labels {
		labelNames = append(labelNames, label.Name)
	}
	labelsStr := strings.Join(labelNames, ",")

	// 解析商品简码信息，使用统一的解析函数
	var itemsInfo []map[string]interface{}
	if goods.ItemsInfo.Valid {
		itemsInfo = function.ParseItemsInfo(goods.ItemsInfo.String)
	}

	// 构建商品快照
	snapshot := map[string]interface{}{
		"avatar_image":      function.NormalizeImagePath(goods.AvatarImage), // 统一存储半路径
		"title":             goods.Title,
		"brief":             goods.Brief,
		"box_id":            0,  // 非盲盒取0
		"valid_time_unit":   "", // 非盲盒取空字符串
		"valid_time_num":    0,  // 非盲盒取0
		"price":             goods.Price,
		"cashback_amount":   goods.CashbackAmount,
		"erp_amount":        goods.ErpAmount,
		"deductible_amount": goods.DeductibleAmount,
		"product_img":       function.NormalizeImagePath(goods.ProductImg), // 统一存储半路径
		"detail":            goods.Detail,
		"goods_id":          goods.Id,
		"inventory":         goods.Inventory,
		"items_info":        itemsInfo,
		"labels":            labelsStr,
		"snapshot_time":     function.FormatTime(time.Now()),
		"type":              goods.Type,
		"warehouse_code":    goods.WarehouseCode,
	}

	snapshotJson, err := json.Marshal(snapshot)
	if err != nil {
		logx.Error("createGoodsSnapshot json.Marshal error: %v", err)
		return "", xerr.NewErrCode(xerr.ServerCommonError)
	}

	return string(snapshotJson), nil
}
