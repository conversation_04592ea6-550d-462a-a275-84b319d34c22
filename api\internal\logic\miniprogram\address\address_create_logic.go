package address

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressCreateLogic {
	return &AddressCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressCreateLogic) AddressCreate(req *types.AddressCreateReq) (resp *types.AddressCreateResp, err error) {
	// todo: add your logic here and delete this line

	return
}
