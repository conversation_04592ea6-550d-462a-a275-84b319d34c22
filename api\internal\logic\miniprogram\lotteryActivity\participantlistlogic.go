package lotteryActivity

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantListLogic {
	return &ParticipantListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantListLogic) ParticipantList(req *types.ActiveLotteryActivityParticipantListReq) (resp *types.ActiveLotteryActivityParticipantListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.ParticipantUserInfo
	)

	// 构建查询条件
	where := squirrel.And{
		squirrel.Eq{"vh_lottery_participant.activity_id": req.Id},
	}

	builder := squirrel.Select("nickname", "join_time").From("vh_lottery_participant").
		Join("vh_user ON vh_lottery_participant.uid = vh_user.id").
		Where(where).OrderBy("vh_lottery_participant.id DESC").
		Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit))

	// 获取总数
	wait.Go(func() error {
		ct, er := l.svcCtx.LotteryParticipantModel.FindCount(l.ctx, model.CountBuilder("*", l.svcCtx.LotteryParticipantModel.TableName()).Where(where))
		if er != nil {
			l.Errorf("ParticipantListLogic.LotteryParticipantModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		er := l.svcCtx.LotteryParticipantModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("ParticipantListLogic.LotteryParticipantModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.ActiveLotteryActivityParticipantListResp)
	resp.Total = total
	resp.List = make([]types.ParticipantUserInfo, 0, len(list))
	for _, ls := range list {
		info := types.ParticipantUserInfo{
			Nickname: ls.Nickname,
			JoinTime: common.TimeToString(ls.JoinTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
