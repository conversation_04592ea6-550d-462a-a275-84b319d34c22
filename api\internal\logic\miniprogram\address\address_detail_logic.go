package address

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressDetailLogic {
	return &AddressDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressDetailLogic) AddressDetail(req *types.AddressDetailReq) (resp *types.UserAddressInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
