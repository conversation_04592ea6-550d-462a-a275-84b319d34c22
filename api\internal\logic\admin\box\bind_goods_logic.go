package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BindGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBindGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindGoodsLogic {
	return &BindGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BindGoodsLogic) BindGoods(req *types.BoxBindGoodsReq) error {
	// todo: add your logic here and delete this line

	return nil
}
