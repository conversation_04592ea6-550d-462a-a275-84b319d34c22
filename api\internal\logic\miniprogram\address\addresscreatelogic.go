package address

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressCreateLogic {
	return &AddressCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressCreateLogic) AddressCreate(req *types.AddressCreateReq) (resp *types.AddressCreateResp, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 如果设置为默认地址，先取消其他默认地址
	if req.IsDefault == 1 {
		updateBuilder := squirrel.Update(l.svcCtx.UserAddressModel.TableName()).
			Set("is_default", 0).
			Where(squirrel.Eq{"uid": uid, "is_default": 1})

		_, err := l.svcCtx.UserAddressModel.UpdateCustom(l.ctx, updateBuilder)
		if err != nil {
			l.Logger.Error("AddressCreate UpdateCustom error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}
	}

	// 创建地址 - 使用正确的字段结构
	address := &model.UserAddress{
		Uid:            uid,
		ProvinceId:     req.ProvinceId,
		CityId:         req.CityId,
		TownId:         req.TownId,
		Address:        req.Address,
		IsDefault:      req.IsDefault,
		Label:          req.Label,
		Code:           req.Code,
		Consignee:      req.Consignee,
		ConsigneePhone: req.ConsigneePhone,
		ProvinceName:   req.ProvinceName,
		CityName:       req.CityName,
		TownName:       req.TownName,
	}

	result, err := l.svcCtx.UserAddressModel.Insert(l.ctx, address)
	if err != nil {
		l.Logger.Error("AddressCreate UserAddressModel.Insert error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取插入的ID
	addressId, err := result.LastInsertId()
	if err != nil {
		l.Logger.Error("AddressCreate get LastInsertId error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	return &types.AddressCreateResp{
		Id: addressId,
	}, nil
}
