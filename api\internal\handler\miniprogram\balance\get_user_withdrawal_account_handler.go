package balance

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/balance"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetUserWithdrawalAccountHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := balance.NewGetUserWithdrawalAccountLogic(r.Context(), svcCtx)
		resp, err := l.GetUserWithdrawalAccount()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
