package share

import (
	"context"
	"database/sql"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MyShareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMyShareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MyShareLogic {
	return &MyShareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MyShareLogic) MyShare() (resp *types.MyShareResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	var myShareSummary model.MyShareSummary
	builder := squirrel.Select("COALESCE(SUM(buy_count), 0) AS buy_ct, COUNT(*) as share_ct, COALESCE(SUM(buy_money), 0) AS cashback_amount").
		From(l.svcCtx.ShareModel.TableName()).
		Where(squirrel.Eq{"uid": uid})
	err = l.svcCtx.ShareModel.FindOneCustom(l.ctx, builder, &myShareSummary)
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		l.Errorf("MyShareLogic ShareModel.FindOneByQuery error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = &types.MyShareResp{
		ShareCt:        myShareSummary.ShareCt,
		BuyCt:          myShareSummary.BuyCt,
		CashbackAmount: myShareSummary.CashbackAmount,
	}

	return
}
