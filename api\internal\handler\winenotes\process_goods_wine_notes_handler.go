package winenotes

import (
	"net/http"

	"engine/api/internal/logic/winenotes"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 处理商品的WineNotes数据
func ProcessGoodsWineNotesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GoodsWineNotesReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := winenotes.NewProcessGoodsWineNotesLogic(r.Context(), svcCtx)
		resp, err := l.ProcessGoodsWineNotes(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
