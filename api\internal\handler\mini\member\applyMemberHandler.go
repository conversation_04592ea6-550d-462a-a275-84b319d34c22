package member

import (
	"net/http"

	"engine/api/internal/logic/mini/member"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ApplyMemberHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ApplyMemberReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := member.NewApplyMemberLogic(r.Context(), svcCtx)
		resp, err := l.ApplyMember(&req)
		result.HttpResult(r, w, resp, err)
	}
}
