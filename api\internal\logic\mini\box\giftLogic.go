package box

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftLogic {
	return &GiftLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftLogic) Gift(req *types.BoxGiftReq) (resp *types.BoxGiftResp, err error) {
	// 获取用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		l.Logger.Error("uid not found in context")
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询盲盒主订单信息
	boxMainOrder, err := l.svcCtx.OrderMainBoxModel.FindOne(l.ctx, req.BoxMainOrderId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("盲盒订单不存在")
		}
		l.Logger.Errorf("查询盲盒主订单失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证订单归属
	if boxMainOrder.Uid != uid {
		return nil, xerr.NewErrMsg("无权限操作此订单")
	}

	// 验证盲盒状态，只有待领取(1)和赠送中(4)的盲盒可以转赠
	if !boxMainOrder.BoxType.Valid || (boxMainOrder.BoxType.Int64 != 1 && boxMainOrder.BoxType.Int64 != 4) {
		var statusMsg string
		switch boxMainOrder.BoxType.Int64 {
		case 2:
			statusMsg = "该盲盒已被领取，无法转赠"
		case 3:
			statusMsg = "该盲盒已被转赠，无法重复转赠"
		default:
			statusMsg = "当前盲盒状态不支持转赠"
		}
		return nil, xerr.NewErrMsg(statusMsg)
	}

	// 验证盲盒时间有效期
	now := time.Now()
	if !boxMainOrder.BeginTime.Valid || !boxMainOrder.EndTime.Valid {
		return nil, xerr.NewErrMsg("盲盒时间信息异常")
	}
	if now.Before(boxMainOrder.BeginTime.Time) {
		return nil, xerr.NewErrMsg("盲盒尚未到领取时间，无法转赠")
	}
	if now.After(boxMainOrder.EndTime.Time) {
		return nil, xerr.NewErrMsg("盲盒已过期，无法转赠")
	}

	// 检查是否已有有效的转赠记录
	existingShare, err := l.svcCtx.BoxGiftShareModel.FindValidByBoxMainOrderId(l.ctx, req.BoxMainOrderId)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Errorf("查询转赠记录失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 如果已有有效转赠记录，返回详细信息
	if existingShare != nil {
		// 获取转赠人信息
		senderUser, err := l.svcCtx.UserModel.FindOne(l.ctx, existingShare.SenderUid)
		if err != nil {
			l.Logger.Errorf("查询转赠人信息失败: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		shareUrl := fmt.Sprintf("/pages/gift/detail?share_id=%d", existingShare.Id)
		return &types.BoxGiftResp{
			ShareId:        existingShare.Id,
			BoxTitle:       existingShare.BoxTitle,
			BoxAvatarImage: l.normalizeImagePath(existingShare.BoxAvatarImage),
			PeriodLabel:    existingShare.PeriodLabel,
			ExpireTime:     existingShare.ExpireTime.Format("2006-01-02 15:04:05"),
			Status:         existingShare.Status,
			SenderName:     l.maskUserName(senderUser.Nickname),
			ShareUrl:       shareUrl,
		}, nil
	}

	// 获取盲盒信息
	boxInfo, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(boxMainOrder.BoxId))
	if err != nil {
		l.Logger.Errorf("查询盲盒信息失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 生成期数标识
	periodLabel := l.generatePeriodLabel(boxMainOrder.BeginTime.Time)

	// 计算转赠过期时间：取24小时和盲盒剩余有效期的较小值
	defaultExpireTime := now.Add(24 * time.Hour)
	boxExpireTime := boxMainOrder.EndTime.Time

	var expireTime time.Time
	if defaultExpireTime.Before(boxExpireTime) {
		expireTime = defaultExpireTime
	} else {
		expireTime = boxExpireTime
	}

	// 开启事务
	var shareId int64
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 更新盲盒状态为赠送中
		updateQuery := `UPDATE vh_order_main_box SET box_type = 4 WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateQuery, req.BoxMainOrderId)
		if err != nil {
			l.Logger.Errorf("更新盲盒状态失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 创建转赠分享记录
		giftShare := &model.VhBoxGiftShare{
			BoxMainOrderId: req.BoxMainOrderId,
			SenderUid:      uid,
			BoxId:          boxMainOrder.BoxId,
			BoxTitle:       boxInfo.Title,
			BoxAvatarImage: boxInfo.AvatarImage,
			PeriodLabel:    periodLabel,
			Status:         1, // 待领取
			ExpireTime:     expireTime,
		}

		// 使用原生SQL插入，避免自动设置字段问题
		insertQuery := `INSERT INTO vh_box_gift_share (box_main_order_id, sender_uid, box_id, box_title, box_avatar_image, period_label, status, expire_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
		result, err := tx.ExecContext(ctx, insertQuery, giftShare.BoxMainOrderId, giftShare.SenderUid, giftShare.BoxId, giftShare.BoxTitle, giftShare.BoxAvatarImage, giftShare.PeriodLabel, giftShare.Status, giftShare.ExpireTime)
		if err != nil {
			l.Logger.Errorf("创建转赠记录失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		shareId, _ = result.LastInsertId()
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 获取转赠人信息
	senderUser, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		l.Logger.Errorf("查询转赠人信息失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 生成分享链接
	shareUrl := fmt.Sprintf("/pages/gift/detail?share_id=%d", shareId)

	return &types.BoxGiftResp{
		ShareId:        shareId,
		BoxTitle:       boxInfo.Title,
		BoxAvatarImage: l.normalizeImagePath(boxInfo.AvatarImage),
		PeriodLabel:    periodLabel,
		ExpireTime:     expireTime.Format("2006-01-02 15:04:05"),
		Status:         1, // 待领取
		SenderName:     l.maskUserName(senderUser.Nickname),
		ShareUrl:       shareUrl,
	}, nil
}

// generatePeriodLabel 生成期数标识
func (l *GiftLogic) generatePeriodLabel(beginTime time.Time) string {
	if beginTime.IsZero() {
		return time.Now().Format("2006年01月")
	}
	return beginTime.Format("2006年01月")
}

// maskUserName 脱敏处理用户姓名
func (l *GiftLogic) maskUserName(name string) string {
	if len(name) == 0 {
		return "匿名用户"
	}
	if len(name) == 1 {
		return name
	}
	if len(name) == 2 {
		return string([]rune(name)[0]) + "*"
	}
	runes := []rune(name)
	result := string(runes[0])
	for i := 1; i < len(runes)-1; i++ {
		result += "*"
	}
	result += string(runes[len(runes)-1])
	return result
}

// normalizeImagePath 处理图片路径，添加ALIURL前缀
func (l *GiftLogic) normalizeImagePath(imagePath string) string {
	if imagePath == "" {
		return ""
	}
	// 如果已经是完整URL，直接返回
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		return imagePath
	}
	// 添加ALIURL前缀
	return l.svcCtx.Config.ITEM.ALIURL + imagePath
}
