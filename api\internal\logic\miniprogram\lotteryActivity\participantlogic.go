package lotteryActivity

import (
	"context"
	"database/sql"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"fmt"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ParticipantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParticipantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParticipantLogic {
	return &ParticipantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParticipantLogic) Participant(req *types.ActiveLotteryActivityParticipantReq) error {
	var (
		wait     errgroup.Group
		activity *model.VhLotteryActivity
		joinCt   int64
		address  *model.UserAddress
	)

	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	// 并发获取基础数据
	wait.Go(func() error {
		info, err := l.svcCtx.LotteryActivityModel.FindOne(l.ctx, req.Id)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			l.Errorf("ParticipantLogic.LotteryActivityModel.FindOne err: %v", err)
			return err
		}
		if errors.Is(err, model.ErrNotFound) {
			return err
		}
		activity = info
		return nil
	})
	wait.Go(func() error {
		info, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.AddressId)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			l.Errorf("ParticipantLogic.UserAddressModel.FindOne err: %v", err)
			return err
		}
		if errors.Is(err, model.ErrNotFound) {
			return err
		}
		address = info
		return nil
	})
	wait.Go(func() error {
		ct, err := l.svcCtx.LotteryParticipantModel.FindCount(l.ctx, model.CountBuilder("*", l.svcCtx.LotteryParticipantModel.TableName()).
			Where(squirrel.Eq{"activity_id": req.Id, "uid": uid}))
		if err != nil {
			l.Errorf("ParticipantLogic.LotteryParticipantModel.FindCount err: %v", err)
			return err
		}
		joinCt = ct
		return nil
	})

	err := wait.Wait()
	if errors.Is(err, model.ErrNotFound) {
		// 没有找到活动，返回空响应
		return xerr.NewErrCode(xerr.DataNoExistError)
	} else if err != nil {
		return xerr.NewErrCode(xerr.DbError)
	}

	// 业务逻辑验证
	if joinCt > 0 {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "已参与过此活动")
	}

	if address.Uid != uid {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "地址不存在")
	}

	if activity.Status != 1 {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "活动未开启")
	}

	if activity.ParticipantCount >= activity.Total {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "参与人数已满")
	}

	// 优化后的事务处理
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		// 1. 使用原子操作更新参与人数，避免乐观锁竞争
		updateResult, er := l.updateParticipantCount(ctx, tx, req.Id, activity)
		if er != nil {
			return er
		}

		// 检查是否真的更新了记录（防止并发问题）
		rowsAffected, er := updateResult.RowsAffected()
		if er != nil {
			l.Errorf("ParticipantLogic.Trans.RowsAffected err: %v", er)
			return er
		}
		if rowsAffected == 0 {
			// 说明参与人数已经变化，需要重试
			return fmt.Errorf("participant count changed, please retry")
		}

		// 2. 插入参与记录
		participant := &model.VhLotteryParticipant{
			ActivityId: req.Id,
			Uid:        uint64(uid),
			AddressId:  uint64(req.AddressId),
			JoinTime:   time.Now(),
			IsWinner:   0,
			SubOrderNo: "",
		}
		_, er = l.svcCtx.LotteryParticipantModel.InsertTx(ctx, tx, participant)
		if er != nil {
			l.Errorf("ParticipantLogic.Trans.InsertTx err: %v", er)
			return er
		}

		return nil
	})

	if err != nil {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "活动太过火爆，请重试!")
	}

	return nil
}

// updateParticipantCount 原子更新参与人数
func (l *ParticipantLogic) updateParticipantCount(ctx context.Context, tx *sql.Tx, activityId uint64, activity *model.VhLotteryActivity) (sql.Result, error) {
	activityUpdateBuilder := squirrel.Update(l.svcCtx.LotteryActivityModel.TableName()).
		Set("participant_count", squirrel.Expr("participant_count + ?", 1)).
		Where(squirrel.Eq{"id": activityId, "participant_count": activity.ParticipantCount})

	// 如果达到总人数，同时更新状态为等待开奖
	if activity.ParticipantCount+1 == activity.Total {
		activityUpdateBuilder = activityUpdateBuilder.Set("status", 2)
	}

	return l.svcCtx.LotteryActivityModel.UpdateCustomTx(ctx, tx, activityUpdateBuilder)
}
