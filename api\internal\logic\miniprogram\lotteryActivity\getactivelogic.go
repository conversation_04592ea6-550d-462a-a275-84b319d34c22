package lotteryActivity

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetActiveLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetActiveLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetActiveLogic {
	return &GetActiveLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetActiveLogic) GetActive() (resp *types.ActiveLotteryActivityResp, err error) {
	// 查询最近有效的抽奖活动
	where := squirrel.And{
		squirrel.GtOrEq{"status": 1},
		squirrel.LtOrEq{"start_time": time.Now()},
	}

	activity, err := l.svcCtx.LotteryActivityModel.FindOneByQuery(l.ctx,
		l.svcCtx.LotteryActivityModel.RowBuilder().Where(where).OrderBy("status asc,start_time desc"))

	if err != nil {
		if errors.Is(err, model.ErrNotFound) {
			// 没有找到活动，返回空响应
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Errorf("GetActiveLogic.LotteryActivityModel.FindOneByQuery err: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = &types.ActiveLotteryActivityResp{LotteryActivityInfo: types.LotteryActivityInfo{
		Id:               activity.Id,
		Title:            activity.Title,
		Describe:         activity.Describe,
		Status:           activity.Status,
		StartTime:        common.TimeToString(activity.StartTime),
		GoodsId:          activity.GoodsId,
		GoodsTitle:       activity.GoodsTitle,
		GoodsImg:         l.svcCtx.Config.ITEM.ALIURL + activity.GoodsImg,
		Total:            activity.Total,
		WinnerCount:      activity.WinnerCount,
		ParticipantCount: activity.ParticipantCount,
		CreateTime:       common.TimeToString(activity.CreateTime),
	}}

	return resp, nil
}
