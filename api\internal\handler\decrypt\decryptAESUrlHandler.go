package decrypt

import (
	"net/http"

	"engine/api/internal/logic/decrypt"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DecryptAESUrlHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AESDecryptUrlReq
		if err := httpx.Parse(r, &req); err != nil {
			// 参数错误也不暴露具体信息
			result.HttpResult(r, w, nil, xerr.NewErrMsg("操作失败"))
			return
		}

		l := decrypt.NewDecryptAESUrlLogic(r.Context(), svcCtx)
		resp, err := l.DecryptAESUrl(&req)
		if err != nil {
			// 解密失败，返回通用错误信息
			result.HttpResult(r, w, nil, err)
			return
		}

		// 返回解密后的URL字符串
		result.HttpResult(r, w, resp, nil)
	}
}
