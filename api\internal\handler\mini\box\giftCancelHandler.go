package box

import (
	"net/http"

	"engine/api/internal/logic/mini/box"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func GiftCancelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BoxGiftCancelReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := box.NewGiftCancelLogic(r.Context(), svcCtx)
		err := l.GiftCancel(&req)
		result.HttpResult(r, w, result.<PERSON>ull<PERSON>son{}, err)
	}
}
