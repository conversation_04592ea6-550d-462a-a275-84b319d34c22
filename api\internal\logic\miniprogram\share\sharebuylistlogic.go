package share

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShareBuyListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShareBuyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShareBuyListLogic {
	return &ShareBuyListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShareBuyListLogic) ShareBuyList(req *types.ShareBuyListReq) (resp *types.ShareBuyListResp, err error) {
	//uid := l.svcCtx.Jwt.GetUid(l.ctx)
	var (
		wait  errgroup.Group
		total int64
		list  []model.ShareBuyInfo
	)

	// 构建查询条件
	where := squirrel.And{squirrel.Eq{"share_id": req.Id}}

	// 获取总数
	wait.Go(func() error {
		ct, er := l.svcCtx.ShareCashBack.FindCount(l.ctx, model.CountBuilder("*", l.svcCtx.ShareCashBack.TableName()).Where(where))
		if er != nil {
			l.Errorf("ShareBuyListLogic.ShareCashBack.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		er := l.svcCtx.ShareCashBack.FindRows(l.ctx, squirrel.Select("u.nickname,u.avatar_image,c.money,c.create_time").
			From("vh_share_cashback c").
			LeftJoin("vh_user u on c.share_uid = u.id").
			Where(where).
			OrderBy("c.id ASC").
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit)), &list)
		if er != nil {
			l.Errorf("ShareBuyListLogic.ShareCashBack.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.ShareBuyListResp)
	resp.Total = total
	resp.List = make([]types.ShareBuyInfo, 0, len(list))
	for _, ls := range list {
		info := types.ShareBuyInfo{
			Nickname:    ls.Nickname,
			AvatarImage: ls.AvatarImage,
			Money:       ls.Money,
			CreateTime:  common.TimeToString(ls.CreateTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
