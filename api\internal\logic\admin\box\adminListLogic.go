package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminBoxListReq) (resp *types.AdminBoxListResp, err error) {
	// 构建查询条件
	builder := l.svcCtx.BoxModel.RowBuilder().Where(squirrel.Eq{"delete_time": 0})
	countBuilder := squirrel.Select("COUNT(*)").From(l.svcCtx.BoxModel.TableName()).Where(squirrel.Eq{"delete_time": 0})

	// 标题筛选
	if req.Title != "" {
		builder = builder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
		countBuilder = countBuilder.Where(squirrel.Like{"title": "%" + req.Title + "%"})
	}

	// 盲盒ID筛选
	if req.Id > 0 {
		builder = builder.Where(squirrel.Eq{"id": req.Id})
		countBuilder = countBuilder.Where(squirrel.Eq{"id": req.Id})
	}

	// 周期单位筛选
	if req.ValidTimeUnit != "" {
		builder = builder.Where(squirrel.Eq{"valid_time_unit": req.ValidTimeUnit})
		countBuilder = countBuilder.Where(squirrel.Eq{"valid_time_unit": req.ValidTimeUnit})
	}

	// 上架状态筛选
	if req.OnsaleStatus > 0 {
		builder = builder.Where(squirrel.Eq{"onsale_status": req.OnsaleStatus})
		countBuilder = countBuilder.Where(squirrel.Eq{"onsale_status": req.OnsaleStatus})
	}

	// 创建时间范围筛选
	if req.StartDate != "" {
		builder = builder.Where(squirrel.GtOrEq{"created_time": req.StartDate + " 00:00:00"})
		countBuilder = countBuilder.Where(squirrel.GtOrEq{"created_time": req.StartDate + " 00:00:00"})
	}
	if req.EndDate != "" {
		builder = builder.Where(squirrel.LtOrEq{"created_time": req.EndDate + " 23:59:59"})
		countBuilder = countBuilder.Where(squirrel.LtOrEq{"created_time": req.EndDate + " 23:59:59"})
	}

	// 排序和分页
	builder = builder.OrderBy("id DESC").Limit(uint64(req.Limit)).Offset(uint64((req.Page - 1) * req.Limit))

	// 查询总数
	total, err := l.svcCtx.BoxModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminList BoxModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询列表数据
	var boxes []*model.VhBox
	err = l.svcCtx.BoxModel.FindRows(l.ctx, builder, &boxes)
	if err != nil {
		l.Logger.Error("AdminList BoxModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据格式
	list := make([]types.BoxInfo, 0, len(boxes))
	for _, box := range boxes {
		// 处理图片URL
		avatarImage := function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, box.AvatarImage)

		list = append(list, types.BoxInfo{
			Id:            int64(box.Id),
			Title:         box.Title,
			AvatarImage:   avatarImage,
			ValidTimeUnit: box.ValidTimeUnit,
			ValidTimeNum:  int64(box.ValidTimeNum),
			Price:         box.Price,
			OnsaleStatus:  box.OnsaleStatus,
			CreateNum:     box.CreateNum,
			ActiveNum:     box.ActiveNum,
			GetNum:        box.GetNum,
			CreatedTime:   function.FormatTime(box.CreatedTime),
			UpdateTime:    function.FormatTime(box.UpdateTime),
		})
	}

	return &types.AdminBoxListResp{
		List:  list,
		Total: total,
	}, nil
}
