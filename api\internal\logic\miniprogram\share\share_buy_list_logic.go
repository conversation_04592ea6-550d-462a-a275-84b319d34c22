package share

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShareBuyListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShareBuyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShareBuyListLogic {
	return &ShareBuyListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShareBuyListLogic) ShareBuyList(req *types.ShareBuyListReq) (resp *types.ShareBuyListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
