package member

import (
	"context"
	"database/sql"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ApplyMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewApplyMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApplyMemberLogic {
	return &ApplyMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApplyMemberLogic) ApplyMember(req *types.ApplyMemberReq) (resp *types.ApplyMemberResp, err error) {
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	// 1. 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("用户不存在")
		}
		l.Logger.Errorf("ApplyMember UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 2. 检查用户是否已经是会员
	if user.Type >= 2 {
		return nil, xerr.NewErrMsg("您已经是会员，无需重复申请")
	}

	// 3. 检查是否已经申请过相同的订单号
	existingApplication, err := l.svcCtx.JoinMemberModel.FindByUidAndOrderNo(l.ctx, uid, req.OrderNo)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Errorf("ApplyMember FindByUidAndOrderNo error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if existingApplication != nil {
		return nil, xerr.NewErrMsg("该订单号已经申请过会员，请勿重复申请")
	}

	// 4. 开始事务处理
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		return l.processApplyMemberTransaction(ctx, tx, uid, req)
	})

	if err != nil {
		l.Logger.Errorf("ApplyMember transaction error: %v", err)
		return nil, xerr.NewErrMsg("申请失败，请稍后重试")
	}

	// 清除用户缓存
	err = l.svcCtx.UserModel.ClearCache(l.ctx, user)
	if err != nil {
		l.Logger.Errorf("ApplyMember clear cache error: %v", err)
		// 不返回错误，因为业务已经成功，只是缓存清除失败
	}

	return &types.ApplyMemberResp{
		Message: "申请成功，您已成为会员",
	}, nil
}

// processApplyMemberTransaction 处理申请会员事务
func (l *ApplyMemberLogic) processApplyMemberTransaction(ctx context.Context, tx *sql.Tx, uid int64, req *types.ApplyMemberReq) error {
	// 1. 插入会员申请记录
	insertJoinMemberQuery := `INSERT INTO vh_join_member (platform, order_no, phone, uid, status) VALUES (?, ?, ?, ?, ?)`
	_, err := tx.ExecContext(ctx, insertJoinMemberQuery, req.Platform, req.OrderNo, req.Phone, uid, 2) // status=2表示已通过
	if err != nil {
		l.Logger.Errorf("Insert join member record error: %v", err)
		return err
	}

	// 2. 升级用户类型为会员
	updateUserTypeQuery := `UPDATE vh_user SET type = 2 WHERE id = ? AND type = 1`
	result, err := tx.ExecContext(ctx, updateUserTypeQuery, uid)
	if err != nil {
		l.Logger.Errorf("Update user type error: %v", err)
		return err
	}

	affected, _ := result.RowsAffected()
	if affected == 0 {
		l.Logger.Infof("User type not updated, user may already be a member: uid=%d", uid)
		// 不返回错误，因为用户可能已经是会员了
	} else {
		l.Logger.Infof("User type upgraded to member: uid=%d", uid)

		// 清除用户缓存
		user, err := l.svcCtx.UserModel.FindOne(ctx, uid)
		if err == nil {
			clearErr := l.svcCtx.UserModel.ClearCache(ctx, user)
			if clearErr != nil {
				l.Logger.Errorf("Clear user cache error: %v", clearErr)
			}
		}
	}

	return nil
}
