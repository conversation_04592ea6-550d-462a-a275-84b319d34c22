package order

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderDetailPathLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrderDetailPathLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderDetailPathLogic {
	return &GetOrderDetailPathLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrderDetailPathLogic) GetOrderDetailPath() (resp *types.GetOrderDetailPathResp, err error) {
	// todo: add your logic here and delete this line

	return
}
