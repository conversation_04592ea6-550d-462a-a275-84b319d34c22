package goods

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetOnsaleStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetOnsaleStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetOnsaleStatusLogic {
	return &SetOnsaleStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetOnsaleStatusLogic) SetOnsaleStatus(req *types.GoodsSetOnsaleStatusReq) error {
	// 检查商品是否存在
	goods, err := l.svcCtx.GoodsModel.FindOne(l.ctx, uint64(req.Id))
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("SetOnsaleStatus GoodsModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 检查是否已被软删除
	if goods.DeleteTime > 0 {
		return xerr.NewErrMsg("商品已被删除")
	}

	// 如果状态没有变化，直接返回
	if goods.OnsaleStatus == req.OnsaleStatus {
		return nil
	}

	// 更新上下架状态和时间
	now := time.Now().Unix()
	updateBuilder := squirrel.Update(l.svcCtx.GoodsModel.TableName()).
		Set("onsale_status", req.OnsaleStatus).
		Where(squirrel.Eq{"id": req.Id})

	// 记录上下架时间
	if req.OnsaleStatus == 2 { // 上架
		updateBuilder = updateBuilder.Set("onsale_time", now)
	} else { // 下架
		updateBuilder = updateBuilder.Set("sold_out_time", now)
	}

	_, err = l.svcCtx.GoodsModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Error("SetOnsaleStatus GoodsModel.UpdateCustom error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
