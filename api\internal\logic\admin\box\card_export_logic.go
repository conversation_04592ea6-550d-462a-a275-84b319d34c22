package box

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CardExportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCardExportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CardExportLogic {
	return &CardExportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CardExportLogic) CardExport(req *types.BoxCardExportReq) error {
	// todo: add your logic here and delete this line

	return nil
}
