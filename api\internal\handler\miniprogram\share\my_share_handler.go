package share

import (
	"net/http"

	"engine/api/internal/logic/miniprogram/share"
	"engine/api/internal/svc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func MyShareHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := share.NewMyShareLogic(r.Context(), svcCtx)
		resp, err := l.MyShare()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
