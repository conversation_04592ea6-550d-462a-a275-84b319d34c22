syntax = "v1"

info (
    title:   "盲盒管理接口"
    desc:    "盲盒相关接口定义"
    author:  "system"
    version: "v1"
)

import "../common/common.api"

type (
    // 盲盒信息
    BoxInfo {
        Id              int64  `json:"id"`
        Title           string `json:"title"`
        AvatarImage     string `json:"avatar_image"`
        ValidTimeUnit   string `json:"valid_time_unit"`
        ValidTimeNum    int64  `json:"valid_time_num"`
        Price           float64 `json:"price"`
        OnsaleStatus    int64  `json:"onsale_status"`
        CreateNum       int64  `json:"create_num"`
        ActiveNum       int64  `json:"active_num"`
        GetNum          int64  `json:"get_num"`
        CreatedTime     string `json:"created_time"`
        UpdateTime      string `json:"update_time"`
    }

    // 盲盒详情信息
    BoxItemInfo {
        Name      string `json:"name"`
        ShortCode string `json:"short_code"`
        Num       int64  `json:"num"`
    }

    // 盲盒兑换请求
    BoxRedeemReq {
        EncryptedCardNo string `json:"encrypted_card_no" validate:"required" v:"加密的卡号"`
        Password        string `json:"password" validate:"required" v:"密码"`
    }

    // 盲盒兑换响应
    BoxRedeemResp {
        MainOrderNo string `json:"main_order_no"`
    }

    // 盲盒转赠请求
    BoxGiftReq {
        BoxMainOrderId int64 `json:"box_main_order_id" validate:"required" v:"盲盒主订单ID"`
    }

    // 盲盒转赠响应
    BoxGiftResp {
        ShareId        int64  `json:"share_id"`
        BoxTitle       string `json:"box_title"`
        BoxAvatarImage string `json:"box_avatar_image"`
        PeriodLabel    string `json:"period_label"`
        ExpireTime     string `json:"expire_time"`
        Status         int64  `json:"status"`
        SenderName     string `json:"sender_name"`
        ShareUrl       string `json:"share_url"`
    }

    // 转赠详情请求
    BoxGiftDetailReq {
        ShareId int64 `form:"share_id" validate:"required" v:"分享ID"`
    }

    // 转赠详情响应
    BoxGiftDetailResp {
        ShareId        int64  `json:"share_id"`
        BoxTitle       string `json:"box_title"`
        BoxAvatarImage string `json:"box_avatar_image"`
        PeriodLabel    string `json:"period_label"`
        ExpireTime     string `json:"expire_time"`
        Status         int64  `json:"status"`
        SenderName     string `json:"sender_name"`
    }

    // 接受转赠请求
    BoxGiftAcceptReq {
        ShareId   int64 `json:"share_id" validate:"required" v:"分享ID"`
        AddressId int64 `json:"address_id" validate:"required" v:"收货地址ID"`
    }

    // 接受转赠响应
    BoxGiftAcceptResp {
        OrderNo string `json:"order_no"`
        Message string `json:"message"`
    }

    // 拒绝转赠请求
    BoxGiftRejectReq {
        ShareId int64 `json:"share_id" validate:"required" v:"分享ID"`
    }

    // 取消转赠请求
    BoxGiftCancelReq {
        ShareId int64 `json:"share_id" validate:"required" v:"分享ID"`
    }

    // 盲盒项目
    BoxItem {
        Id        int64         `json:"id"`
        BoxId     int64         `json:"box_id"`
        Type      int64         `json:"type"`
        ItemsInfo []BoxItemInfo `json:"items_info"`
        ClaimedCount int64       `json:"claimed_count"` // 已领取人数
    }

    // 后台盲盒列表请求
    AdminBoxListReq {
        Page           int64  `form:"page,default=1" validate:"min=1" v:"页码"`
        Limit          int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
        Title          string `form:"title,optional" v:"盲盒名称"`
        Id             int64  `form:"id,optional" v:"盲盒ID"`
        ValidTimeUnit  string `form:"valid_time_unit,optional" validate:"omitempty,oneof=hour day week month year" v:"周期单位"`
        OnsaleStatus   int64  `form:"onsale_status,optional" validate:"omitempty,oneof=2 3" v:"上架状态"`
        StartDate      string `form:"start_date,optional" v:"开始日期"`
        EndDate        string `form:"end_date,optional" v:"结束日期"`
    }

    // 后台盲盒列表响应
    AdminBoxListResp {
        List  []BoxInfo `json:"list"`
        Total int64     `json:"total"`
    }

    // 盲盒详情请求
    BoxDetailReq {
        Id int64 `form:"id" validate:"required" v:"盲盒ID"`
    }

    // 盲盒详情响应
    BoxDetailResp {
        BoxInfo
        Items []BoxItem `json:"items"`
    }

    // 创建盲盒请求
    BoxCreateReq {
        Title         string `json:"title" validate:"required" v:"盲盒名称"`
        AvatarImage   string `json:"avatar_image" validate:"required" v:"列表图"`
        ValidTimeUnit string `json:"valid_time_unit" validate:"required,oneof=hour day week month year" v:"有效期单位"`
        ValidTimeNum  int64  `json:"valid_time_num" validate:"required,min=1" v:"有效期数量"`
        Price         float64 `json:"price,default=0" validate:"min=0" v:"售价"`
        OnsaleStatus  int64  `json:"onsale_status,default=3" validate:"oneof=2 3" v:"上架状态"`
    }

    // 更新盲盒请求
    BoxUpdateReq {
        Id          int64  `json:"id" validate:"required" v:"盲盒ID"`
        Title       string `json:"title" validate:"required" v:"盲盒名称"`
        AvatarImage string `json:"avatar_image" validate:"required" v:"列表图"`
    }

    // 删除盲盒请求
    BoxDeleteReq {
        Id int64 `json:"id" validate:"required" v:"盲盒ID"`
    }

    // 设置盲盒上下架状态请求
    BoxSetOnsaleStatusReq {
        Id           int64 `json:"id" validate:"required" v:"盲盒ID"`
        OnsaleStatus int64 `json:"onsale_status" validate:"required,oneof=2 3" v:"上架状态"`
    }

    // 绑定商品请求
    BoxBindGoodsReq {
        Id    int64           `json:"id" validate:"required" v:"盲盒ID"`
        Items []BoxItemUpdate `json:"items" validate:"required" v:"商品信息"`
    }

    // 盲盒项目更新
    BoxItemUpdate {
        Id        int64         `json:"id" validate:"required" v:"盲盒项目ID"`
        Type      int64         `json:"type" validate:"required,oneof=1 2" v:"类型"`
        ItemsInfo []BoxItemInfo `json:"items_info" validate:"required,min=1" v:"商品详情"`
    }

    // 生成礼品卡请求
    BoxGenerateCardsReq {
        BoxId int64 `json:"box_id" validate:"required" v:"盲盒ID"`
        Num   int64 `json:"num" validate:"required,min=1,max=1000" v:"生成数量"`
    }

    // 生成礼品卡响应
    BoxGenerateCardsResp {
        GeneratedNum int64 `json:"generated_num"`
    }

    // 作废礼品卡请求
    BoxRepealCardReq {
        CardNo string `json:"card_no" validate:"required" v:"卡号"`
        Remark string `json:"remark,optional" v:"作废备注"`
    }

    // 礼品卡信息
    BoxCardInfo {
        CardNo       string `json:"card_no"`
        Status       int64  `json:"status"`
        BoxId        int64  `json:"box_id"`
        BoxTitle     string `json:"box_title"`
        MainOrderNo  string `json:"main_order_no"`
        UseTime      string `json:"use_time"`
        RepealTime   string `json:"repeal_time"`
        RepealName   string `json:"repeal_name"`
        RepealRemark string `json:"repeal_remark"`
        CreatedTime  string `json:"created_time"`
    }

    // 礼品卡列表请求
    BoxCardListReq {
        Page     int64  `form:"page,default=1" validate:"min=1" v:"页码"`
        Limit    int64  `form:"limit,default=10" validate:"min=1,max=100" v:"每页数量"`
        BoxId    int64  `form:"box_id,optional" v:"盲盒ID"`
        CardNo   string `form:"card_no,optional" v:"卡号"`
        Status   int64  `form:"status,optional" validate:"omitempty,oneof=1 2 5" v:"状态"`
    }

    // 礼品卡列表响应
    BoxCardListResp {
        List  []BoxCardInfo `json:"list"`
        Total int64         `json:"total"`
    }

    // 礼品卡Excel导出请求
    BoxCardExportReq {
        BoxId      int64 `form:"box_id,optional" v:"盲盒ID"`
        Status     int64 `form:"status,optional" validate:"omitempty,oneof=1 2 5" v:"状态"`
        PrintCount int64 `form:"print_count,optional" v:"打印次数"`
    }
)

// 后台盲盒管理接口
@server(
    middleware: Global,Admin
    group: admin/box
    prefix: /mulandoGreateDestiny/v1/admin/box
    timeout: 3s
)
service mulandoGreateDestiny {
    // 盲盒列表
    @handler AdminList
    get /list (AdminBoxListReq) returns (AdminBoxListResp)

    // 盲盒详情
    @handler AdminDetail
    get /detail (BoxDetailReq) returns (BoxDetailResp)

    // 创建盲盒
    @handler AdminCreate
    post /create (BoxCreateReq)

    // 更新盲盒
    @handler AdminUpdate
    post /update (BoxUpdateReq)

    // 删除盲盒
    @handler AdminDelete
    post /delete (BoxDeleteReq)

    // 设置上下架状态
    @handler SetOnsaleStatus
    post /set_onsale_status (BoxSetOnsaleStatusReq)

    // 绑定商品
    @handler BindGoods
    post /bind_goods (BoxBindGoodsReq)

    // 生成礼品卡
    @handler GenerateCards
    post /generate_cards (BoxGenerateCardsReq) returns (BoxGenerateCardsResp)

    // 作废礼品卡
    @handler RepealCard
    post /repeal_card (BoxRepealCardReq)

    // 礼品卡列表
    @handler CardList
    get /card_list (BoxCardListReq) returns (BoxCardListResp)

    // 礼品卡Excel导出
    @handler CardExport
    get /card_export (BoxCardExportReq)
}

// 小程序盲盒接口（需要登录）
@server(
    middleware: Global,Auth
    group: mini/box
    prefix: /mulandoGreateDestiny/v1/mini/box
    timeout: 10s
)
service mulandoGreateDestiny {
    // 盲盒兑换
    @handler Redeem
    post /redeem (BoxRedeemReq) returns (BoxRedeemResp)

    // 盲盒转赠
    @handler Gift
    post /gift (BoxGiftReq) returns (BoxGiftResp)

    // 接受转赠
    @handler GiftAccept
    post /gift_accept (BoxGiftAcceptReq) returns (BoxGiftAcceptResp)

    // 拒绝转赠
    @handler GiftReject
    post /gift_reject (BoxGiftRejectReq)

    // 取消转赠
    @handler GiftCancel
    post /gift_cancel (BoxGiftCancelReq)
}

// 小程序盲盒接口（不强制登录）
@server(
    middleware: Global,ExistAuth
    group: mini/box
    prefix: /mulandoGreateDestiny/v1/mini/box
    timeout: 10s
)
service mulandoGreateDestiny {
    // 转赠详情（允许未登录用户查看）
    @handler GiftDetail
    get /gift_detail (BoxGiftDetailReq) returns (BoxGiftDetailResp)
}
