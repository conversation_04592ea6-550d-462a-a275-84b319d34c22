package box

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiftAcceptLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiftAcceptLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiftAcceptLogic {
	return &GiftAcceptLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GiftAcceptLogic) GiftAccept(req *types.BoxGiftAcceptReq) (resp *types.BoxGiftAcceptResp, err error) {
	// 获取用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		l.Logger.Error("uid not found in context")
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询转赠分享记录
	giftShare, err := l.svcCtx.BoxGiftShareModel.FindOne(l.ctx, req.ShareId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("转赠记录不存在")
		}
		l.Logger.Errorf("查询转赠记录失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证转赠状态
	if giftShare.Status != 1 {
		return nil, xerr.NewErrMsg("转赠已失效")
	}

	// 验证是否过期
	if time.Now().After(giftShare.ExpireTime) {
		return nil, xerr.NewErrMsg("转赠已过期")
	}

	// 验证不能接受自己的转赠
	if giftShare.SenderUid == uid {
		return nil, xerr.NewErrMsg("不能接受自己的转赠")
	}

	// 验证收货地址
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.AddressId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrMsg("收货地址不存在")
		}
		l.Logger.Errorf("查询收货地址失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 验证地址归属
	if address.Uid != uid {
		return nil, xerr.NewErrMsg("收货地址不属于当前用户")
	}

	// 查询盲盒主订单信息
	boxMainOrder, err := l.svcCtx.OrderMainBoxModel.FindOne(l.ctx, giftShare.BoxMainOrderId)
	if err != nil {
		l.Logger.Errorf("查询盲盒主订单失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 获取盲盒信息
	boxInfo, err := l.svcCtx.BoxModel.FindOne(l.ctx, uint64(giftShare.BoxId))
	if err != nil {
		l.Logger.Errorf("查询盲盒信息失败: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 生成订单号
	mainOrderNo := l.generateMainOrderNo()
	subOrderNo := l.generateSubOrderNo()

	// 创建盲盒快照
	snapshot, err := l.createBoxSnapshot(boxInfo, boxMainOrder.ItemsInfo)
	if err != nil {
		return nil, err
	}

	var orderNo string
	// 开启事务
	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		now := time.Now()
		nowTimestamp := now.Unix()

		// 1. 更新转赠记录状态为已接受
		updateGiftQuery := `UPDATE vh_box_gift_share SET status = 2, receiver_uid = ?, accept_time = ?, update_time = ? WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateGiftQuery, uid, now, now, req.ShareId)
		if err != nil {
			l.Logger.Errorf("更新转赠记录失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 2. 更新原盲盒状态为已赠送，并记录子订单号和领取时间
		updateBoxQuery := `UPDATE vh_order_main_box SET box_type = 3, order_no = ?, get_time = ? WHERE id = ?`
		_, err = tx.ExecContext(ctx, updateBoxQuery, subOrderNo, now, giftShare.BoxMainOrderId)
		if err != nil {
			l.Logger.Errorf("更新盲盒状态失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		// 3. 创建赠送主订单
		mainOrderQuery := `INSERT INTO vh_order_main (uid, main_order_no, main_order_status, payment_amount, payment_time, payment_method, deductible_amount, cash_amount, snapshot, share_id, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
		mainResult, err := tx.ExecContext(ctx, mainOrderQuery,
			uid, mainOrderNo, 1, 0.00, nowTimestamp, 0, 0.00, 0.00, snapshot, 0, 4) // type=4 赠送订单
		if err != nil {
			l.Logger.Errorf("创建主订单失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		mainOrderId, _ := mainResult.LastInsertId()

		// 4. 创建赠送子订单
		subOrderQuery := `INSERT INTO vh_order (snapshot, uid, sub_order_no, sub_order_status, main_order_id, goods_id, order_qty, payment_amount, cash_amount, erp_amount, deductible_amount, express_type, payment_time, province_id, province, city_id, city, district_id, district, address, consignee, consignee_phone, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
		_, err = tx.ExecContext(ctx, subOrderQuery,
			snapshot, uid, subOrderNo, 1, mainOrderId, giftShare.BoxId, 1, 0.00, 0.00, 0.00, 0.00, 0,
			nowTimestamp, address.ProvinceId, address.ProvinceName, address.CityId, address.CityName,
			address.TownId, address.TownName, address.Address, address.Consignee, address.ConsigneePhone, 4) // type=4 赠送订单
		if err != nil {
			l.Logger.Errorf("创建子订单失败: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}

		orderNo = subOrderNo
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.BoxGiftAcceptResp{
		OrderNo: orderNo,
		Message: "转赠接受成功",
	}, nil
}

// generateMainOrderNo 生成主订单号，确保唯一性
func (l *GiftAcceptLogic) generateMainOrderNo() string {
	// 最多尝试10次生成唯一订单号
	for i := 0; i < 10; i++ {
		mainOrderNo := function.GenerateMainOrderNo()

		// 检查订单号是否已存在
		_, err := l.svcCtx.OrderMainModel.FindOneByMainOrderNo(l.ctx, mainOrderNo)
		if err != nil && err == model.ErrNotFound {
			return mainOrderNo
		}
		time.Sleep(time.Millisecond)
	}

	// 如果10次都失败，使用时间戳确保唯一性
	now := time.Now()
	timestamp := now.UnixNano() / 1000000
	return function.GenerateOrderNo("MLDM" + now.Format("060102") + string(timestamp%1000000))
}

// generateSubOrderNo 生成子订单号
func (l *GiftAcceptLogic) generateSubOrderNo() string {
	return function.GenerateSubOrderNo()
}

// createBoxSnapshot 创建盲盒快照
func (l *GiftAcceptLogic) createBoxSnapshot(box *model.VhBox, itemsInfo string) (string, error) {
	// 解析商品信息
	var items []map[string]interface{}
	if itemsInfo != "" {
		if err := json.Unmarshal([]byte(itemsInfo), &items); err != nil {
			l.Logger.Errorf("解析商品信息失败: %v", err)
			items = []map[string]interface{}{}
		}
	}

	// 构建盲盒快照
	snapshot := map[string]interface{}{
		"avatar_image":      function.NormalizeImagePath(box.AvatarImage),
		"title":             box.Title,
		"brief":             "",
		"box_id":            box.Id,
		"valid_time_unit":   box.ValidTimeUnit,
		"valid_time_num":    box.ValidTimeNum,
		"price":             box.Price,
		"cashback_amount":   0.0,
		"erp_amount":        0.0,
		"deductible_amount": 0.0,
		"product_img":       function.NormalizeImagePath(box.AvatarImage),
		"detail":            "",
		"goods_id":          box.Id,
		"inventory":         0,
		"items_info":        items,
		"labels":            "",
		"snapshot_time":     function.FormatTime(time.Now()),
	}

	snapshotBytes, err := json.Marshal(snapshot)
	if err != nil {
		l.Logger.Errorf("创建盲盒快照失败: %v", err)
		return "", xerr.NewErrCode(xerr.DbError)
	}

	return string(snapshotBytes), nil
}
