package lotteryActivity

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type IsParticipantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewIsParticipantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *IsParticipantLogic {
	return &IsParticipantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *IsParticipantLogic) IsParticipant(req *types.ActiveLotteryActivityisParticipantReq) (resp *types.ActiveLotteryActivityisParticipantResp, err error) {
	resp = new(types.ActiveLotteryActivityisParticipantResp)
	uid := l.svcCtx.Jwt.GetUid(l.ctx)

	var (
		wait        errgroup.Group
		activity    *model.VhLotteryActivity
		participant *model.VhLotteryParticipant
	)

	//验证是否参与过活动
	wait.Go(func() error {
		info, er := l.svcCtx.LotteryParticipantModel.FindOneByQuery(l.ctx, l.svcCtx.LotteryParticipantModel.RowBuilder().Where(squirrel.Eq{"activity_id": req.Id, "uid": uid}))
		if er != nil && !errors.Is(er, model.ErrNotFound) {
			l.Logger.Error("IsParticipantLogic LotteryParticipantModel.FindOneByQuery error: %v", er)
			return er
		}
		participant = info
		return nil
	})

	wait.Go(func() error {
		info, er := l.svcCtx.LotteryActivityModel.FindOne(l.ctx, cast.ToUint64(req.Id))
		if er != nil && !errors.Is(er, model.ErrNotFound) {
			l.Logger.Error("IsParticipantLogic LotteryActivityModel.FindOne error: %v", er)
			return er
		}
		activity = info
		return nil
	})
	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if activity == nil {
		return nil, xerr.NewErrCodeMsg(xerr.DataNoExistError, "活动不存在")
	}

	if participant != nil {
		resp.IsParticipant = true
		if activity.Status == 3 {
			if participant.IsWinner != 0 {
				resp.IsWinner = 1
			} else {
				resp.IsWinner = 2
			}
		}
	}

	return
}
