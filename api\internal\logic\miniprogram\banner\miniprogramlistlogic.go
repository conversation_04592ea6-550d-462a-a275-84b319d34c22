package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniProgramListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniProgramListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniProgramListLogic {
	return &MiniProgramListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniProgramListLogic) MiniProgramList() (resp *types.MiniProgramBannerListResp, err error) {
	var banners []*model.Banner

	// 构建查询条件：状态启用
	builder := l.svcCtx.BannerModel.RowBuilder().
		Where(squirrel.Eq{"status": 2}).
		OrderBy("sort DESC, id DESC")

	// 查询数据
	err = l.svcCtx.BannerModel.FindRows(l.ctx, builder, &banners)
	if err != nil {
		l.Logger.Error("MiniProgramListLogic BannerModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.MiniProgramBannerListResp{
		List: make([]types.MiniProgramBannerInfo, 0),
	}
	
	for _, banner := range banners {
		resp.List = append(resp.List, types.MiniProgramBannerInfo{
			Id:        banner.Id,
			Name:      banner.Name,
			Picture:   function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, banner.Picture),
			JumpType:  banner.JumpType,
			JumpValue: banner.JumpValue,
		})
	}
	
	return
}
