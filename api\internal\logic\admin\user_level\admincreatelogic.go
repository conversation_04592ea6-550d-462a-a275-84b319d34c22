package user_level

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminCreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminCreateLogic {
	return &AdminCreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminCreateLogic) AdminCreate(req *types.UserLevelCreateReq) error {
	// 获取管理员信息 - 暂时不使用，因为新表结构中没有这些字段
	// adminUid := l.ctx.Value("admin_uid").(int64)
	// adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 检查等级是否已存在
	existLevel, err := l.svcCtx.UserLevelModel.FindOneByLevel(l.ctx, req.Level)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("AdminCreate UserLevelModel.FindOneByLevel error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}
	if existLevel != nil {
		return xerr.NewErrMsg("该等级已存在")
	}

	// 创建用户等级 - 使用正确的字段结构
	userLevel := &model.UserLevel{
		Level:            req.Level,
		Name:             req.Name,
		LevelName:        req.LevelName,
		ShareBrokerage:   req.ShareBrokerage,
		PaymentBrokerage: req.PaymentBrokerage,
		CashBrokerage:    req.CashBrokerage,
		CashMinAmount:    req.CashMinAmount,
		Duration:         req.Duration,
	}

	result, err := l.svcCtx.UserLevelModel.Insert(l.ctx, userLevel)
	if err != nil {
		l.Logger.Error("AdminCreate UserLevelModel.Insert error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	levelId, _ := result.LastInsertId()

	// 创建关联的任务 - 使用正确的字段结构
	for _, taskReq := range req.Tasks {
		task := &model.UserLevelTask{
			Level: req.Level,
			Name:  taskReq.Name,
			Type:  taskReq.Type,
			Num:   taskReq.Num,
		}

		_, err = l.svcCtx.UserLevelTaskModel.Insert(l.ctx, task)
		if err != nil {
			l.Logger.Error("AdminCreate UserLevelTaskModel.Insert error: %v", err)
			// 如果任务创建失败，删除已创建的等级
			_ = l.svcCtx.UserLevelModel.Delete(l.ctx, levelId)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
